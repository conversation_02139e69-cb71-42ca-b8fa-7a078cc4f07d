import fs from 'fs-extra';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';
import { SessionError } from '../utils/ErrorHandler';
import { Logger } from '../utils/Logger';
import { ConfigManager } from '../config/ConfigManager';
export class SessionManager {
    static instance;
    sessions;
    currentSession;
    sessionsDir;
    logger;
    configManager;
    constructor() {
        this.sessions = new Map();
        this.currentSession = null;
        this.sessionsDir = path.join(process.cwd(), '.ai-cli', 'sessions');
        this.logger = Logger.getInstance();
        this.configManager = ConfigManager.getInstance();
        this.initializeSessionsDirectory();
    }
    static getInstance() {
        if (!SessionManager.instance) {
            SessionManager.instance = new SessionManager();
        }
        return SessionManager.instance;
    }
    initializeSessionsDirectory() {
        try {
            fs.ensureDirSync(this.sessionsDir);
            this.loadExistingSessions();
        }
        catch (error) {
            this.logger.error('Failed to initialize sessions directory', {
                error: error.message,
                sessionsDir: this.sessionsDir
            });
        }
    }
    loadExistingSessions() {
        try {
            const sessionFiles = fs.readdirSync(this.sessionsDir)
                .filter(file => file.endsWith('.json'));
            for (const file of sessionFiles) {
                try {
                    const sessionPath = path.join(this.sessionsDir, file);
                    const sessionData = fs.readJsonSync(sessionPath);
                    const session = {
                        ...sessionData,
                        createdAt: new Date(sessionData.createdAt),
                        lastAccessedAt: new Date(sessionData.lastAccessedAt)
                    };
                    this.sessions.set(session.id, session);
                }
                catch (error) {
                    this.logger.warn(`Failed to load session file: ${file}`, {
                        error: error.message
                    });
                }
            }
            this.logger.info(`Loaded ${this.sessions.size} existing sessions`);
        }
        catch (error) {
            this.logger.error('Failed to load existing sessions', {
                error: error.message
            });
        }
    }
    async createSession(workingDirectory, context) {
        const sessionId = uuidv4();
        const now = new Date();
        const session = {
            id: sessionId,
            createdAt: now,
            lastAccessedAt: now,
            workingDirectory,
            context,
            conversationHistory: [],
            metadata: {
                provider: this.configManager.getAgentConfig().provider,
                model: this.configManager.getAgentConfig().model,
                createdBy: 'ai-cli',
                version: '1.0.0'
            }
        };
        this.sessions.set(sessionId, session);
        this.currentSession = session;
        if (this.configManager.getConfig().session.persistenceEnabled) {
            await this.saveSession(session);
        }
        this.logger.logSessionEvent('session_created', {
            sessionId,
            workingDirectory,
            provider: session.metadata.provider
        });
        return session;
    }
    async loadSession(sessionId) {
        let session = this.sessions.get(sessionId);
        if (!session) {
            // Try to load from disk
            const sessionPath = path.join(this.sessionsDir, `${sessionId}.json`);
            if (fs.existsSync(sessionPath)) {
                try {
                    const sessionData = fs.readJsonSync(sessionPath);
                    session = {
                        ...sessionData,
                        createdAt: new Date(sessionData.createdAt),
                        lastAccessedAt: new Date(sessionData.lastAccessedAt)
                    };
                    if (session) {
                        this.sessions.set(sessionId, session);
                    }
                }
                catch (error) {
                    throw new SessionError(`Failed to load session from disk: ${sessionId}`, {
                        sessionId,
                        error: error.message
                    });
                }
            }
        }
        if (!session) {
            throw new SessionError(`Session not found: ${sessionId}`, { sessionId });
        }
        // Update last accessed time
        session.lastAccessedAt = new Date();
        this.currentSession = session;
        if (this.configManager.getConfig().session.persistenceEnabled) {
            await this.saveSession(session);
        }
        this.logger.logSessionEvent('session_loaded', { sessionId });
        return session;
    }
    getCurrentSession() {
        return this.currentSession;
    }
    async saveSession(session) {
        if (!this.configManager.getConfig().session.persistenceEnabled) {
            return;
        }
        try {
            const sessionPath = path.join(this.sessionsDir, `${session.id}.json`);
            await fs.writeJson(sessionPath, session, { spaces: 2 });
            this.logger.debug('Session saved', { sessionId: session.id });
        }
        catch (error) {
            this.logger.error('Failed to save session', {
                sessionId: session.id,
                error: error.message
            });
            throw new SessionError(`Failed to save session: ${session.id}`, {
                sessionId: session.id,
                error: error.message
            });
        }
    }
    async deleteSession(sessionId) {
        const session = this.sessions.get(sessionId);
        if (!session) {
            return false;
        }
        // Remove from memory
        this.sessions.delete(sessionId);
        // Clear current session if it's the one being deleted
        if (this.currentSession?.id === sessionId) {
            this.currentSession = null;
        }
        // Remove from disk
        try {
            const sessionPath = path.join(this.sessionsDir, `${sessionId}.json`);
            if (fs.existsSync(sessionPath)) {
                await fs.remove(sessionPath);
            }
        }
        catch (error) {
            this.logger.error('Failed to delete session file', {
                sessionId,
                error: error.message
            });
        }
        this.logger.logSessionEvent('session_deleted', { sessionId });
        return true;
    }
    listSessions() {
        return Array.from(this.sessions.values())
            .sort((a, b) => b.lastAccessedAt.getTime() - a.lastAccessedAt.getTime());
    }
    addMessage(message) {
        if (!this.currentSession) {
            throw new SessionError('No active session to add message to');
        }
        this.currentSession.conversationHistory.push(message);
        this.currentSession.lastAccessedAt = new Date();
        // Save session if persistence is enabled
        if (this.configManager.getConfig().session.persistenceEnabled) {
            this.saveSession(this.currentSession).catch(error => {
                this.logger.error('Failed to save session after adding message', {
                    sessionId: this.currentSession?.id,
                    error: error.message
                });
            });
        }
    }
    getConversationHistory() {
        return this.currentSession?.conversationHistory || [];
    }
    clearConversationHistory() {
        if (this.currentSession) {
            this.currentSession.conversationHistory = [];
            this.currentSession.lastAccessedAt = new Date();
            this.logger.logSessionEvent('conversation_cleared', {
                sessionId: this.currentSession.id
            });
        }
    }
    async cleanupExpiredSessions() {
        const config = this.configManager.getConfig().session;
        if (!config.autoCleanup) {
            return 0;
        }
        const now = Date.now();
        const expiredSessions = [];
        for (const [sessionId, session] of this.sessions) {
            const timeSinceLastAccess = now - session.lastAccessedAt.getTime();
            if (timeSinceLastAccess > config.sessionTimeout) {
                expiredSessions.push(sessionId);
            }
        }
        // Keep only the most recent sessions if we exceed the limit
        const allSessions = this.listSessions();
        if (allSessions.length > config.maxSessions) {
            const sessionsToRemove = allSessions.slice(config.maxSessions);
            for (const session of sessionsToRemove) {
                if (!expiredSessions.includes(session.id)) {
                    expiredSessions.push(session.id);
                }
            }
        }
        // Delete expired sessions
        for (const sessionId of expiredSessions) {
            await this.deleteSession(sessionId);
        }
        if (expiredSessions.length > 0) {
            this.logger.info(`Cleaned up ${expiredSessions.length} expired sessions`);
        }
        return expiredSessions.length;
    }
    getSessionStats() {
        const sessions = this.listSessions();
        const now = Date.now();
        const activeThreshold = 24 * 60 * 60 * 1000; // 24 hours
        const activeSessions = sessions.filter(session => now - session.lastAccessedAt.getTime() < activeThreshold).length;
        const totalMessages = sessions.reduce((total, session) => total + session.conversationHistory.length, 0);
        return {
            totalSessions: sessions.length,
            activeSessions,
            oldestSession: sessions.length > 0 ? sessions[sessions.length - 1].createdAt : null,
            newestSession: sessions.length > 0 ? sessions[0].createdAt : null,
            totalMessages
        };
    }
    async exportSession(sessionId, filePath) {
        const session = this.sessions.get(sessionId);
        if (!session) {
            throw new SessionError(`Session not found: ${sessionId}`, { sessionId });
        }
        try {
            await fs.writeJson(filePath, session, { spaces: 2 });
            this.logger.info(`Session exported: ${sessionId}`, { sessionId, filePath });
        }
        catch (error) {
            throw new SessionError(`Failed to export session: ${sessionId}`, {
                sessionId,
                filePath,
                error: error.message
            });
        }
    }
    async importSession(filePath) {
        try {
            const sessionData = await fs.readJson(filePath);
            const session = {
                ...sessionData,
                createdAt: new Date(sessionData.createdAt),
                lastAccessedAt: new Date(sessionData.lastAccessedAt)
            };
            // Generate new ID to avoid conflicts
            session.id = uuidv4();
            session.lastAccessedAt = new Date();
            this.sessions.set(session.id, session);
            if (this.configManager.getConfig().session.persistenceEnabled) {
                await this.saveSession(session);
            }
            this.logger.info(`Session imported: ${session.id}`, {
                sessionId: session.id,
                filePath
            });
            return session;
        }
        catch (error) {
            throw new SessionError(`Failed to import session from: ${filePath}`, {
                filePath,
                error: error.message
            });
        }
    }
    async backupSessions() {
        try {
            const backupData = {
                timestamp: new Date().toISOString(),
                sessions: Array.from(this.sessions.entries()).map(([id, session]) => ({
                    id,
                    session: {
                        ...session,
                        // Serialize dates
                        createdAt: session.createdAt.toISOString(),
                        lastAccessedAt: session.lastAccessedAt.toISOString()
                    }
                }))
            };
            const backupPath = path.join(this.sessionsDir, `backup-${Date.now()}.json`);
            await fs.writeFile(backupPath, JSON.stringify(backupData, null, 2));
            this.logger.info(`Sessions backed up to: ${backupPath}`);
        }
        catch (error) {
            this.logger.error('Failed to backup sessions', {
                error: error.message
            });
        }
    }
    async restoreFromBackup(backupPath) {
        try {
            const backupData = JSON.parse(await fs.readFile(backupPath, 'utf8'));
            for (const { id, session } of backupData.sessions) {
                // Deserialize dates
                session.createdAt = new Date(session.createdAt);
                session.lastAccessedAt = new Date(session.lastAccessedAt);
                this.sessions.set(id, session);
                // Save to persistent storage
                const sessionPath = path.join(this.sessionsDir, `${id}.json`);
                await fs.writeFile(sessionPath, JSON.stringify(session, null, 2));
            }
            this.logger.info(`Restored ${backupData.sessions.length} sessions from backup`);
        }
        catch (error) {
            this.logger.error('Failed to restore from backup', {
                error: error.message,
                backupPath
            });
            throw error;
        }
    }
    async cleanupOldSessions(maxAgeHours = 168) {
        let cleanedCount = 0;
        const cutoffTime = Date.now() - (maxAgeHours * 60 * 60 * 1000);
        try {
            for (const [sessionId, session] of this.sessions.entries()) {
                if (session.lastAccessedAt.getTime() < cutoffTime) {
                    await this.deleteSession(sessionId);
                    cleanedCount++;
                }
            }
            this.logger.info(`Cleaned up ${cleanedCount} old sessions`);
            return cleanedCount;
        }
        catch (error) {
            this.logger.error('Failed to cleanup old sessions', {
                error: error.message
            });
            return cleanedCount;
        }
    }
    async getSessionMetrics() {
        const stats = this.getSessionStats();
        const sessions = Array.from(this.sessions.values());
        const averageMessagesPerSession = sessions.length > 0 ?
            stats.totalMessages / sessions.length : 0;
        const oldestSession = sessions.length > 0 ?
            new Date(Math.min(...sessions.map(s => s.createdAt.getTime()))) : null;
        const newestSession = sessions.length > 0 ?
            new Date(Math.max(...sessions.map(s => s.createdAt.getTime()))) : null;
        // Calculate storage size
        let totalStorageSize = 0;
        try {
            const files = await fs.readdir(this.sessionsDir);
            for (const file of files) {
                if (file.endsWith('.json')) {
                    const filePath = path.join(this.sessionsDir, file);
                    const stat = await fs.stat(filePath);
                    totalStorageSize += stat.size;
                }
            }
        }
        catch (error) {
            this.logger.warn('Failed to calculate storage size', {
                error: error.message
            });
        }
        return {
            ...stats,
            averageMessagesPerSession,
            oldestSession,
            newestSession,
            totalStorageSize
        };
    }
}
//# sourceMappingURL=SessionManager.js.map