import { GitError } from './git-error';
/**
 * The `TaskConfigurationError` is thrown when a command was incorrectly
 * configured. An error of this kind means that no attempt was made to
 * run your command through the underlying `git` binary.
 *
 * Check the `.message` property for more detail on why your configuration
 * resulted in an error.
 */
export declare class TaskConfigurationError extends GitError {
    constructor(message?: string);
}
