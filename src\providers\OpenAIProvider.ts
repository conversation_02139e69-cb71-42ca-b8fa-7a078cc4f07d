import OpenAI from 'openai';
import { <PERSON><PERSON><PERSON>ider, LLMO<PERSON>s, LLMResponse, ToolCall, ToolResult, AgentContext } from '../types';
import { LLMProviderError } from '../utils/ErrorHandler';
import { Logger } from '../utils/Logger';
import { ToolRegistry } from '../tools/ToolRegistry';

export class OpenAIProvider implements LLMProvider {
  public readonly name = 'openai';
  private client: OpenAI;
  private logger: Logger;
  private toolRegistry: ToolRegistry;

  constructor(config: Record<string, unknown>) {
    this.logger = Logger.getInstance();
    this.toolRegistry = ToolRegistry.getInstance();

    if (!config.apiKey) {
      throw new LLMProviderError(this.name, 'OpenAI API key is required');
    }

    this.client = new OpenAI({
      apiKey: config.apiKey as string,
      baseURL: config.baseURL as string,
      organization: config.organization as string
    });
  }

  public async generateResponse(prompt: string, options: LLMOptions = {}): Promise<LLMResponse> {
    try {
      const messages = this.buildMessages(prompt, options);
      const model = options.model || 'gpt-4';

      const requestParams: OpenAI.Chat.ChatCompletionCreateParams = {
        model,
        messages,
        temperature: options.temperature || 0.7,
        max_tokens: options.maxTokens || 4000
      };

      // Add tools if available and enabled
      if (options.tools && options.tools.length > 0) {
        requestParams.tools = options.tools.map(tool => ({
          type: 'function',
          function: {
            name: tool.name,
            description: tool.description,
            parameters: tool.parameters
          }
        }));
        requestParams.tool_choice = 'auto';
      }

      this.logger.debug('Making OpenAI API request', {
        model,
        messageCount: messages.length,
        hasTools: !!requestParams.tools,
        toolCount: requestParams.tools?.length || 0
      });

      const response = await this.client.chat.completions.create(requestParams);
      const choice = response.choices[0];

      if (!choice) {
        throw new LLMProviderError(this.name, 'No response choice returned from OpenAI');
      }

      const content = choice.message.content || '';
      const toolCalls = choice.message.tool_calls?.map(tc => ({
        id: tc.id,
        name: tc.function.name,
        arguments: JSON.parse(tc.function.arguments)
      })) || [];

      const llmResponse: LLMResponse = {
        content,
        toolCalls,
        usage: response.usage ? {
          promptTokens: response.usage.prompt_tokens,
          completionTokens: response.usage.completion_tokens,
          totalTokens: response.usage.total_tokens
        } : undefined
      };

      this.logger.logLLMCall(this.name, model, prompt, content, llmResponse.usage);

      return llmResponse;

    } catch (error) {
      this.logger.error('OpenAI API request failed', {
        error: (error as Error).message,
        prompt: `${prompt.substring(0, 100)  }...`
      });

      if (error instanceof OpenAI.APIError) {
        throw new LLMProviderError(
          this.name,
          `OpenAI API error: ${error.message}`,
          { status: error.status, type: error.type }
        );
      }

      throw new LLMProviderError(this.name, (error as Error).message);
    }
  }

  public async *generateStreamResponse(prompt: string, options: LLMOptions = {}): AsyncGenerator<string, void, unknown> {
    try {
      const messages = this.buildMessages(prompt, options);
      const model = options.model || 'gpt-4';

      const requestParams: OpenAI.Chat.ChatCompletionCreateParams = {
        model,
        messages,
        temperature: options.temperature || 0.7,
        max_tokens: options.maxTokens || 4000,
        stream: true
      };

      // Add tools if available and enabled
      if (options.tools && options.tools.length > 0) {
        requestParams.tools = options.tools.map(tool => ({
          type: 'function',
          function: {
            name: tool.name,
            description: tool.description,
            parameters: tool.parameters
          }
        }));
        requestParams.tool_choice = 'auto';
      }

      this.logger.debug('Making OpenAI streaming API request', {
        model,
        messageCount: messages.length,
        hasTools: !!requestParams.tools
      });

      const stream = await this.client.chat.completions.create(requestParams);

      for await (const chunk of stream) {
        const choice = chunk.choices[0];
        if (choice?.delta?.content) {
          yield choice.delta.content;
        }
      }

    } catch (error) {
      this.logger.error('OpenAI streaming API request failed', {
        error: (error as Error).message,
        prompt: `${prompt.substring(0, 100)  }...`
      });

      if (error instanceof OpenAI.APIError) {
        throw new LLMProviderError(
          this.name,
          `OpenAI streaming API error: ${error.message}`,
          { status: error.status, type: error.type }
        );
      }

      throw new LLMProviderError(this.name, (error as Error).message);
    }
  }

  public supportsToolCalling(): boolean {
    return true;
  }

  public async callTool(toolCall: ToolCall, context: AgentContext): Promise<ToolResult> {
    try {
      return await this.toolRegistry.executeTool(toolCall, context);
    } catch (error) {
      this.logger.error('Tool execution failed in OpenAI provider', {
        toolName: toolCall.name,
        error: (error as Error).message
      });
      throw error;
    }
  }

  private buildMessages(prompt: string, options: LLMOptions): OpenAI.Chat.ChatCompletionMessageParam[] {
    const messages: OpenAI.Chat.ChatCompletionMessageParam[] = [];

    // Add system message if provided
    if (options.systemPrompt) {
      messages.push({
        role: 'system',
        content: options.systemPrompt
      });
    }

    // Add conversation history if provided
    if (options.conversationHistory) {
      for (const msg of options.conversationHistory) {
        if (msg.role === 'tool') {
          messages.push({
            role: 'tool',
            content: msg.content,
            tool_call_id: msg.toolCallId!
          });
        } else {
          messages.push({
            role: msg.role as 'system' | 'user' | 'assistant',
            content: msg.content
          });
        }
      }
    }

    // Add current prompt
    messages.push({
      role: 'user',
      content: prompt
    });

    return messages;
  }

  public async validateApiKey(): Promise<boolean> {
    try {
      await this.client.models.list();
      return true;
    } catch (error) {
      this.logger.error('OpenAI API key validation failed', {
        error: (error as Error).message
      });
      return false;
    }
  }

  public async getAvailableModels(): Promise<string[]> {
    try {
      const response = await this.client.models.list();
      return response.data
        .filter(model => model.id.includes('gpt'))
        .map(model => model.id)
        .sort();
    } catch (error) {
      this.logger.error('Failed to fetch OpenAI models', {
        error: (error as Error).message
      });
      return ['gpt-4', 'gpt-4-turbo', 'gpt-3.5-turbo'];
    }
  }

  public getDefaultModel(): string {
    return 'gpt-4';
  }

  public getMaxTokens(model?: string): number {
    const tokenLimits: Record<string, number> = {
      'gpt-4': 8192,
      'gpt-4-turbo': 128000,
      'gpt-4o': 128000,
      'gpt-4o-mini': 128000,
      'gpt-3.5-turbo': 16385
    };

    return tokenLimits[model || 'gpt-4'] || 8192;
  }

  public estimateTokens(text: string): number {
    // Rough estimation: 1 token ≈ 4 characters for English text
    return Math.ceil(text.length / 4);
  }

  public calculateCost(usage: { promptTokens: number; completionTokens: number }, model?: string): number {
    // Pricing as of 2024 (in USD per 1K tokens)
    const pricing: Record<string, { input: number; output: number }> = {
      'gpt-4': { input: 0.03, output: 0.06 },
      'gpt-4-turbo': { input: 0.01, output: 0.03 },
      'gpt-4o': { input: 0.005, output: 0.015 },
      'gpt-4o-mini': { input: 0.00015, output: 0.0006 },
      'gpt-3.5-turbo': { input: 0.0005, output: 0.0015 }
    };

    const modelPricing = pricing[model || 'gpt-4'] || pricing['gpt-4'];
    const inputCost = (usage.promptTokens / 1000) * modelPricing.input;
    const outputCost = (usage.completionTokens / 1000) * modelPricing.output;

    return inputCost + outputCost;
  }
}
