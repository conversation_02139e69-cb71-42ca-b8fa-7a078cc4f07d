import { AgentOrchestrator } from '../../src/agents/AgentOrchestrator';
import { ConfigManager } from '../../src/config/ConfigManager';
import { SessionManager } from '../../src/session/SessionManager';
import { ToolRegistry } from '../../src/tools/ToolRegistry';
import { ContextEngine } from '../../src/context/ContextEngine';
import path from 'path';
import fs from 'fs-extra';

describe('Basic Functionality Integration Tests', () => {
  let agentOrchestrator: AgentOrchestrator;
  let configManager: ConfigManager;
  let sessionManager: SessionManager;
  let toolRegistry: ToolRegistry;
  let contextEngine: ContextEngine;
  let testDir: string;

  beforeAll(async () => {
    // Create test directory
    testDir = path.join(__dirname, 'test-workspace');
    await fs.ensureDir(testDir);
    
    // Initialize components
    configManager = ConfigManager.getInstance();
    sessionManager = SessionManager.getInstance();
    toolRegistry = ToolRegistry.getInstance();
    contextEngine = ContextEngine.getInstance();
    agentOrchestrator = AgentOrchestrator.getInstance();

    // Set up basic configuration for testing
    configManager.updateAgentConfig({
      provider: 'openai',
      model: 'gpt-3.5-turbo',
      maxTokens: 1000,
      temperature: 0.1
    });
  });

  afterAll(async () => {
    // Cleanup
    await fs.remove(testDir);
    agentOrchestrator.cleanup();
  });

  describe('Component Initialization', () => {
    test('should initialize all core components', () => {
      expect(configManager).toBeDefined();
      expect(sessionManager).toBeDefined();
      expect(toolRegistry).toBeDefined();
      expect(contextEngine).toBeDefined();
      expect(agentOrchestrator).toBeDefined();
    });

    test('should have singleton instances', () => {
      expect(ConfigManager.getInstance()).toBe(configManager);
      expect(SessionManager.getInstance()).toBe(sessionManager);
      expect(ToolRegistry.getInstance()).toBe(toolRegistry);
      expect(ContextEngine.getInstance()).toBe(contextEngine);
      expect(AgentOrchestrator.getInstance()).toBe(agentOrchestrator);
    });
  });

  describe('Tool Registry', () => {
    test('should have registered tools', () => {
      const tools = toolRegistry.getAllTools();
      expect(tools.length).toBeGreaterThan(0);
      
      const toolNames = tools.map(tool => tool.name);
      expect(toolNames).toContain('file');
      expect(toolNames).toContain('shell');
    });

    test('should get tool by name', () => {
      const fileTool = toolRegistry.getTool('file');
      expect(fileTool).toBeDefined();
      expect(fileTool?.name).toBe('file');
    });
  });

  describe('File Tool Operations', () => {
    test('should create and read files', async () => {
      const testFile = path.join(testDir, 'test.txt');
      const testContent = 'Hello, World!';

      // Create context for tool execution
      const context = await contextEngine.createAgentContext('test-session', testDir);

      // Write file
      const writeResult = await toolRegistry.executeTool({
        name: 'file',
        id: 'test-write',
        arguments: {
          operation: 'write',
          path: 'test.txt',
          content: testContent
        }
      }, context);

      expect(writeResult.success).toBe(true);
      expect(await fs.pathExists(testFile)).toBe(true);

      // Read file
      const readResult = await toolRegistry.executeTool({
        name: 'file',
        id: 'test-read',
        arguments: {
          operation: 'read',
          path: 'test.txt'
        }
      }, context);

      expect(readResult.success).toBe(true);
      expect((readResult.result as { metadata: { content: string } }).metadata.content).toBe(testContent);
    });

    test('should handle file operations with options', async () => {
      const context = await contextEngine.createAgentContext('test-session-2', testDir);

      // Test file exists check
      const existsResult = await toolRegistry.executeTool({
        name: 'file',
        id: 'test-exists',
        arguments: {
          operation: 'exists',
          path: 'test.txt'
        }
      }, context);

      expect(existsResult.success).toBe(true);
      expect((existsResult.result as { metadata: { exists: boolean } }).metadata.exists).toBe(true);
    });
  });

  describe('Context Engine', () => {
    test('should create agent context', async () => {
      const context = await contextEngine.createAgentContext('test-context', testDir);
      
      expect(context).toBeDefined();
      expect(context.sessionId).toBe('test-context');
      expect(context.workingDirectory).toBe(testDir);
      expect(context.projectContext).toBeDefined();
      expect(context.availableTools.length).toBeGreaterThan(0);
    });

    test('should get project context', () => {
      const projectContext = contextEngine.getProjectContext(testDir);
      expect(projectContext).toBeDefined();
      expect(projectContext?.rootPath).toBe(testDir);
    });
  });

  describe('Session Management', () => {
    test('should create and manage sessions', async () => {
      const context = await contextEngine.createAgentContext('session-test', testDir);
      const session = await sessionManager.createSession(testDir, context);

      expect(session).toBeDefined();
      expect(session.id).toBeDefined();
      expect(session.workingDirectory).toBe(testDir);
      expect(session.conversationHistory).toEqual([]);
    });

    test('should list sessions', () => {
      const sessions = sessionManager.listSessions();
      expect(Array.isArray(sessions)).toBe(true);
    });
  });

  describe('Configuration Management', () => {
    test('should manage configuration', () => {
      const config = configManager.getAgentConfig();
      expect(config).toBeDefined();
      expect(config.provider).toBe('openai');
      expect(config.model).toBe('gpt-3.5-turbo');
    });

    test('should update configuration', () => {
      configManager.updateAgentConfig({ temperature: 0.5 });
      const config = configManager.getAgentConfig();
      expect(config.temperature).toBe(0.5);
    });
  });

  describe('Enhanced Tool Chain Execution', () => {
    test('should execute tool chain with enhanced features', async () => {
      const context = await contextEngine.createAgentContext('chain-test', testDir);
      
      const toolCalls = [
        {
          name: 'file',
          id: 'chain-1',
          arguments: {
            operation: 'write',
            path: 'chain-test.txt',
            content: 'Chain test content'
          }
        },
        {
          name: 'file',
          id: 'chain-2',
          arguments: {
            operation: 'read',
            path: 'chain-test.txt'
          }
        }
      ];

      const results = await toolRegistry.executeToolChain(toolCalls, context, {
        enableLearning: true,
        adaptiveExecution: true,
        maxParallel: 2
      });

      expect(results).toHaveLength(2);
      expect(results.every(r => r.success)).toBe(true);
    });
  });
});
