import { Logger } from '../utils/Logger';
import { ConfigManager } from '../config/ConfigManager';
describe('Basic functionality tests', () => {
    test('Logger should initialize correctly', () => {
        const logger = Logger.getInstance();
        expect(logger).toBeDefined();
    });
    test('ConfigManager should initialize correctly', () => {
        const configManager = ConfigManager.getInstance();
        expect(configManager).toBeDefined();
        const config = configManager.getConfig();
        expect(config).toBeDefined();
        expect(config.agent).toBeDefined();
        expect(config.context).toBeDefined();
        expect(config.providers).toBeDefined();
    });
    test('Should have correct project structure', () => {
        expect(true).toBe(true); // Basic test to ensure Jest is working
    });
});
//# sourceMappingURL=basic.test.js.map