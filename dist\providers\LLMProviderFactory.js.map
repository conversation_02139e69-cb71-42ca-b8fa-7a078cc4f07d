{"version": 3, "file": "LLMProviderFactory.js", "sourceRoot": "", "sources": ["../../src/providers/LLMProviderFactory.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,gBAAgB,EAAE,kBAAkB,EAAE,MAAM,uBAAuB,CAAC;AAC7E,OAAO,EAAE,MAAM,EAAE,MAAM,iBAAiB,CAAC;AACzC,OAAO,EAAE,aAAa,EAAE,MAAM,yBAAyB,CAAC;AACxD,OAAO,EAAE,cAAc,EAAE,MAAM,kBAAkB,CAAC;AAClD,OAAO,EAAE,iBAAiB,EAAE,MAAM,qBAAqB,CAAC;AACxD,OAAO,EAAE,gBAAgB,EAAE,MAAM,oBAAoB,CAAC;AACtD,OAAO,EAAE,cAAc,EAAE,MAAM,kBAAkB,CAAC;AAClD,OAAO,EAAE,cAAc,EAAE,MAAM,kBAAkB,CAAC;AAClD,OAAO,EAAE,eAAe,EAAE,MAAM,mBAAmB,CAAC;AAEpD,MAAM,OAAO,kBAAkB;IACrB,MAAM,CAAC,QAAQ,CAAqB;IACpC,SAAS,CAA2B;IACpC,MAAM,CAAS;IACf,aAAa,CAAgB;IAErC;QACE,IAAI,CAAC,SAAS,GAAG,IAAI,GAAG,EAAE,CAAC;QAC3B,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,WAAW,EAAE,CAAC;QACnC,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC,WAAW,EAAE,CAAC;IACnD,CAAC;IAEM,MAAM,CAAC,WAAW;QACvB,IAAI,CAAC,kBAAkB,CAAC,QAAQ,EAAE,CAAC;YACjC,kBAAkB,CAAC,QAAQ,GAAG,IAAI,kBAAkB,EAAE,CAAC;QACzD,CAAC;QACD,OAAO,kBAAkB,CAAC,QAAQ,CAAC;IACrC,CAAC;IAEM,KAAK,CAAC,cAAc,CAAC,YAAoB;QAC9C,sCAAsC;QACtC,IAAI,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,YAAY,CAAC,EAAE,CAAC;YACrC,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,YAAY,CAAE,CAAC;QAC3C,CAAC;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;QAClE,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,MAAM,IAAI,kBAAkB,CAAC,wCAAwC,YAAY,EAAE,CAAC,CAAC;QACvF,CAAC;QAED,IAAI,QAAqB,CAAC;QAE1B,IAAI,CAAC;YACH,QAAQ,YAAY,CAAC,WAAW,EAAE,EAAE,CAAC;gBACnC,KAAK,QAAQ;oBACX,QAAQ,GAAG,IAAI,cAAc,CAAC,MAAM,CAAC,CAAC;oBACtC,MAAM;gBACR,KAAK,WAAW;oBACd,QAAQ,GAAG,IAAI,iBAAiB,CAAC,MAAM,CAAC,CAAC;oBACzC,MAAM;gBACR,KAAK,UAAU;oBACb,QAAQ,GAAG,IAAI,gBAAgB,CAAC,MAAM,CAAC,CAAC;oBACxC,MAAM;gBACR,KAAK,QAAQ;oBACX,QAAQ,GAAG,IAAI,cAAc,CAAC,MAAM,CAAC,CAAC;oBACtC,MAAM;gBACR,KAAK,QAAQ;oBACX,QAAQ,GAAG,IAAI,cAAc,CAAC,MAAM,CAAC,CAAC;oBACtC,MAAM;gBACR,KAAK,SAAS;oBACZ,QAAQ,GAAG,IAAI,eAAe,CAAC,MAAM,CAAC,CAAC;oBACvC,MAAM;gBACR;oBACE,MAAM,IAAI,gBAAgB,CAAC,YAAY,EAAE,yBAAyB,YAAY,EAAE,CAAC,CAAC;YACtF,CAAC;YAED,+BAA+B;YAC/B,MAAM,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;YAElC,qBAAqB;YACrB,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC;YAE3C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,oCAAoC,YAAY,EAAE,CAAC,CAAC;YACrE,OAAO,QAAQ,CAAC;QAElB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,kCAAkC,YAAY,EAAE,EAAE;gBAClE,KAAK,EAAG,KAAe,CAAC,OAAO;gBAC/B,QAAQ,EAAE,YAAY;aACvB,CAAC,CAAC;YAEH,IAAI,KAAK,YAAY,gBAAgB,IAAI,KAAK,YAAY,kBAAkB,EAAE,CAAC;gBAC7E,MAAM,KAAK,CAAC;YACd,CAAC;YAED,MAAM,IAAI,gBAAgB,CACxB,YAAY,EACZ,kCAAmC,KAAe,CAAC,OAAO,EAAE,CAC7D,CAAC;QACJ,CAAC;IACH,CAAC;IAEM,KAAK,CAAC,WAAW,CAAC,YAAqB;QAC5C,MAAM,cAAc,GAAG,YAAY,IAAI,IAAI,CAAC,aAAa,CAAC,cAAc,EAAE,CAAC,QAAQ,CAAC;QACpF,OAAO,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;IAC7C,CAAC;IAEM,qBAAqB;QAC1B,OAAO,CAAC,QAAQ,EAAE,WAAW,EAAE,UAAU,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,CAAC,CAAC;IAC5E,CAAC;IAEM,mBAAmB,CAAC,YAAoB;QAC7C,OAAO,IAAI,CAAC,qBAAqB,EAAE,CAAC,QAAQ,CAAC,YAAY,CAAC,WAAW,EAAE,CAAC,CAAC;IAC3E,CAAC;IAEM,KAAK,CAAC,YAAY,CAAC,QAAqB;QAC7C,IAAI,CAAC;YACH,MAAM,UAAU,GAAG,uEAAuE,CAAC;YAC3F,MAAM,QAAQ,GAAG,MAAM,QAAQ,CAAC,gBAAgB,CAAC,UAAU,EAAE;gBAC3D,SAAS,EAAE,EAAE;gBACb,WAAW,EAAE,CAAC;aACf,CAAC,CAAC;YAEH,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;gBACtB,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;YACtD,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,6BAA6B,QAAQ,CAAC,IAAI,EAAE,EAAE;gBAC9D,QAAQ,EAAE,QAAQ,CAAC,IAAI;gBACvB,cAAc,EAAE,QAAQ,CAAC,OAAO,CAAC,MAAM;aACxC,CAAC,CAAC;YAEH,OAAO,IAAI,CAAC;QAEd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,yBAAyB,QAAQ,CAAC,IAAI,EAAE,EAAE;gBAC1D,QAAQ,EAAE,QAAQ,CAAC,IAAI;gBACvB,KAAK,EAAG,KAAe,CAAC,OAAO;aAChC,CAAC,CAAC;YAEH,MAAM,IAAI,gBAAgB,CACxB,QAAQ,CAAC,IAAI,EACb,yBAA0B,KAAe,CAAC,OAAO,EAAE,CACpD,CAAC;QACJ,CAAC;IACH,CAAC;IAEM,KAAK,CAAC,gBAAgB;QAC3B,MAAM,OAAO,GAA4B,EAAE,CAAC;QAC5C,MAAM,kBAAkB,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAExD,KAAK,MAAM,YAAY,IAAI,kBAAkB,EAAE,CAAC;YAC9C,IAAI,CAAC;gBACH,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;gBAClE,IAAI,CAAC,MAAM,EAAE,CAAC;oBACZ,OAAO,CAAC,YAAY,CAAC,GAAG,KAAK,CAAC;oBAC9B,SAAS;gBACX,CAAC;gBAED,kDAAkD;gBAClD,IAAI,YAAY,KAAK,QAAQ,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;oBAChD,OAAO,CAAC,YAAY,CAAC,GAAG,KAAK,CAAC;oBAC9B,SAAS;gBACX,CAAC;gBAED,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;gBACzD,OAAO,CAAC,YAAY,CAAC,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;YAE5D,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,yBAAyB,YAAY,EAAE,EAAE;oBACzD,QAAQ,EAAE,YAAY;oBACtB,KAAK,EAAG,KAAe,CAAC,OAAO;iBAChC,CAAC,CAAC;gBACH,OAAO,CAAC,YAAY,CAAC,GAAG,KAAK,CAAC;YAChC,CAAC;QACH,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAEM,kBAAkB,CAAC,YAAqB;QAC7C,IAAI,YAAY,EAAE,CAAC;YACjB,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;YACpC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,2BAA2B,YAAY,EAAE,CAAC,CAAC;QAC9D,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;YACvB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;QAClD,CAAC;IACH,CAAC;IAEM,uBAAuB,CAAC,YAAoB;QAMjD,MAAM,YAAY,GAAG;YACnB,MAAM,EAAE;gBACN,mBAAmB,EAAE,IAAI;gBACzB,iBAAiB,EAAE,IAAI;gBACvB,SAAS,EAAE,MAAM;gBACjB,eAAe,EAAE,CAAC,OAAO,EAAE,aAAa,EAAE,eAAe,EAAE,QAAQ,EAAE,aAAa,CAAC;aACpF;YACD,SAAS,EAAE;gBACT,mBAAmB,EAAE,IAAI;gBACzB,iBAAiB,EAAE,IAAI;gBACvB,SAAS,EAAE,MAAM;gBACjB,eAAe,EAAE,CAAC,wBAAwB,EAAE,0BAA0B,EAAE,yBAAyB,EAAE,4BAA4B,CAAC;aACjI;YACD,QAAQ,EAAE;gBACR,mBAAmB,EAAE,IAAI;gBACzB,iBAAiB,EAAE,IAAI;gBACvB,SAAS,EAAE,KAAK;gBAChB,eAAe,EAAE,CAAC,eAAe,EAAE,gBAAgB,CAAC;aACrD;YACD,MAAM,EAAE;gBACN,mBAAmB,EAAE,IAAI;gBACzB,iBAAiB,EAAE,IAAI;gBACvB,SAAS,EAAE,KAAK;gBAChB,eAAe,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,WAAW,EAAE,SAAS,EAAE,SAAS,CAAC;aACzE;YACD,MAAM,EAAE;gBACN,mBAAmB,EAAE,IAAI;gBACzB,iBAAiB,EAAE,IAAI;gBACvB,SAAS,EAAE,KAAK;gBAChB,eAAe,EAAE,CAAC,YAAY,EAAE,mBAAmB,CAAC;aACrD;YACD,OAAO,EAAE;gBACP,mBAAmB,EAAE,IAAI;gBACzB,iBAAiB,EAAE,IAAI;gBACvB,SAAS,EAAE,KAAK;gBAChB,eAAe,EAAE,CAAC,cAAc,EAAE,eAAe,EAAE,gBAAgB,EAAE,eAAe,CAAC;aACtF;SACF,CAAC;QAEF,OAAO,YAAY,CAAC,YAAY,CAAC,WAAW,EAA+B,CAAC,IAAI;YAC9E,mBAAmB,EAAE,KAAK;YAC1B,iBAAiB,EAAE,KAAK;YACxB,SAAS,EAAE,IAAI;YACf,eAAe,EAAE,EAAE;SACpB,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,cAAc,CAAC,eAAuB;QACjD,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,eAAe,CAAC,EAAE,CAAC;YAC/C,MAAM,IAAI,gBAAgB,CAAC,eAAe,EAAE,yBAAyB,eAAe,EAAE,CAAC,CAAC;QAC1F,CAAC;QAED,uBAAuB;QACvB,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC,EAAE,QAAQ,EAAE,eAAe,EAAE,CAAC,CAAC;QAEpE,2BAA2B;QAC3B,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAE1B,iCAAiC;QACjC,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,eAAe,CAAC,CAAC;QAE5D,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,yBAAyB,eAAe,EAAE,CAAC,CAAC;QAC7D,OAAO,QAAQ,CAAC;IAClB,CAAC;IAEM,eAAe,CAAC,YAAoB;QAOzC,MAAM,YAAY,GAAG;YACnB,MAAM,EAAE;gBACN,IAAI,EAAE,QAAQ;gBACd,WAAW,EAAE,gDAAgD;gBAC7D,OAAO,EAAE,oBAAoB;gBAC7B,OAAO,EAAE,eAAe;gBACxB,QAAQ,EAAE,CAAC,kBAAkB,EAAE,iBAAiB,EAAE,WAAW,EAAE,QAAQ,CAAC;aACzE;YACD,SAAS,EAAE;gBACT,IAAI,EAAE,kBAAkB;gBACxB,WAAW,EAAE,sDAAsD;gBACnE,OAAO,EAAE,uBAAuB;gBAChC,OAAO,EAAE,eAAe;gBACxB,QAAQ,EAAE,CAAC,kBAAkB,EAAE,cAAc,EAAE,gBAAgB,EAAE,WAAW,CAAC;aAC9E;YACD,QAAQ,EAAE;gBACR,IAAI,EAAE,UAAU;gBAChB,WAAW,EAAE,qDAAqD;gBAClE,OAAO,EAAE,sBAAsB;gBAC/B,OAAO,EAAE,eAAe;gBACxB,QAAQ,EAAE,CAAC,iBAAiB,EAAE,kBAAkB,EAAE,gBAAgB,CAAC;aACpE;YACD,MAAM,EAAE;gBACN,IAAI,EAAE,QAAQ;gBACd,WAAW,EAAE,yCAAyC;gBACtD,OAAO,EAAE,mBAAmB;gBAC5B,OAAO,EAAE,cAAc;gBACvB,QAAQ,EAAE,CAAC,iBAAiB,EAAE,SAAS,EAAE,cAAc,EAAE,iBAAiB,CAAC;aAC5E;YACD,MAAM,EAAE;gBACN,IAAI,EAAE,eAAe;gBACrB,WAAW,EAAE,+CAA+C;gBAC5D,OAAO,EAAE,uBAAuB;gBAChC,OAAO,EAAE,eAAe;gBACxB,QAAQ,EAAE,CAAC,YAAY,EAAE,kBAAkB,EAAE,gBAAgB,CAAC;aAC/D;YACD,OAAO,EAAE;gBACP,IAAI,EAAE,YAAY;gBAClB,WAAW,EAAE,oDAAoD;gBACjE,OAAO,EAAE,oBAAoB;gBAC7B,OAAO,EAAE,eAAe;gBACxB,QAAQ,EAAE,CAAC,kBAAkB,EAAE,cAAc,EAAE,WAAW,CAAC;aAC5D;SACF,CAAC;QAEF,OAAO,YAAY,CAAC,YAAY,CAAC,WAAW,EAA+B,CAAC,IAAI;YAC9E,IAAI,EAAE,SAAS;YACf,WAAW,EAAE,kBAAkB;YAC/B,OAAO,EAAE,EAAE;YACX,OAAO,EAAE,SAAS;YAClB,QAAQ,EAAE,EAAE;SACb,CAAC;IACJ,CAAC;CACF"}