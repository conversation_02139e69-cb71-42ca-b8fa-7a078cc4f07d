#!/usr/bin/env node
import { Command } from 'commander';
import inquirer from 'inquirer';
import chalk from 'chalk';
import ora from 'ora';
import dotenv from 'dotenv';
import path from 'path';
import { AgentOrchestrator } from './agents/AgentOrchestrator';
import { ConfigManager } from './config/ConfigManager';
import { SessionManager } from './session/SessionManager';
import { ErrorHandler } from './utils/ErrorHandler';
// import { Logger } from './utils/Logger';
// Load environment variables
dotenv.config();
const program = new Command();
// const _logger = Logger.getInstance();
const errorHandler = ErrorHandler.getInstance();
const configManager = ConfigManager.getInstance();
const sessionManager = SessionManager.getInstance();
const agentOrchestrator = AgentOrchestrator.getInstance();
// Setup graceful shutdown
errorHandler.createGracefulShutdown();
program
    .name('ai-cli')
    .description('🤖 Autonomous AI-Powered CLI Tool System')
    .version('1.0.0')
    .configureHelp({
    sortSubcommands: true,
    subcommandTerm: (cmd) => `${cmd.name()} ${cmd.usage()}`
});
// Main chat command
program
    .command('chat')
    .description('Start an interactive AI chat session')
    .option('-d, --directory <path>', 'Working directory', process.cwd())
    .option('-p, --provider <provider>', 'LLM provider to use')
    .option('-m, --model <model>', 'Model to use')
    .option('-s, --session <sessionId>', 'Resume existing session')
    .option('--autonomous', 'Enable autonomous mode for complex tasks')
    .option('--max-iterations <iterations>', 'Maximum iterations for autonomous mode', parseInt)
    .action(async (options) => {
    const spinner = ora('Initializing AI CLI...').start();
    try {
        // Set working directory
        const workingDirectory = path.resolve(options.directory);
        process.chdir(workingDirectory);
        // Update config if provider/model specified
        if (options.provider) {
            configManager.updateAgentConfig({ provider: options.provider });
        }
        if (options.model) {
            configManager.updateAgentConfig({ model: options.model });
        }
        // Validate configuration
        if (!configManager.validateConfig()) {
            spinner.fail('Configuration validation failed');
            console.log(chalk.yellow('\nPlease run "ai-cli config" to set up your API keys.'));
            return;
        }
        // Initialize or resume session
        let context;
        if (options.session) {
            spinner.text = 'Resuming session...';
            const session = await sessionManager.loadSession(options.session);
            context = session.context;
        }
        else {
            spinner.text = 'Creating new session...';
            context = await agentOrchestrator.initialize(workingDirectory);
        }
        spinner.succeed('AI CLI initialized successfully!');
        // Display welcome message
        console.log(chalk.blue.bold('\n🤖 AI CLI Agent - Autonomous Assistant'));
        console.log(chalk.gray('Type "help" for commands, "exit" to quit\n'));
        const sessionInfo = agentOrchestrator.getSessionInfo();
        if (sessionInfo) {
            console.log(chalk.cyan(`📁 Working Directory: ${sessionInfo.workingDirectory}`));
            console.log(chalk.cyan(`🏗️  Project Type: ${sessionInfo.projectType}`));
            console.log(chalk.cyan(`💬 Session: ${sessionInfo.sessionId.substring(0, 8)}...`));
            console.log('');
        }
        // Start interactive chat loop
        await startChatLoop(context);
    }
    catch (error) {
        spinner.fail('Failed to initialize AI CLI');
        errorHandler.handleError(error, 'CLI initialization');
    }
});
// Configuration command
program
    .command('config')
    .description('Configure AI CLI settings')
    .option('--provider <provider>', 'Set default LLM provider')
    .option('--model <model>', 'Set default model')
    .option('--api-key <key>', 'Set API key for current provider')
    .option('--list', 'List current configuration')
    .action(async (options) => {
    try {
        if (options.list) {
            const config = configManager.getConfig();
            console.log(chalk.blue.bold('\n🔧 Current Configuration:'));
            console.log(chalk.cyan(`Provider: ${config.agent.provider}`));
            console.log(chalk.cyan(`Model: ${config.agent.model}`));
            console.log(chalk.cyan(`Temperature: ${config.agent.temperature}`));
            console.log(chalk.cyan(`Max Tokens: ${config.agent.maxTokens}`));
            console.log(chalk.cyan(`Tool Calling: ${config.agent.enableToolCalling ? 'Enabled' : 'Disabled'}`));
            console.log(chalk.cyan(`Parallel Execution: ${config.agent.enableParallelExecution ? 'Enabled' : 'Disabled'}`));
            return;
        }
        if (options.provider) {
            configManager.updateAgentConfig({ provider: options.provider });
            console.log(chalk.green(`✅ Provider set to: ${options.provider}`));
        }
        if (options.model) {
            configManager.updateAgentConfig({ model: options.model });
            console.log(chalk.green(`✅ Model set to: ${options.model}`));
        }
        if (options.apiKey) {
            const provider = options.provider || configManager.getAgentConfig().provider;
            configManager.setProviderApiKey(provider, options.apiKey);
            console.log(chalk.green(`✅ API key set for: ${provider}`));
        }
        if (!options.provider && !options.model && !options.apiKey) {
            await interactiveConfig();
        }
    }
    catch (error) {
        errorHandler.handleError(error, 'Configuration');
    }
});
// Session management commands
program
    .command('sessions')
    .description('Manage chat sessions')
    .option('--list', 'List all sessions')
    .option('--delete <sessionId>', 'Delete a session')
    .option('--export <sessionId>', 'Export a session')
    .option('--import <filePath>', 'Import a session')
    .action(async (options) => {
    try {
        if (options.list) {
            const sessions = sessionManager.listSessions();
            console.log(chalk.blue.bold('\n📝 Chat Sessions:'));
            if (sessions.length === 0) {
                console.log(chalk.gray('No sessions found.'));
                return;
            }
            for (const session of sessions.slice(0, 10)) {
                const age = Math.floor((Date.now() - session.lastAccessedAt.getTime()) / (1000 * 60 * 60));
                console.log(chalk.cyan(`${session.id.substring(0, 8)}... - ${session.workingDirectory} (${age}h ago)`));
            }
            return;
        }
        if (options.delete) {
            const deleted = await sessionManager.deleteSession(options.delete);
            if (deleted) {
                console.log(chalk.green(`✅ Session deleted: ${options.delete}`));
            }
            else {
                console.log(chalk.red(`❌ Session not found: ${options.delete}`));
            }
            return;
        }
        if (options.export) {
            const filePath = `session-${options.export.substring(0, 8)}.json`;
            await sessionManager.exportSession(options.export, filePath);
            console.log(chalk.green(`✅ Session exported to: ${filePath}`));
            return;
        }
        if (options.import) {
            const session = await sessionManager.importSession(options.import);
            console.log(chalk.green(`✅ Session imported: ${session.id}`));
            return;
        }
    }
    catch (error) {
        errorHandler.handleError(error, 'Session management');
    }
});
// Status command
program
    .command('status')
    .description('Show comprehensive system status and configuration')
    .option('-v, --verbose', 'Show detailed information')
    .option('--json', 'Output in JSON format')
    .action(async (options) => {
    try {
        const spinner = ora('Checking status...').start();
        const config = configManager.getConfig();
        const agentConfig = configManager.getAgentConfig();
        const providerStatus = await agentOrchestrator.getProviderStatus();
        const sessionStats = sessionManager.getSessionStats();
        const toolRegistry = agentOrchestrator.toolRegistry;
        const statusInfo = {
            system: {
                version: '1.0.0',
                nodeVersion: process.version,
                platform: process.platform,
                arch: process.arch,
                workingDirectory: process.cwd(),
                configFile: configManager.getConfigPath()
            },
            agent: {
                provider: agentConfig.provider,
                model: agentConfig.model,
                maxTokens: agentConfig.maxTokens,
                temperature: agentConfig.temperature,
                systemPrompt: agentConfig.systemPrompt ? '✅ Set' : '❌ Not set'
            },
            providers: {
                current: providerStatus.current,
                available: providerStatus.available,
                working: providerStatus.working
            },
            features: {
                sessionPersistence: config.session.persistenceEnabled,
                contextWatching: config.context.watchForChanges,
                toolCalling: toolRegistry.hasToolCalling(),
                autonomousMode: true
            },
            tools: toolRegistry.getAllTools().map(tool => ({
                name: tool.name,
                description: tool.description
            })),
            sessions: sessionStats,
            performance: {
                toolStats: toolRegistry.getToolUsageStats()
            }
        };
        spinner.succeed('Status check completed');
        if (options.json) {
            console.log(JSON.stringify(statusInfo, null, 2));
            return;
        }
        console.log('\n🤖 AI CLI Agent Status\n');
        console.log('═'.repeat(60));
        console.log('\n📊 System Information:');
        console.log(`  Version: ${statusInfo.system.version}`);
        console.log(`  Node.js: ${statusInfo.system.nodeVersion}`);
        console.log(`  Platform: ${statusInfo.system.platform} (${statusInfo.system.arch})`);
        console.log(`  Working Directory: ${statusInfo.system.workingDirectory}`);
        console.log(`  Config File: ${statusInfo.system.configFile}`);
        console.log('\n🧠 Agent Configuration:');
        console.log(`  Provider: ${statusInfo.agent.provider}`);
        console.log(`  Model: ${statusInfo.agent.model}`);
        console.log(`  Max Tokens: ${statusInfo.agent.maxTokens}`);
        console.log(`  Temperature: ${statusInfo.agent.temperature}`);
        console.log(`  System Prompt: ${statusInfo.agent.systemPrompt}`);
        console.log('\n🔌 Provider Status:');
        console.log(`  Current: ${statusInfo.providers.current}`);
        console.log(`  Available: ${statusInfo.providers.available.join(', ')}`);
        for (const [provider, working] of Object.entries(statusInfo.providers.working)) {
            const status = working ? chalk.green('✅ Working') : chalk.red('❌ Not working');
            console.log(`  ${provider}: ${status}`);
        }
        console.log('\n⚡ Features:');
        console.log(`  Session Persistence: ${statusInfo.features.sessionPersistence ? '✅' : '❌'}`);
        console.log(`  Context Watching: ${statusInfo.features.contextWatching ? '✅' : '❌'}`);
        console.log(`  Tool Calling: ${statusInfo.features.toolCalling ? '✅' : '❌'}`);
        console.log(`  Autonomous Mode: ${statusInfo.features.autonomousMode ? '✅' : '❌'}`);
        console.log(`\n🛠️  Available Tools (${statusInfo.tools.length}):`);
        statusInfo.tools.forEach(tool => {
            console.log(`  • ${tool.name}: ${tool.description}`);
        });
        console.log('\n📈 Session Statistics:');
        console.log(`  Total Sessions: ${statusInfo.sessions.totalSessions}`);
        console.log(`  Active Sessions: ${statusInfo.sessions.activeSessions}`);
        console.log(`  Total Messages: ${statusInfo.sessions.totalMessages}`);
        if (options.verbose) {
            console.log('\n🔧 Tool Usage Statistics:');
            Object.entries(statusInfo.performance.toolStats).forEach(([tool, stats]) => {
                const toolStats = stats;
                const successRate = toolStats.calls > 0 ? ((toolStats.successes / toolStats.calls) * 100).toFixed(1) : '0';
                console.log(`  ${tool}: ${toolStats.calls} calls, ${successRate}% success, ${toolStats.averageDuration.toFixed(0)}ms avg`);
            });
        }
        console.log('\n═'.repeat(60));
    }
    catch (error) {
        errorHandler.handleError(error, 'Status check');
    }
});
// Autonomous task execution command
program
    .command('task <description>')
    .description('Execute an autonomous task')
    .option('-d, --directory <path>', 'Working directory', process.cwd())
    .option('-p, --provider <provider>', 'LLM provider to use')
    .option('--max-iterations <iterations>', 'Maximum iterations', parseInt, 10)
    .option('--save-progress', 'Save task progress')
    .option('--learning', 'Enable learning mode')
    .action(async (description, options) => {
    const spinner = ora('Initializing autonomous task...').start();
    try {
        const workingDirectory = path.resolve(options.directory);
        if (options.provider) {
            configManager.updateAgentConfig({ provider: options.provider });
        }
        if (!configManager.validateConfig()) {
            spinner.fail('Configuration validation failed');
            console.log(chalk.yellow('\nPlease run "ai-cli config" to set up your API keys.'));
            return;
        }
        await agentOrchestrator.initialize(workingDirectory);
        spinner.text = 'Executing autonomous task...';
        const result = await agentOrchestrator.executeAutonomousTask(description, workingDirectory, {
            maxIterations: options.maxIterations,
            enableLearning: options.learning,
            saveProgress: options.saveProgress
        });
        spinner.succeed(`Task completed in ${result.iterations} iterations`);
        console.log(chalk.blue.bold('\n🤖 Autonomous Task Results:'));
        console.log(chalk.cyan(`Success: ${result.success ? '✅' : '❌'}`));
        console.log(chalk.cyan(`Iterations: ${result.iterations}`));
        console.log(chalk.cyan(`Final Response:`));
        console.log(chalk.white(result.finalResponse));
        if (result.results.length > 0) {
            console.log(chalk.blue.bold('\n📊 Execution Summary:'));
            result.results.forEach((res, i) => {
                console.log(chalk.gray(`${i + 1}. ${res.task.substring(0, 80)}...`));
            });
        }
    }
    catch (error) {
        spinner.fail('Autonomous task failed');
        errorHandler.handleError(error, 'Autonomous task execution');
    }
});
// Advanced autonomous task command with enhanced features
program
    .command('advanced-task <description>')
    .alias('adv-task')
    .description('Execute advanced autonomous task with enhanced AI capabilities')
    .option('-d, --directory <path>', 'Working directory', process.cwd())
    .option('-p, --provider <provider>', 'LLM provider to use')
    .option('--max-iterations <iterations>', 'Maximum iterations', parseInt, 15)
    .option('--save-progress', 'Save task progress')
    .option('--learning', 'Enable learning mode')
    .option('--adaptive', 'Enable adaptive strategy')
    .option('--context-awareness', 'Enable context awareness')
    .option('--self-correction', 'Enable self-correction')
    .action(async (description, options) => {
    const spinner = ora('Initializing advanced autonomous task...').start();
    try {
        const workingDirectory = path.resolve(options.directory);
        if (options.provider) {
            configManager.updateAgentConfig({ provider: options.provider });
        }
        if (!configManager.validateConfig()) {
            spinner.fail('Configuration validation failed');
            console.log(chalk.yellow('\nPlease run "ai-cli config" to set up your API keys.'));
            return;
        }
        await agentOrchestrator.initialize(workingDirectory);
        spinner.text = 'Executing advanced autonomous task...';
        const result = await agentOrchestrator.executeAdvancedAutonomousTask(description, workingDirectory, {
            maxIterations: options.maxIterations,
            enableLearning: options.learning,
            saveProgress: options.saveProgress,
            adaptiveStrategy: options.adaptive,
            contextAwareness: options.contextAwareness,
            selfCorrection: options.selfCorrection
        });
        spinner.succeed(`Advanced task completed in ${result.iterations} iterations`);
        console.log(chalk.blue.bold('\n🚀 Advanced Autonomous Task Results:'));
        console.log(chalk.cyan(`Success: ${result.success ? '✅' : '❌'}`));
        console.log(chalk.cyan(`Iterations: ${result.iterations}`));
        console.log(chalk.cyan(`Final Response:`));
        console.log(chalk.white(result.finalResponse));
        if (result.insights.length > 0) {
            console.log(chalk.blue.bold('\n💡 Insights:'));
            result.insights.forEach(insight => console.log(chalk.cyan(`  • ${insight}`)));
        }
        if (result.recommendations.length > 0) {
            console.log(chalk.blue.bold('\n📋 Recommendations:'));
            result.recommendations.forEach(rec => console.log(chalk.yellow(`  • ${rec}`)));
        }
        if (result.results.length > 0) {
            console.log(chalk.blue.bold('\n📊 Execution Summary:'));
            result.results.forEach((res, i) => {
                console.log(chalk.gray(`${i + 1}. ${res.task.substring(0, 80)}...`));
            });
        }
    }
    catch (error) {
        spinner.fail('Advanced autonomous task failed');
        errorHandler.handleError(error, 'Advanced autonomous task execution');
    }
});
// Workflow execution command
program
    .command('workflow <name>')
    .description('Execute predefined autonomous workflow')
    .option('-d, --directory <path>', 'Working directory', process.cwd())
    .option('-p, --provider <provider>', 'LLM provider to use')
    .option('--continue-on-failure', 'Continue workflow even if steps fail')
    .option('--max-retries <retries>', 'Maximum retries per step', parseInt, 2)
    .option('--timeout <seconds>', 'Workflow timeout in seconds', parseInt, 300)
    .action(async (name, options) => {
    const spinner = ora('Loading workflow...').start();
    try {
        const workingDirectory = path.resolve(options.directory);
        if (options.provider) {
            configManager.updateAgentConfig({ provider: options.provider });
        }
        if (!configManager.validateConfig()) {
            spinner.fail('Configuration validation failed');
            console.log(chalk.yellow('\nPlease run "ai-cli config" to set up your API keys.'));
            return;
        }
        await agentOrchestrator.initialize(workingDirectory);
        // Load predefined workflows (this would be from a config file or database)
        const workflows = {
            'code-review': {
                name: 'Code Review',
                description: 'Comprehensive code review and analysis',
                steps: [
                    { name: 'file_read', id: '1', arguments: { pattern: '**/*.{js,ts,py,java,cpp}' } },
                    { name: 'shell_execute', id: '2', arguments: { command: 'git log --oneline -10' } },
                    { name: 'file_search', id: '3', arguments: { pattern: 'TODO|FIXME|BUG', directory: '.' } }
                ]
            },
            'project-setup': {
                name: 'Project Setup',
                description: 'Initialize new project with best practices',
                steps: [
                    { name: 'file_create', id: '1', arguments: { path: 'README.md', content: '# New Project\n\nProject description here.' } },
                    { name: 'file_create', id: '2', arguments: { path: '.gitignore', content: 'node_modules/\n.env\n*.log' } },
                    { name: 'shell_execute', id: '3', arguments: { command: 'git init' } }
                ]
            }
        };
        const workflow = workflows[name];
        if (!workflow) {
            spinner.fail(`Workflow '${name}' not found`);
            console.log(chalk.yellow(`Available workflows: ${Object.keys(workflows).join(', ')}`));
            return;
        }
        spinner.text = `Executing workflow: ${workflow.name}...`;
        const toolRegistry = agentOrchestrator.toolRegistry;
        const context = {
            sessionId: `workflow-${Date.now()}`,
            workingDirectory,
            projectContext: null,
            conversationHistory: [],
            availableTools: [],
            config: configManager.getAgentConfig()
        };
        const result = await toolRegistry.executeAutonomousWorkflow({
            ...workflow,
            conditions: {
                continueOnFailure: options.continueOnFailure,
                maxRetries: options.maxRetries,
                timeout: options.timeout * 1000
            }
        }, context);
        spinner.succeed(`Workflow completed: ${result.stepsCompleted}/${workflow.steps.length} steps`);
        console.log(chalk.blue.bold('\n⚙️ Workflow Results:'));
        console.log(chalk.cyan(`Success: ${result.success ? '✅' : '❌'}`));
        console.log(chalk.cyan(`Steps Completed: ${result.stepsCompleted}/${workflow.steps.length}`));
        console.log(chalk.cyan(`Execution Time: ${result.executionTime}ms`));
        if (result.results.length > 0) {
            console.log(chalk.blue.bold('\n📊 Step Results:'));
            result.results.forEach((res, i) => {
                const status = res.success ? '✅' : '❌';
                console.log(chalk.gray(`${i + 1}. ${status} ${workflow.steps[i]?.name || 'Unknown'}`));
            });
        }
    }
    catch (error) {
        spinner.fail('Workflow execution failed');
        errorHandler.handleError(error, 'Workflow execution');
    }
});
// Project analysis command
program
    .command('analyze [directory]')
    .description('Analyze project health and structure')
    .option('--detailed', 'Show detailed analysis')
    .action(async (directory, options) => {
    const spinner = ora('Analyzing project...').start();
    try {
        const workingDirectory = path.resolve(directory || process.cwd());
        await agentOrchestrator.initialize(workingDirectory);
        const health = await agentOrchestrator.analyzeProjectHealth(workingDirectory);
        spinner.succeed('Project analysis completed');
        console.log(chalk.blue.bold('\n🔍 Project Health Analysis:'));
        console.log(chalk.cyan(`Health Score: ${health.score}/100`));
        if (health.issues.length > 0) {
            console.log(chalk.red.bold('\n⚠️  Issues Found:'));
            health.issues.forEach(issue => {
                console.log(chalk.red(`• ${issue}`));
            });
        }
        if (health.recommendations.length > 0) {
            console.log(chalk.yellow.bold('\n💡 Recommendations:'));
            health.recommendations.forEach(rec => {
                console.log(chalk.yellow(`• ${rec}`));
            });
        }
        if (options.detailed) {
            console.log(chalk.blue.bold('\n📈 Metrics:'));
            Object.entries(health.metrics).forEach(([key, value]) => {
                console.log(chalk.cyan(`${key}: ${value}`));
            });
        }
    }
    catch (error) {
        spinner.fail('Project analysis failed');
        errorHandler.handleError(error, 'Project analysis');
    }
});
// Context management command
program
    .command('context')
    .description('Manage project context')
    .option('--refresh', 'Refresh project context')
    .option('--snapshot', 'Save context snapshot')
    .option('--history', 'Show context history')
    .option('--restore <index>', 'Restore from snapshot', parseInt)
    .option('-d, --directory <path>', 'Working directory', process.cwd())
    .action(async (options) => {
    try {
        const workingDirectory = path.resolve(options.directory);
        await agentOrchestrator.initialize(workingDirectory);
        if (options.refresh) {
            const spinner = ora('Refreshing context...').start();
            await agentOrchestrator.refreshProjectContext();
            spinner.succeed('Context refreshed');
            return;
        }
        if (options.snapshot) {
            const spinner = ora('Saving context snapshot...').start();
            const contextEngine = agentOrchestrator.contextEngine;
            await contextEngine.saveContextSnapshot(workingDirectory);
            spinner.succeed('Context snapshot saved');
            return;
        }
        if (options.history) {
            const contextEngine = agentOrchestrator.contextEngine;
            const history = contextEngine.getContextHistory(workingDirectory);
            console.log(chalk.blue.bold('\n📚 Context History:'));
            if (history.length === 0) {
                console.log(chalk.gray('No context snapshots found.'));
            }
            else {
                history.forEach((snapshot, index) => {
                    const date = new Date(snapshot.lastUpdated).toLocaleString();
                    console.log(chalk.cyan(`${index}: ${date} - ${snapshot.files?.length || 0} files`));
                });
            }
            return;
        }
        if (typeof options.restore === 'number') {
            const spinner = ora('Restoring context snapshot...').start();
            const contextEngine = agentOrchestrator.contextEngine;
            const restored = contextEngine.restoreContextSnapshot(workingDirectory, options.restore);
            if (restored) {
                spinner.succeed(`Context restored from snapshot ${options.restore}`);
            }
            else {
                spinner.fail(`Failed to restore snapshot ${options.restore}`);
            }
            return;
        }
        // Show context summary
        const contextEngine = agentOrchestrator.contextEngine;
        const context = contextEngine.getProjectContext(workingDirectory);
        const metrics = contextEngine.getContextMetrics(workingDirectory);
        console.log(chalk.blue.bold('\n📁 Context Summary:'));
        if (context) {
            console.log(chalk.cyan(`Project Type: ${context.projectType}`));
            console.log(chalk.cyan(`Files: ${context.files.length}`));
            console.log(chalk.cyan(`Dependencies: ${Object.keys(context.dependencies).length}`));
            console.log(chalk.cyan(`Git: ${context.gitInfo ? 'Yes' : 'No'}`));
        }
        if (metrics) {
            console.log(chalk.blue.bold('\n📊 Context Metrics:'));
            console.log(chalk.cyan(`File Count: ${metrics.fileCount}`));
            console.log(chalk.cyan(`Total Size: ${(metrics.totalSize / 1024 / 1024).toFixed(2)} MB`));
            console.log(chalk.cyan(`Cache Hit Rate: ${(metrics.cacheHitRate * 100).toFixed(1)}%`));
            console.log(chalk.cyan(`Last Updated: ${metrics.lastUpdated.toLocaleString()}`));
        }
    }
    catch (error) {
        errorHandler.handleError(error, 'Context management');
    }
});
// Tool statistics command
program
    .command('tools')
    .description('Show tool usage statistics and information')
    .option('--stats', 'Show usage statistics')
    .option('--list', 'List available tools')
    .action(async (options) => {
    try {
        await agentOrchestrator.initialize(process.cwd());
        const toolRegistry = agentOrchestrator.toolRegistry;
        if (options.stats) {
            const stats = toolRegistry.getToolUsageStats();
            console.log(chalk.blue.bold('\n🔧 Tool Usage Statistics:'));
            Object.entries(stats).forEach(([toolName, stat]) => {
                const successRate = stat.calls > 0 ? ((stat.successes / stat.calls) * 100).toFixed(1) : '0';
                console.log(chalk.cyan(`\n${toolName}:`));
                console.log(chalk.gray(`  Calls: ${stat.calls}`));
                console.log(chalk.gray(`  Success Rate: ${successRate}%`));
                console.log(chalk.gray(`  Average Duration: ${stat.averageDuration.toFixed(0)}ms`));
            });
            return;
        }
        if (options.list) {
            const tools = toolRegistry.getAllTools();
            console.log(chalk.blue.bold('\n🛠️  Available Tools:'));
            tools.forEach((tool) => {
                console.log(chalk.cyan(`\n${tool.name}:`));
                console.log(chalk.gray(`  Description: ${tool.description}`));
                console.log(chalk.gray(`  Parameters: ${Object.keys(tool.parameters.properties || {}).join(', ')}`));
            });
            return;
        }
        // Default: show both
        const tools = toolRegistry.getAllTools();
        const stats = toolRegistry.getToolUsageStats();
        console.log(chalk.blue.bold('\n🛠️  Tool Overview:'));
        console.log(chalk.cyan(`Total Tools: ${tools.length}`));
        const totalCalls = Object.values(stats).reduce((sum, stat) => sum + stat.calls, 0);
        console.log(chalk.cyan(`Total Executions: ${totalCalls}`));
    }
    catch (error) {
        errorHandler.handleError(error, 'Tool statistics');
    }
});
async function startChatLoop(context) {
    while (true) {
        try {
            const { input } = await inquirer.prompt({
                type: 'input',
                name: 'input',
                message: chalk.green('You:')
            });
            if (!input.trim())
                continue;
            // Handle special commands
            if (input.toLowerCase() === 'exit' || input.toLowerCase() === 'quit') {
                console.log(chalk.yellow('👋 Goodbye!'));
                break;
            }
            if (input.toLowerCase() === 'help') {
                showHelpMessage();
                continue;
            }
            if (input.toLowerCase() === 'clear') {
                await agentOrchestrator.clearConversation();
                console.log(chalk.yellow('🧹 Conversation cleared'));
                continue;
            }
            if (input.toLowerCase() === 'refresh') {
                await agentOrchestrator.refreshProjectContext();
                console.log(chalk.yellow('🔄 Project context refreshed'));
                continue;
            }
            if (input.toLowerCase().startsWith('autonomous:')) {
                const task = input.substring(11).trim();
                if (task) {
                    const spinner = ora('Executing autonomous task...').start();
                    try {
                        const result = await agentOrchestrator.executeAutonomousTask(task, context.workingDirectory, {
                            maxIterations: 5,
                            enableLearning: true,
                            saveProgress: true
                        });
                        spinner.succeed(`Autonomous task completed in ${result.iterations} iterations`);
                        console.log(chalk.blue('\nAI (Autonomous):'), result.finalResponse);
                        console.log('');
                    }
                    catch (error) {
                        spinner.fail('Autonomous task failed');
                        errorHandler.handleError(error, 'Autonomous task');
                    }
                    continue;
                }
            }
            // Process user input
            const spinner = ora('AI is thinking...').start();
            try {
                const response = await agentOrchestrator.processUserInput(input, context, {
                    enableToolCalling: true,
                    maxIterations: 5
                });
                spinner.stop();
                console.log(chalk.blue('\nAI:'), response);
                console.log('');
            }
            catch (error) {
                spinner.fail('AI processing failed');
                errorHandler.handleError(error, 'AI processing');
            }
        }
        catch (error) {
            if (error.name === 'ExitPromptError') {
                console.log(chalk.yellow('\n👋 Goodbye!'));
                break;
            }
            errorHandler.handleError(error, 'Chat loop');
        }
    }
    // Cleanup
    agentOrchestrator.cleanup();
}
function showHelpMessage() {
    console.log(chalk.blue.bold('\n🆘 Available Commands:'));
    console.log(chalk.cyan('help                    - Show this help message'));
    console.log(chalk.cyan('clear                   - Clear conversation history'));
    console.log(chalk.cyan('refresh                 - Refresh project context'));
    console.log(chalk.cyan('autonomous: <task>      - Execute autonomous task'));
    console.log(chalk.cyan('exit                    - Exit the chat session'));
    console.log(chalk.gray('\nOr just type your question/request naturally!'));
    console.log(chalk.blue.bold('\n🤖 Autonomous Mode:'));
    console.log(chalk.gray('Use "autonomous: <task description>" for complex multi-step tasks'));
    console.log(chalk.gray('Example: autonomous: analyze this codebase and suggest improvements'));
    console.log(chalk.blue.bold('\n🚀 Advanced Commands:'));
    console.log(chalk.cyan('task <description>      - Execute autonomous task with options'));
    console.log(chalk.cyan('advanced-task <desc>    - Execute with enhanced AI capabilities'));
    console.log(chalk.cyan('workflow <name>         - Execute predefined workflow'));
    console.log(chalk.cyan('analyze [directory]     - Analyze project health'));
    console.log(chalk.cyan('context                 - Manage project context'));
    console.log(chalk.cyan('tools                   - Show tool information'));
    console.log(chalk.cyan('status                  - Show system status'));
    console.log('');
}
async function interactiveConfig() {
    console.log(chalk.blue.bold('\n🔧 Interactive Configuration'));
    const { provider } = await inquirer.prompt([
        {
            type: 'list',
            name: 'provider',
            message: 'Select LLM provider:',
            choices: ['openai', 'anthropic', 'deepseek', 'ollama', 'gemini', 'mistral']
        }
    ]);
    configManager.updateAgentConfig({ provider });
    if (provider !== 'ollama') {
        const { apiKey } = await inquirer.prompt([
            {
                type: 'password',
                name: 'apiKey',
                message: `Enter API key for ${provider}:`,
                mask: '*'
            }
        ]);
        configManager.setProviderApiKey(provider, apiKey);
    }
    console.log(chalk.green('\n✅ Configuration saved successfully!'));
}
// Parse command line arguments
program.parse();
// If no command provided, show help
if (!process.argv.slice(2).length) {
    program.outputHelp();
}
//# sourceMappingURL=cli.js.map