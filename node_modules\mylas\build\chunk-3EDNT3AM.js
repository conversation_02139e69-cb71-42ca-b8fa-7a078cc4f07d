"use strict";Object.defineProperty(exports, "__esModule", {value: true}); function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }var _chunkM3H36CX2js = require('./chunk-M3H36CX2.js');var _fs = require('fs'); var _fs2 = _interopRequireDefault(_fs);var R={load:async(e,r)=>{if(await _chunkM3H36CX2js.c.call(void 0, e)){let t=await _fs2.default.promises.readFile(e);return r==null||r(t),t}else throw new Error(`Can't read from ${e}`)},save:async(e,r,t)=>{if(await _chunkM3H36CX2js.c.call(void 0, e))await _fs2.default.promises.writeFile(e,r),t==null||t();else throw new Error(`Can't write to ${e}`)},loadW:async(e,r)=>{let t=await _chunkM3H36CX2js.d.call(void 0, {method:5,path:e});return r==null||r(Buffer.from(t)),Buffer.from(t)},saveW:async(e,r,t)=>{let o=new Uint8Array(new SharedArrayBuffer(r.byteLength));o.set(r),await _chunkM3H36CX2js.d.call(void 0, {method:6,path:e,data:o,callback:t})}},O= exports.a =R;var _path = require('path'); var _path2 = _interopRequireDefault(_path);var _os = require('os'); var _os2 = _interopRequireDefault(_os);function _(e){let r={cwd:process.cwd(),relative:!0,...typeof e=="string"?{cwd:e}:e},t=[],o=r.cwd,s,i=!1;do if(o.charAt(0)==="~"&&(o=B(o)),o.charAt(0)==="@"&&(o=_path2.default.join(E||(E=g?_path2.default.resolve(c||(c=C()),"node_modules"):_path2.default.resolve(c||(c=C()),"lib/node_modules")),o.slice(1))),s=b(o),s){let n=r.relative?_path2.default.relative(r.cwd,s):s;i=t.indexOf(n)>-1,i||(t.push(n),o=_path2.default.join(s,"../../"))}while(s&&!i);return t}function b(e){let r=_path2.default.resolve(e,"node_modules"),t=_fs2.default.existsSync(r)?_path2.default.resolve(r):null;if(t)return t;let o=_path2.default.dirname(e);return o===e?null:b(o)}var E,c;function C(){if(process.env.PREFIX)c=process.env.PREFIX;else if(c=A(_path2.default.resolve(_os2.default.homedir(),".npmrc")),!c){try{c=A(_path2.default.resolve(I(),"..","..","npmrc")),c&&(c=A(_path2.default.resolve(c,"etc","npmrc"))||c)}catch (e2){}c||(g?c=process.env.APPDATA?_path2.default.join(process.env.APPDATA,"npm"):_path2.default.dirname(process.execPath):(c=_path2.default.dirname(_path2.default.dirname(process.execPath)),process.env.DESTDIR&&(c=_path2.default.join(process.env.DESTDIR,c))))}return c?B(c):""}function A(e){var r;try{return(r=F(_fs2.default.readFileSync(e,"utf-8")))==null?void 0:r.prefix}catch (e3){return null}}function F(e){let r=Object.create(null),t=r,o=null;for(let i of e.split(/[\r\n]+/g)){if(!i||i.match(/^\s*[;#]/))continue;let n=i.match(/^\[([^\]]*)\]$|^([^=]+)(=(.*))?$/i);if(!n)continue;if(n[1]!==void 0){if(o=P(n[1]),o==="__proto__"){t=Object.create(null);continue}t=r[o]=r[o]||Object.create(null);continue}let u=P(n[2]||""),d=u.length>2&&u.slice(-2)==="[]",l=d?u.slice(0,-2):u;if(l==="__proto__")continue;let a=n[3]?P(n[4]||""):!0,x=a==="true"||a==="false"||a==="null"?JSON.parse(a):a;d&&(Object.hasOwnProperty.call(t,l)?Array.isArray(t[l])||(t[l]=[t[l]]):t[l]=[]),Array.isArray(t[l])?t[l].push(x):t[l]=x}let s=[];for(let i of Object.keys(r)){if(!Object.hasOwnProperty.call(r,i)||typeof r[i]!="object"||Array.isArray(r[i]))continue;let n=i.replace(/\1/g,"LITERAL\\1LITERAL").replace(/\\\./g,"").split(/\./).map(a=>a.replace(/\1/g,"\\.").replace(/\2LITERAL\\1LITERAL\2/g,"")),u=r,d=n.pop()||"",l=d.replace(/\\\./g,".");for(let a of n)a!=="__proto__"&&((!Object.hasOwnProperty.call(u,a)||typeof u[a]!="object")&&(u[a]=Object.create(null)),u=u[a]);u===r&&l===d||(u[l]=r[i],s.push(i))}return s.forEach(i=>delete r[i]),r}function P(e){e=e.trim();let r=e.charAt(0),t=e.charAt(e.length-1);if(r==='"'&&t==='"'||r==="'"&&t==="'"){e=r==="'"?e.substr(1,e.length-2):e;try{return JSON.parse(e)}catch (e4){}}else{let o=!1,s="";for(let i=0;i<e.length;i++){let n=e.charAt(i);if(o)"\\;#".indexOf(n)!==-1?s+=n:s+="\\"+n,o=!1;else{if(";#".indexOf(n)!==-1)break;n==="\\"?o=!0:s+=n}}return o&&(s+="\\"),s.trim()}return e}var B=e=>e.charAt(0)==="~"?e.charAt(1)==="+"?_path2.default.join(process.cwd(),e.slice(2)):_path2.default.join(_os2.default.homedir(),e.slice(1)):e,g=process.platform==="win32"||process.env.OSTYPE==="cygwin"||process.env.OSTYPE==="msys";function I(){let e=(process.env.PATH||"").split(g?";":":"),r=[""];g&&(e.unshift(process.cwd()),r=(process.env.PATHEXT||".EXE;.CMD;.BAT;.COM").split(";"));for(let t of e){let o=_path2.default.join(t.charAt(0)==='"'&&t.slice(-1)==='"'?t.slice(1,-1):t,"npm");for(let s of r)if(M(o+s))return _fs2.default.realpathSync(o+s)}throw new Error("Can't find npm file")}function M(e){try{return(g?r=>{let t=_fs2.default.statSync(r);if(!t.isSymbolicLink()&&!t.isFile())return!1;if(!process.env.PATHEXT)return!0;let o=process.env.PATHEXT.split(";");if(o.indexOf("")!==-1)return!0;for(let s of o){let i=s.toLowerCase();if(i&&r.substr(-i.length).toLowerCase()===i)return!0}return!1}:r=>{let{mode:t,gid:o,uid:s,isFile:i}=_fs2.default.statSync(r);return i()&&(t&1||t&2&&o===process.getgid()||t&4&&s===process.getuid()||t&6&&process.getuid()===0)})(e)}catch (e5){return!1}}var X={mkS:e=>{_chunkM3H36CX2js.b.call(void 0, e)&&_fs2.default.mkdirSync(e,{recursive:!0})},rmS:e=>{_chunkM3H36CX2js.b.call(void 0, e)&&(process.versions.node.startsWith("12")?_fs2.default.rmdirSync(e,{recursive:!0}):_fs2.default.rmSync(e,{recursive:!0,force:!0}))},checkS:e=>_fs2.default.existsSync(e),mk:async(e,r)=>{await _chunkM3H36CX2js.c.call(void 0, e)&&await _fs2.default.promises.mkdir(e,{recursive:!0}),r==null||r()},rm:async(e,r)=>{await _chunkM3H36CX2js.c.call(void 0, e)&&(process.versions.node.startsWith("12")?await _fs2.default.promises.rmdir(e,{recursive:!0}):await _fs2.default.promises.rm(e,{recursive:!0,force:!0})),r==null||r()},check:async(e,r)=>{let t=_fs2.default.existsSync(e);return r==null||r(t),t},nodeModules:e=>_(e)},L= exports.b =X;var W={json:_chunkM3H36CX2js.f,file:_chunkM3H36CX2js.e,dir:L,buf:O,..._chunkM3H36CX2js.e},se= exports.c =W;exports.a = O; exports.b = L; exports.c = se;
