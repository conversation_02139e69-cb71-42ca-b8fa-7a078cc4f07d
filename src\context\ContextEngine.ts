import fs from 'fs-extra';
import path from 'path';
import chokidar from 'chokidar';
import { ProjectContext, AgentContext, FileInfo } from '../types';
import { Logger } from '../utils/Logger';
import { ConfigManager } from '../config/ConfigManager';
import { ProjectIndexer } from './ProjectIndexer';
import { ToolRegistry } from '../tools/ToolRegistry';

export class ContextEngine {
  private static instance: ContextEngine;
  private projectContexts: Map<string, ProjectContext>;
  private fileWatchers: Map<string, chokidar.FSWatcher>;
  private logger: Logger;
  private configManager: ConfigManager;
  private projectIndexer: ProjectIndexer;
  private toolRegistry: ToolRegistry;
  private contextUpdateCallbacks: Set<(_context?: ProjectContext) => void> = new Set();
  private contextHistory: Map<string, ProjectContext[]> = new Map();

  private constructor() {
    this.projectContexts = new Map();
    this.fileWatchers = new Map();
    this.logger = Logger.getInstance();
    this.configManager = ConfigManager.getInstance();
    this.projectIndexer = ProjectIndexer.getInstance();
    this.toolRegistry = ToolRegistry.getInstance();
  }

  public static getInstance(): ContextEngine {
    if (!ContextEngine.instance) {
      ContextEngine.instance = new ContextEngine();
    }
    return ContextEngine.instance;
  }

  public async createAgentContext(
    sessionId: string,
    workingDirectory: string
  ): Promise<AgentContext> {
    try {
      this.logger.info(`Creating agent context for session: ${sessionId}`, {
        sessionId,
        workingDirectory
      });

      // Get or create project context
      const projectContext = await this.getOrCreateProjectContext(workingDirectory);

      // Create agent context
      const agentContext: AgentContext = {
        sessionId,
        workingDirectory,
        projectContext,
        conversationHistory: [],
        availableTools: this.toolRegistry.getAllTools(),
        config: this.configManager.getAgentConfig()
      };

      // Set up file watching if enabled
      if (this.configManager.getConfig().context.watchForChanges) {
        await this.setupFileWatching(workingDirectory, projectContext);
      }

      this.logger.info(`Agent context created successfully`, {
        sessionId,
        projectType: projectContext.projectType,
        fileCount: projectContext.files.length,
        toolCount: agentContext.availableTools.length
      });

      return agentContext;

    } catch (error) {
      this.logger.error('Failed to create agent context', {
        sessionId,
        workingDirectory,
        error: (error as Error).message
      });
      throw error;
    }
  }

  private async getOrCreateProjectContext(workingDirectory: string): Promise<ProjectContext> {
    const normalizedPath = path.resolve(workingDirectory);
    
    // Check if we already have a context for this directory
    let projectContext = this.projectContexts.get(normalizedPath);
    
    if (!projectContext) {
      // Create new project context
      projectContext = await this.projectIndexer.indexProject(normalizedPath);
      this.projectContexts.set(normalizedPath, projectContext);
    } else {
      // Check if we need to refresh the context
      if (await this.shouldRefreshContext(projectContext)) {
        this.logger.info(`Refreshing project context: ${normalizedPath}`);
        projectContext = await this.projectIndexer.indexProject(normalizedPath);
        this.projectContexts.set(normalizedPath, projectContext);
      }
    }

    return projectContext;
  }

  private async shouldRefreshContext(projectContext: ProjectContext): Promise<boolean> {
    // Check if key files have been modified
    const keyFiles = [
      'package.json',
      'requirements.txt',
      'Cargo.toml',
      'go.mod',
      'pom.xml',
      '.gitignore'
    ];

    for (const keyFile of keyFiles) {
      const filePath = path.join(projectContext.rootPath, keyFile);
      try {
        if (await fs.pathExists(filePath)) {
          const stats = await fs.stat(filePath);
          const existingFile = projectContext.files.find(f => f.path === keyFile);
          
          if (!existingFile || stats.mtime > existingFile.lastModified) {
            return true;
          }
        }
      } catch {
        // Ignore errors checking individual files
      }
    }

    return false;
  }

  private async setupFileWatching(
    workingDirectory: string,
    projectContext: ProjectContext
  ): Promise<void> {
    const normalizedPath = path.resolve(workingDirectory);

    // Clean up existing watcher
    if (this.fileWatchers.has(normalizedPath)) {
      this.fileWatchers.get(normalizedPath)?.close();
    }

    try {
      const config = this.configManager.getConfig().context;

      const watcher = chokidar.watch(normalizedPath, {
        ignored: config.excludePatterns.map(pattern =>
          pattern.replace('**/', '').replace('*', '')
        ),
        persistent: true,
        ignoreInitial: true,
        followSymlinks: false,
        depth: 10,
        awaitWriteFinish: {
          stabilityThreshold: 100,
          pollInterval: 50
        }
      });

      watcher
        .on('add', (filePath) => this.handleFileChange(normalizedPath, filePath, 'add', projectContext))
        .on('change', (filePath) => this.handleFileChange(normalizedPath, filePath, 'change', projectContext))
        .on('unlink', (filePath) => this.handleFileChange(normalizedPath, filePath, 'unlink', projectContext))
        .on('addDir', (dirPath) => this.handleFileChange(normalizedPath, dirPath, 'addDir', projectContext))
        .on('unlinkDir', (dirPath) => this.handleFileChange(normalizedPath, dirPath, 'unlinkDir', projectContext))
        .on('error', (error) => {
          this.logger.warn('File watcher error', {
            workingDirectory: normalizedPath,
            error: error.message
          });
        });

      this.fileWatchers.set(normalizedPath, watcher);

      this.logger.debug(`Enhanced file watching enabled for: ${normalizedPath}`);

    } catch (error) {
      this.logger.warn('Failed to setup file watching', {
        workingDirectory: normalizedPath,
        error: (error as Error).message
      });
    }
  }

  private async handleFileChange(
    rootPath: string,
    filePath: string,
    eventType: string,
    projectContext: ProjectContext
  ): Promise<void> {
    try {
      const config = this.configManager.getConfig().context;
      const relativePath = path.relative(rootPath, filePath);

      // Check if file should be ignored
      const shouldIgnore = config.excludePatterns.some(pattern => {
        // Simple pattern matching - in a real implementation, use a proper glob matcher
        return relativePath.includes(pattern.replace('**/', '').replace('*', ''));
      });

      if (shouldIgnore) {
        return;
      }

      this.logger.debug('File change detected', {
        rootPath,
        relativePath,
        eventType,
        filePath
      });

      // Update the project context based on the file change
      switch (eventType) {
        case 'add':
        case 'change':
          const updatedFile = await this.projectIndexer.updateFileIndex(rootPath, relativePath);
          if (updatedFile) {
            const existingIndex = projectContext.files.findIndex(f => f.path === relativePath);
            if (existingIndex >= 0) {
              projectContext.files[existingIndex] = updatedFile;
            } else {
              projectContext.files.push(updatedFile);
            }
          }
          break;

        case 'unlink':
          projectContext.files = projectContext.files.filter(f => f.path !== relativePath);
          break;

        case 'addDir':
          // Re-index the directory
          await this.indexDirectory(filePath, projectContext);
          break;

        case 'unlinkDir':
          // Remove all files in the directory
          projectContext.files = projectContext.files.filter(f =>
            !f.path.startsWith(relativePath + path.sep) && f.path !== relativePath
          );
          break;
      }

      // If it's a key file, trigger a full refresh
      const keyFiles = ['package.json', 'requirements.txt', 'Cargo.toml', 'go.mod', 'pom.xml'];
      const filename = path.basename(relativePath);
      if (keyFiles.includes(filename)) {
        this.logger.info(`Key file changed, refreshing project context: ${filename}`);
        const refreshedContext = await this.projectIndexer.indexProject(rootPath);
        this.projectContexts.set(rootPath, refreshedContext);
      }

      // Notify context update
      this.notifyContextUpdate(projectContext);

    } catch (error) {
      this.logger.debug('Error handling file change', {
        rootPath,
        filePath,
        eventType,
        error: (error as Error).message
      });
    }
  }

  private async indexDirectory(dirPath: string, projectContext: ProjectContext): Promise<void> {
    try {
      const files = await this.projectIndexer.indexFiles(dirPath);
      for (const file of files) {
        const existingIndex = projectContext.files.findIndex(f => f.path === file.path);
        if (existingIndex >= 0) {
          projectContext.files[existingIndex] = file;
        } else {
          projectContext.files.push(file);
        }
      }
    } catch (error) {
      this.logger.debug('Failed to index directory', {
        dirPath,
        error: (error as Error).message
      });
    }
  }

  public getProjectContext(workingDirectory: string): ProjectContext | undefined {
    const normalizedPath = path.resolve(workingDirectory);
    return this.projectContexts.get(normalizedPath);
  }

  public async refreshProjectContext(workingDirectory: string): Promise<ProjectContext> {
    const normalizedPath = path.resolve(workingDirectory);
    
    this.logger.info(`Manually refreshing project context: ${normalizedPath}`);
    
    const projectContext = await this.projectIndexer.indexProject(normalizedPath);
    this.projectContexts.set(normalizedPath, projectContext);
    
    return projectContext;
  }

  public searchFiles(
    workingDirectory: string,
    query: string,
    options: {
      caseSensitive?: boolean;
      regex?: boolean;
      fileTypes?: string[];
      maxResults?: number;
    } = {}
  ): FileInfo[] {
    const projectContext = this.getProjectContext(workingDirectory);
    if (!projectContext) {
      return [];
    }

    const results: FileInfo[] = [];
    const maxResults = options.maxResults || 50;

    for (const file of projectContext.files) {
      if (results.length >= maxResults) {
        break;
      }

      // Filter by file type if specified
      if (options.fileTypes && options.fileTypes.length > 0) {
        const ext = path.extname(file.path).toLowerCase();
        if (!options.fileTypes.includes(ext)) {
          continue;
        }
      }

      // Search in file path
      const searchText = options.caseSensitive ? file.path : file.path.toLowerCase();
      const searchQuery = options.caseSensitive ? query : query.toLowerCase();

      let matches = false;

      if (options.regex) {
        try {
          const regex = new RegExp(query, options.caseSensitive ? 'g' : 'gi');
          matches = regex.test(file.path) || (file.content ? regex.test(file.content) : false);
        } catch {
          // Invalid regex, fall back to simple search
          matches = searchText.includes(searchQuery) ||
                   (file.content ? file.content.toLowerCase().includes(searchQuery) : false);
        }
      } else {
        matches = searchText.includes(searchQuery) ||
                 (file.content ?
                  (options.caseSensitive ? file.content : file.content.toLowerCase())
                    .includes(searchQuery) : false);
      }

      if (matches) {
        results.push(file);
      }
    }

    return results;
  }

  public getFileContent(workingDirectory: string, filePath: string): string | undefined {
    const projectContext = this.getProjectContext(workingDirectory);
    if (!projectContext) {
      return undefined;
    }

    const file = projectContext.files.find(f => f.path === filePath);
    return file?.content;
  }

  public getProjectSummary(workingDirectory: string): {
    projectType: string;
    fileCount: number;
    totalSize: number;
    languages: string[];
    dependencies: string[];
    hasGit: boolean;
  } | undefined {
    const projectContext = this.getProjectContext(workingDirectory);
    if (!projectContext) {
      return undefined;
    }

    const fileCount = projectContext.files.filter(f => f.type === 'file').length;
    const totalSize = projectContext.files.reduce((sum, f) => sum + f.size, 0);
    
    const languages = new Set<string>();
    for (const file of projectContext.files) {
      const ext = path.extname(file.path).toLowerCase();
      if (ext) {
        languages.add(ext.substring(1)); // Remove the dot
      }
    }

    const dependencies = Object.keys(projectContext.dependencies);

    return {
      projectType: projectContext.projectType,
      fileCount,
      totalSize,
      languages: Array.from(languages),
      dependencies,
      hasGit: !!projectContext.gitInfo
    };
  }

  public onContextUpdate(callback: (_context?: ProjectContext) => void): void {
    this.contextUpdateCallbacks.add(callback);
  }

  public offContextUpdate(callback: (_context?: ProjectContext) => void): void {
    this.contextUpdateCallbacks.delete(callback);
  }

  private notifyContextUpdate(context: ProjectContext): void {
    for (const callback of this.contextUpdateCallbacks) {
      try {
        callback(context);
      } catch (error) {
        this.logger.error('Context update callback failed', {
          error: (error as Error).message
        });
      }
    }
  }

  public async saveContextSnapshot(workingDirectory: string): Promise<void> {
    const context = this.getProjectContext(workingDirectory);
    if (!context) return;

    try {
      // Save to memory
      const history = this.contextHistory.get(workingDirectory) || [];
      const snapshot = JSON.parse(JSON.stringify(context)); // Deep clone
      snapshot.lastUpdated = new Date();

      history.push(snapshot);

      // Keep only last 10 snapshots
      if (history.length > 10) {
        history.shift();
      }

      this.contextHistory.set(workingDirectory, history);

      // Save to persistent storage
      const snapshotsDir = path.join(process.cwd(), '.ai-cli', 'snapshots');
      await fs.ensureDir(snapshotsDir);

      const snapshotFile = path.join(snapshotsDir, `${path.basename(workingDirectory)}-${Date.now()}.json`);
      await fs.writeJson(snapshotFile, snapshot, { spaces: 2 });

      this.logger.debug(`Context snapshot saved for: ${workingDirectory}`, {
        snapshotCount: history.length,
        snapshotFile
      });
    } catch (error) {
      this.logger.warn('Failed to save context snapshot', {
        workingDirectory,
        error: (error as Error).message
      });
    }
  }

  public getContextHistory(workingDirectory: string): ProjectContext[] {
    return this.contextHistory.get(workingDirectory) || [];
  }

  public restoreContextSnapshot(workingDirectory: string, snapshotIndex: number): boolean {
    const history = this.contextHistory.get(workingDirectory);
    if (!history || snapshotIndex < 0 || snapshotIndex >= history.length) {
      return false;
    }

    const snapshot = history[snapshotIndex];
    this.projectContexts.set(workingDirectory, snapshot);
    this.notifyContextUpdate(snapshot);

    this.logger.info(`Context restored from snapshot: ${workingDirectory}`, {
      snapshotIndex,
      snapshotDate: new Date()
    });

    return true;
  }

  public getContextDiff(workingDirectory: string, snapshotIndex: number): {
    added: string[];
    modified: string[];
    deleted: string[];
  } | null {
    const current = this.getProjectContext(workingDirectory);
    const history = this.contextHistory.get(workingDirectory);

    if (!current || !history || snapshotIndex < 0 || snapshotIndex >= history.length) {
      return null;
    }

    const snapshot = history[snapshotIndex];
    const currentFiles = new Set(current.files.map(f => f.path));
    const snapshotFiles = new Set(snapshot.files.map(f => f.path));

    const added = Array.from(currentFiles).filter(path => !snapshotFiles.has(path));
    const deleted = Array.from(snapshotFiles).filter(path => !currentFiles.has(path));

    const modified: string[] = [];
    for (const file of current.files) {
      const snapshotFile = snapshot.files.find(f => f.path === file.path);
      if (snapshotFile && file.lastModified > snapshotFile.lastModified) {
        modified.push(file.path);
      }
    }

    return { added, modified, deleted };
  }

  public async refreshContext(workingDirectory: string): Promise<void> {
    this.logger.info(`Refreshing context for: ${workingDirectory}`);

    // Save current state as snapshot
    this.saveContextSnapshot(workingDirectory);

    // Re-index the project
    await this.refreshProjectContext(workingDirectory);

    const context = this.getProjectContext(workingDirectory);
    if (context) {
      this.notifyContextUpdate(context);
    }
  }

  public getContextMetrics(workingDirectory: string): {
    fileCount: number;
    totalSize: number;
    lastUpdated: Date;
    watchedPaths: number;
    cacheHitRate: number;
  } | null {
    const context = this.getProjectContext(workingDirectory);
    if (!context) return null;

    const fileCount = context.files.filter(f => f.type === 'file').length;
    const totalSize = context.files.reduce((sum, f) => sum + f.size, 0);
    const watchedPaths = this.fileWatchers.size;

    // Simple cache hit rate calculation (would be more sophisticated in real implementation)
    const cacheHitRate = 0.85; // Placeholder

    return {
      fileCount,
      totalSize,
      lastUpdated: new Date(),
      watchedPaths,
      cacheHitRate
    };
  }

  public cleanup(): void {
    // Close all file watchers
    for (const [path, watcher] of this.fileWatchers) {
      try {
        watcher.close();
        this.logger.debug(`Closed file watcher for: ${path}`);
      } catch (error) {
        this.logger.warn(`Failed to close file watcher for: ${path}`, {
          error: (error as Error).message
        });
      }
    }

    this.fileWatchers.clear();
    this.projectContexts.clear();
    this.contextUpdateCallbacks.clear();
    this.contextHistory.clear();

    this.logger.info('Context engine cleanup completed');
  }
}
