{"name": "tsc-alias", "version": "1.8.16", "description": "Replace alias paths with relative paths after typescript compilation.", "main": "dist/index.js", "files": ["dist/"], "bin": {"tsc-alias": "dist/bin/index.js"}, "engines": {"node": ">=16.20.2"}, "scripts": {"prepare": "husky install", "build:dev": "tsc -w", "clean": "rimraf dist && tsc && pnpm link -g", "release:patch": "npm version patch", "postversion": "git push && git push --tags && npm publish", "format": "prettier --write \"**/*.{js,ts,json}\"", "test": "npm run clean && jest"}, "repository": {"type": "git", "url": "https://github.com/justkey007/tsc-alias.git"}, "keywords": ["typescript", "paths", "alias", "absolute path", "relative path", "compilation", "tsc"], "author": "Juskey B.", "license": "MIT", "bugs": {"url": "https://github.com/justkey007/tsc-alias/issues"}, "homepage": "https://github.com/justkey007/tsc-alias.git", "dependencies": {"chokidar": "^3.5.3", "commander": "^9.0.0", "get-tsconfig": "^4.10.0", "globby": "^11.0.4", "mylas": "^2.1.9", "normalize-path": "^3.0.0", "plimit-lit": "^1.2.6"}, "devDependencies": {"@types/jest": "^27.4.0", "@types/node": "^16.18.126", "@types/normalize-path": "^3.0.2", "@types/rimraf": "^3.0.2", "@types/shelljs": "^0.8.11", "husky": "^7.0.4", "jest": "^27.5.1", "prettier": "^2.5.1", "rimraf": "^6.0.1", "shelljs": "^0.8.5", "ts-jest": "^27.1.3", "typescript": "^4.9.5"}}