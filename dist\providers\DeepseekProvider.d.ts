import { LL<PERSON>rovider, LLMOptions, LLMResponse, <PERSON>l<PERSON>all, <PERSON>lResult, AgentContext } from '../types';
export declare class DeepseekProvider implements LLMProvider {
    readonly name = "deepseek";
    private client;
    private logger;
    private toolRegistry;
    constructor(config: Record<string, unknown>);
    generateResponse(prompt: string, options?: LLMOptions): Promise<LLMResponse>;
    generateStreamResponse(prompt: string, options?: LLMOptions): AsyncGenerator<string, void, unknown>;
    supportsToolCalling(): boolean;
    callTool(toolCall: ToolCall, context: AgentContext): Promise<ToolResult>;
    private buildMessages;
    validateApiKey(): Promise<boolean>;
    getAvailableModels(): Promise<string[]>;
    getDefaultModel(): string;
    getMaxTokens(model?: string): number;
    estimateTokens(text: string): number;
    calculateCost(usage: {
        promptTokens: number;
        completionTokens: number;
    }, model?: string): number;
}
//# sourceMappingURL=DeepseekProvider.d.ts.map