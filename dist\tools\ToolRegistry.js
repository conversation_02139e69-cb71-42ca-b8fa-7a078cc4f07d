import fs from 'fs-extra';
import path from 'path';
import { ToolExecutionError } from '../utils/ErrorHandler';
import { Logger } from '../utils/Logger';
import { ShellTool } from './ShellTool';
import { FileTool } from './FileTool';
export class ToolRegistry {
    static instance;
    tools;
    logger;
    executionQueue;
    toolStats;
    statsFile;
    constructor() {
        this.tools = new Map();
        this.logger = Logger.getInstance();
        this.executionQueue = new Map();
        this.toolStats = new Map();
        this.statsFile = path.join(process.cwd(), '.ai-cli', 'tool-stats.json');
        this.loadToolStats();
        this.registerDefaultTools();
    }
    static getInstance() {
        if (!ToolRegistry.instance) {
            ToolRegistry.instance = new ToolRegistry();
        }
        return ToolRegistry.instance;
    }
    async loadToolStats() {
        try {
            if (await fs.pathExists(this.statsFile)) {
                const statsData = await fs.readJson(this.statsFile);
                this.toolStats = new Map(Object.entries(statsData));
            }
        }
        catch (error) {
            this.logger.debug('Failed to load tool statistics', {
                error: error.message
            });
        }
    }
    async saveToolStats() {
        try {
            await fs.ensureDir(path.dirname(this.statsFile));
            const statsObject = Object.fromEntries(this.toolStats);
            await fs.writeJson(this.statsFile, statsObject, { spaces: 2 });
        }
        catch (error) {
            this.logger.debug('Failed to save tool statistics', {
                error: error.message
            });
        }
    }
    updateToolStats(toolName, success, duration) {
        if (!this.toolStats.has(toolName)) {
            this.toolStats.set(toolName, { calls: 0, successes: 0, failures: 0, totalDuration: 0 });
        }
        const stats = this.toolStats.get(toolName);
        stats.calls++;
        stats.totalDuration += duration;
        if (success) {
            stats.successes++;
        }
        else {
            stats.failures++;
        }
        // Save stats periodically
        if (stats.calls % 10 === 0) {
            this.saveToolStats().catch(() => {
                // Ignore save errors
            });
        }
    }
    registerDefaultTools() {
        this.registerTool(new ShellTool());
        this.registerTool(new FileTool());
    }
    registerTool(tool) {
        this.tools.set(tool.name, tool);
        this.logger.info(`Tool registered: ${tool.name}`, {
            toolName: tool.name,
            description: tool.description
        });
    }
    unregisterTool(toolName) {
        const removed = this.tools.delete(toolName);
        if (removed) {
            this.logger.info(`Tool unregistered: ${toolName}`);
        }
        return removed;
    }
    getTool(toolName) {
        return this.tools.get(toolName);
    }
    getAllTools() {
        return Array.from(this.tools.values());
    }
    getToolNames() {
        return Array.from(this.tools.keys());
    }
    hasToolCalling() {
        return this.tools.size > 0;
    }
    async executeTool(toolCall, context) {
        const tool = this.getTool(toolCall.name);
        if (!tool) {
            throw new ToolExecutionError(toolCall.name, `Tool not found: ${toolCall.name}`, { availableTools: this.getToolNames() });
        }
        try {
            this.logger.info(`Executing tool: ${toolCall.name}`, {
                toolName: toolCall.name,
                arguments: toolCall.arguments,
                toolCallId: toolCall.id
            });
            const startTime = Date.now();
            const result = await tool.execute(toolCall.arguments, context);
            const duration = Date.now() - startTime;
            this.logger.info(`Tool execution completed: ${toolCall.name}`, {
                toolName: toolCall.name,
                success: result.success,
                duration,
                toolCallId: toolCall.id
            });
            // Update statistics
            this.updateToolStats(toolCall.name, result.success, duration);
            return {
                ...result,
                metadata: {
                    ...result.metadata,
                    toolCallId: toolCall.id,
                    executionTime: duration
                }
            };
        }
        catch (error) {
            this.logger.error(`Tool execution failed: ${toolCall.name}`, {
                toolName: toolCall.name,
                error: error.message,
                arguments: toolCall.arguments,
                toolCallId: toolCall.id
            });
            if (error instanceof ToolExecutionError) {
                throw error;
            }
            throw new ToolExecutionError(toolCall.name, error.message, {
                arguments: toolCall.arguments,
                toolCallId: toolCall.id
            });
        }
    }
    async executeToolsParallel(toolCalls, context, maxParallel = 3) {
        if (toolCalls.length === 0) {
            return [];
        }
        this.logger.info(`Executing ${toolCalls.length} tools in parallel`, {
            toolCalls: toolCalls.map(tc => ({ name: tc.name, id: tc.id })),
            maxParallel
        });
        const results = [];
        const executing = [];
        for (let i = 0; i < toolCalls.length; i++) {
            const toolCall = toolCalls[i];
            // Wait if we've reached the parallel limit
            if (executing.length >= maxParallel) {
                const completed = await Promise.race(executing);
                results[completed.index] = completed.result;
                // Remove completed promise from executing array
                const completedIndex = executing.findIndex(p => p === Promise.resolve(completed));
                if (completedIndex !== -1) {
                    executing.splice(completedIndex, 1);
                }
            }
            // Start new tool execution
            const executionPromise = this.executeTool(toolCall, context)
                .then(result => ({ index: i, result }))
                .catch(error => ({
                index: i,
                result: {
                    success: false,
                    result: null,
                    error: error.message,
                    metadata: { toolCallId: toolCall.id }
                }
            }));
            executing.push(executionPromise);
        }
        // Wait for all remaining executions to complete
        const remainingResults = await Promise.all(executing);
        for (const { index, result } of remainingResults) {
            results[index] = result;
        }
        this.logger.info(`Parallel tool execution completed`, {
            totalTools: toolCalls.length,
            successfulTools: results.filter(r => r.success).length,
            failedTools: results.filter(r => !r.success).length
        });
        return results;
    }
    async executeToolsSequential(toolCalls, context) {
        if (toolCalls.length === 0) {
            return [];
        }
        this.logger.info(`Executing ${toolCalls.length} tools sequentially`, {
            toolCalls: toolCalls.map(tc => ({ name: tc.name, id: tc.id }))
        });
        const results = [];
        for (const toolCall of toolCalls) {
            try {
                const result = await this.executeTool(toolCall, context);
                results.push(result);
                // If a tool fails and it's critical, we might want to stop
                if (!result.success && this.isCriticalTool(toolCall.name)) {
                    this.logger.warn(`Critical tool failed, stopping sequential execution: ${toolCall.name}`);
                    break;
                }
            }
            catch (error) {
                const errorResult = {
                    success: false,
                    result: null,
                    error: error.message,
                    metadata: { toolCallId: toolCall.id }
                };
                results.push(errorResult);
                // Stop on critical tool failure
                if (this.isCriticalTool(toolCall.name)) {
                    this.logger.warn(`Critical tool failed, stopping sequential execution: ${toolCall.name}`);
                    break;
                }
            }
        }
        this.logger.info(`Sequential tool execution completed`, {
            totalTools: toolCalls.length,
            executedTools: results.length,
            successfulTools: results.filter(r => r.success).length,
            failedTools: results.filter(r => !r.success).length
        });
        return results;
    }
    isCriticalTool(toolName) {
        // Define which tools are considered critical
        // If they fail, subsequent tools might not work properly
        const criticalTools = ['shell', 'file'];
        return criticalTools.includes(toolName);
    }
    getToolSchema(toolName) {
        const tool = this.getTool(toolName);
        if (!tool) {
            return null;
        }
        return {
            type: 'function',
            function: {
                name: tool.name,
                description: tool.description,
                parameters: tool.parameters
            }
        };
    }
    getAllToolSchemas() {
        return this.getAllTools().map(tool => this.getToolSchema(tool.name));
    }
    validateToolCall(toolCall) {
        const tool = this.getTool(toolCall.name);
        if (!tool) {
            return false;
        }
        // Basic validation - check if required parameters are present
        const required = tool.parameters.required || [];
        for (const param of required) {
            if (!(param in toolCall.arguments)) {
                this.logger.warn(`Missing required parameter: ${param} for tool: ${toolCall.name}`);
                return false;
            }
        }
        return true;
    }
    getToolUsageStats() {
        const stats = {};
        for (const toolName of this.getToolNames()) {
            const toolStat = this.toolStats.get(toolName);
            if (toolStat) {
                stats[toolName] = {
                    calls: toolStat.calls,
                    successes: toolStat.successes,
                    failures: toolStat.failures,
                    averageDuration: toolStat.calls > 0 ? toolStat.totalDuration / toolStat.calls : 0
                };
            }
            else {
                stats[toolName] = { calls: 0, successes: 0, failures: 0, averageDuration: 0 };
            }
        }
        return stats;
    }
    async executeToolWithRetry(toolCall, context, maxRetries = 2) {
        let lastError = null;
        for (let attempt = 1; attempt <= maxRetries + 1; attempt++) {
            try {
                const result = await this.executeTool(toolCall, context);
                if (result.success || attempt === maxRetries + 1) {
                    return result;
                }
                // If not successful but not the last attempt, retry
                this.logger.warn(`Tool execution failed, retrying (${attempt}/${maxRetries + 1}): ${toolCall.name}`, {
                    error: result.error,
                    attempt
                });
                // Wait before retry with exponential backoff
                await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt - 1) * 1000));
            }
            catch (error) {
                lastError = error;
                if (attempt === maxRetries + 1) {
                    throw error;
                }
                this.logger.warn(`Tool execution error, retrying (${attempt}/${maxRetries + 1}): ${toolCall.name}`, {
                    error: error.message,
                    attempt
                });
                // Wait before retry with exponential backoff
                await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt - 1) * 1000));
            }
        }
        throw lastError || new ToolExecutionError(toolCall.name, 'Max retries exceeded');
    }
    analyzeDependencies(toolCalls) {
        // Simple dependency analysis - group tools that can run in parallel
        const groups = [];
        const fileOperations = [];
        const shellOperations = [];
        const independentOperations = [];
        for (const toolCall of toolCalls) {
            if (toolCall.name === 'file') {
                // File operations might depend on each other
                const operation = toolCall.arguments.operation;
                if (['read', 'exists', 'stat', 'search', 'glob'].includes(operation)) {
                    // Read operations can run in parallel
                    independentOperations.push(toolCall);
                }
                else {
                    // Write operations should be sequential
                    fileOperations.push(toolCall);
                }
            }
            else if (toolCall.name === 'shell') {
                // Shell operations might have dependencies
                shellOperations.push(toolCall);
            }
            else {
                // Other tools can run independently
                independentOperations.push(toolCall);
            }
        }
        // Add groups in dependency order
        if (independentOperations.length > 0) {
            groups.push(independentOperations);
        }
        if (fileOperations.length > 0) {
            groups.push(fileOperations);
        }
        if (shellOperations.length > 0) {
            groups.push(shellOperations);
        }
        return groups.length > 0 ? groups : [toolCalls];
    }
    createBatches(toolCalls, maxParallel) {
        const batches = [];
        for (let i = 0; i < toolCalls.length; i += maxParallel) {
            batches.push(toolCalls.slice(i, i + maxParallel));
        }
        return batches;
    }
    async executeToolChain(toolCalls, context, options = {}) {
        const { stopOnFailure = false, maxParallel = 3, enableRetry = true, enableLearning = false, adaptiveExecution = false } = options;
        this.logger.info(`Executing enhanced tool chain with ${toolCalls.length} tools`, {
            stopOnFailure,
            maxParallel,
            enableRetry,
            enableLearning,
            adaptiveExecution
        });
        const results = [];
        let dependencyGroups = this.analyzeDependencies(toolCalls);
        // Adaptive execution: reorder based on previous success rates
        if (adaptiveExecution) {
            dependencyGroups = this.optimizeExecutionOrder(dependencyGroups);
        }
        for (const group of dependencyGroups) {
            const batches = this.createBatches(group, maxParallel);
            for (const batch of batches) {
                const batchPromises = batch.map(toolCall => enableRetry ?
                    this.executeToolWithRetry(toolCall, context, 2) :
                    this.executeTool(toolCall, context));
                const batchResults = await Promise.allSettled(batchPromises);
                for (let i = 0; i < batchResults.length; i++) {
                    const result = batchResults[i];
                    const toolCall = batch[i];
                    if (result.status === 'fulfilled') {
                        results.push(result.value);
                        // Learning: update success patterns
                        if (enableLearning) {
                            this.updateSuccessPatterns(toolCall, result.value);
                        }
                    }
                    else {
                        const failedResult = {
                            success: false,
                            result: null,
                            error: result.reason?.message || 'Tool execution failed',
                            metadata: {
                                toolName: toolCall.name,
                                toolCallId: toolCall.id,
                                failureReason: result.reason?.message
                            }
                        };
                        results.push(failedResult);
                        // Learning: update failure patterns
                        if (enableLearning) {
                            this.updateFailurePatterns(toolCall, result.reason);
                        }
                        if (stopOnFailure) {
                            this.logger.error(`Tool chain stopped due to failure: ${toolCall.name}`);
                            return results;
                        }
                    }
                }
            }
        }
        // Post-execution analysis for learning
        if (enableLearning) {
            this.analyzeExecutionPatterns(toolCalls, results);
        }
        return results;
    }
    optimizeExecutionOrder(dependencyGroups) {
        // Sort groups by success rate and execution time
        return dependencyGroups.map(group => {
            return group.sort((a, b) => {
                const statsA = this.toolStats.get(a.name);
                const statsB = this.toolStats.get(b.name);
                if (!statsA && !statsB)
                    return 0;
                if (!statsA)
                    return 1;
                if (!statsB)
                    return -1;
                const successRateA = statsA.calls > 0 ? statsA.successes / statsA.calls : 0;
                const successRateB = statsB.calls > 0 ? statsB.successes / statsB.calls : 0;
                // Prioritize higher success rate, then lower average duration
                if (successRateA !== successRateB) {
                    return successRateB - successRateA;
                }
                const avgDurationA = statsA.calls > 0 ? statsA.totalDuration / statsA.calls : 0;
                const avgDurationB = statsB.calls > 0 ? statsB.totalDuration / statsB.calls : 0;
                return avgDurationA - avgDurationB;
            });
        });
    }
    updateSuccessPatterns(toolCall, result) {
        // Store successful patterns for learning
        const pattern = {
            toolName: toolCall.name,
            arguments: toolCall.arguments,
            result: result.result,
            timestamp: Date.now(),
            success: true
        };
        this.logger.debug('Recording success pattern', {
            toolName: toolCall.name,
            pattern
        });
    }
    updateFailurePatterns(toolCall, error) {
        // Store failure patterns for learning
        const pattern = {
            toolName: toolCall.name,
            arguments: toolCall.arguments,
            error: error?.message || 'Unknown error',
            timestamp: Date.now(),
            success: false
        };
        this.logger.debug('Recording failure pattern', {
            toolName: toolCall.name,
            pattern
        });
    }
    analyzeExecutionPatterns(toolCalls, results) {
        // Analyze patterns for future optimization
        const successRate = results.filter(r => r.success).length / results.length;
        const totalDuration = results.reduce((sum, r) => sum + (r.metadata?.executionTime || 0), 0);
        this.logger.info('Execution pattern analysis', {
            totalTools: toolCalls.length,
            successRate: `${(successRate * 100).toFixed(1)}%`,
            totalDuration,
            averageDuration: totalDuration / results.length
        });
    }
    async executeAutonomousWorkflow(workflow, context) {
        const startTime = Date.now();
        const { steps, conditions = {} } = workflow;
        const { continueOnFailure = false, maxRetries = 2, timeout = 300000 // 5 minutes
         } = conditions;
        this.logger.info(`Starting autonomous workflow: ${workflow.name}`, {
            workflowName: workflow.name,
            stepCount: steps.length,
            conditions
        });
        const results = [];
        let stepsCompleted = 0;
        try {
            // Set timeout for the entire workflow
            const timeoutPromise = new Promise((_, reject) => {
                setTimeout(() => reject(new Error('Workflow timeout')), timeout);
            });
            const workflowPromise = (async () => {
                for (const step of steps) {
                    let stepResult = null;
                    let retries = 0;
                    while (retries <= maxRetries) {
                        try {
                            stepResult = await this.executeTool(step, context);
                            if (stepResult.success) {
                                break; // Success, move to next step
                            }
                            else if (!continueOnFailure && retries === maxRetries) {
                                throw new Error(`Step failed: ${step.name} - ${stepResult.error}`);
                            }
                        }
                        catch (error) {
                            if (!continueOnFailure && retries === maxRetries) {
                                throw error;
                            }
                            this.logger.warn(`Step retry ${retries + 1}/${maxRetries + 1}`, {
                                stepName: step.name,
                                error: error.message
                            });
                        }
                        retries++;
                    }
                    if (stepResult) {
                        results.push(stepResult);
                        stepsCompleted++;
                        if (!stepResult.success && !continueOnFailure) {
                            break;
                        }
                    }
                }
            })();
            await Promise.race([workflowPromise, timeoutPromise]);
        }
        catch (error) {
            this.logger.error(`Autonomous workflow failed: ${workflow.name}`, {
                error: error.message,
                stepsCompleted,
                totalSteps: steps.length
            });
        }
        const executionTime = Date.now() - startTime;
        const success = stepsCompleted === steps.length && results.every(r => r.success);
        this.logger.info(`Autonomous workflow completed: ${workflow.name}`, {
            success,
            stepsCompleted,
            totalSteps: steps.length,
            executionTime
        });
        return {
            success,
            results,
            executionTime,
            stepsCompleted
        };
    }
}
//# sourceMappingURL=ToolRegistry.js.map