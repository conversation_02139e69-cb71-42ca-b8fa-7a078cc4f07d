import { ProjectContext, AgentContext, FileInfo } from '../types';
export declare class ContextEngine {
    private static instance;
    private projectContexts;
    private fileWatchers;
    private logger;
    private configManager;
    private projectIndexer;
    private toolRegistry;
    private contextUpdateCallbacks;
    private contextHistory;
    private constructor();
    static getInstance(): ContextEngine;
    createAgentContext(sessionId: string, workingDirectory: string): Promise<AgentContext>;
    private getOrCreateProjectContext;
    private shouldRefreshContext;
    private setupFileWatching;
    private handleFileChange;
    private indexDirectory;
    getProjectContext(workingDirectory: string): ProjectContext | undefined;
    refreshProjectContext(workingDirectory: string): Promise<ProjectContext>;
    searchFiles(workingDirectory: string, query: string, options?: {
        caseSensitive?: boolean;
        regex?: boolean;
        fileTypes?: string[];
        maxResults?: number;
    }): FileInfo[];
    getFileContent(workingDirectory: string, filePath: string): string | undefined;
    getProjectSummary(workingDirectory: string): {
        projectType: string;
        fileCount: number;
        totalSize: number;
        languages: string[];
        dependencies: string[];
        hasGit: boolean;
    } | undefined;
    onContextUpdate(callback: (_context?: ProjectContext) => void): void;
    offContextUpdate(callback: (_context?: ProjectContext) => void): void;
    private notifyContextUpdate;
    saveContextSnapshot(workingDirectory: string): Promise<void>;
    getContextHistory(workingDirectory: string): ProjectContext[];
    restoreContextSnapshot(workingDirectory: string, snapshotIndex: number): boolean;
    getContextDiff(workingDirectory: string, snapshotIndex: number): {
        added: string[];
        modified: string[];
        deleted: string[];
    } | null;
    refreshContext(workingDirectory: string): Promise<void>;
    getContextMetrics(workingDirectory: string): {
        fileCount: number;
        totalSize: number;
        lastUpdated: Date;
        watchedPaths: number;
        cacheHitRate: number;
    } | null;
    cleanup(): void;
}
//# sourceMappingURL=ContextEngine.d.ts.map