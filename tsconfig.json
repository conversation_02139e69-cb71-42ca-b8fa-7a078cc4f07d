{"compilerOptions": {"target": "ES2022", "module": "ES2022", "lib": ["ES2022"], "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "declaration": true, "declarationMap": true, "sourceMap": true, "removeComments": false, "noImplicitAny": true, "noImplicitReturns": true, "noImplicitThis": true, "noUnusedLocals": false, "noUnusedParameters": false, "exactOptionalPropertyTypes": false, "noImplicitOverride": false, "noPropertyAccessFromIndexSignature": false, "noUncheckedIndexedAccess": false, "allowUnusedLabels": false, "allowUnreachableCode": false, "resolveJsonModule": true, "moduleResolution": "node", "baseUrl": "./src", "paths": {"@/*": ["*"], "@/agents/*": ["agents/*"], "@/tools/*": ["tools/*"], "@/providers/*": ["providers/*"], "@/session/*": ["session/*"], "@/context/*": ["context/*"], "@/config/*": ["config/*"], "@/utils/*": ["utils/*"], "@/types/*": ["types/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"]}