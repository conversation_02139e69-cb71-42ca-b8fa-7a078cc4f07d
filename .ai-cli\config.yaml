agent:
  provider: openai
  model: gpt-4
  temperature: 0.7
  maxTokens: 4000
  enableToolCalling: true
  enableParallelExecution: true
  maxParallelTools: 3
  sessionPersistence: true
  contextIndexing: true
  autoDiscovery: true
providers:
  openai:
    baseURL: https://api.openai.com/v1
  anthropic:
    baseURL: https://api.anthropic.com
  deepseek:
    baseURL: https://api.deepseek.com/v1
  ollama:
    baseURL: http://localhost:11434
    timeout: 30000
session:
  persistenceEnabled: true
  maxSessions: 50
  sessionTimeout: 86400000
  autoCleanup: true
context:
  indexingEnabled: true
  maxFileSize: 1048576
  excludePatterns:
    - node_modules/**
    - .git/**
    - dist/**
    - build/**
    - "*.log"
    - .env*
    - "*.tmp"
    - "*.temp"
  includePatterns:
    - "**/*.ts"
    - "**/*.js"
    - "**/*.py"
    - "**/*.java"
    - "**/*.cpp"
    - "**/*.c"
    - "**/*.h"
    - "**/*.json"
    - "**/*.yaml"
    - "**/*.yml"
    - "**/*.md"
    - "**/*.txt"
  watchForChanges: true
logging:
  level: info
  enableFileLogging: true
  maxLogFiles: 10
  maxLogSize: 5MB
