{"level":"info","message":"Loaded 0 existing sessions","timestamp":"2025-06-01T06:51:11.177Z"}
{"level":"info","message":"Tool registered: shell","timestamp":"2025-06-01T06:51:11.180Z","metadata":{"toolName":"shell","description":"Execute shell commands with full system access"}}
{"level":"info","message":"Tool registered: file","timestamp":"2025-06-01T06:51:11.180Z","metadata":{"toolName":"file","description":"Comprehensive file operations including read, write, search, and manipulation"}}
{"level":"error","message":"Ollama API request failed","timestamp":"2025-06-01T06:51:11.219Z","metadata":{"error":"request to http://localhost:11434/api/chat failed, reason: ","prompt":"Hello, this is a test message. Please respond with \"Test successful\"....","baseURL":"http://localhost:11434"}}
{"level":"error","message":"Provider test failed: ollama","timestamp":"2025-06-01T06:51:11.220Z","metadata":{"provider":"ollama","error":"LLM Provider error: ollama - request to http://localhost:11434/api/chat failed, reason: "}}
{"level":"error","message":"Failed to create LLM provider: ollama","timestamp":"2025-06-01T06:51:11.220Z","metadata":{"error":"LLM Provider error: ollama - Provider test failed: LLM Provider error: ollama - request to http://localhost:11434/api/chat failed, reason: ","provider":"ollama"}}
{"level":"info","message":"Loaded 0 existing sessions","timestamp":"2025-06-01T06:51:20.598Z"}
{"level":"info","message":"Tool registered: shell","timestamp":"2025-06-01T06:51:20.601Z","metadata":{"toolName":"shell","description":"Execute shell commands with full system access"}}
{"level":"info","message":"Tool registered: file","timestamp":"2025-06-01T06:51:20.601Z","metadata":{"toolName":"file","description":"Comprehensive file operations including read, write, search, and manipulation"}}
