import { IConfig, IOutput, ITSConfig, ReplaceTscAliasPathsOptions } from '../interfaces';
export declare function prepareConfig(options: ReplaceTscAliasPathsOptions): Promise<IConfig>;
export declare const loadConfig: (file: string, output: IOutput, baseConfigDir?: string | null) => ITSConfig;
export declare function normalizeTsConfigExtendsOption(ext: string | string[], file: string): string[];
export declare function resolveTsConfigExtendsPath(ext: string, file: string): string;
