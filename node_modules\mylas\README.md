# Mylas 
![npm](https://img.shields.io/npm/dt/mylas)
![install size](https://badgen.net/packagephobia/install/mylas)
![npm bundle size (scoped)](https://img.shields.io/bundlephobia/min/mylas)
![npm bundle size (scoped)](https://img.shields.io/bundlephobia/minzip/mylas)
![dependencie count](https://img.shields.io/badge/dependencies-0-brightgreen)
![GitHub](https://img.shields.io/github/license/raouldeheer/Mylas)
![npm (scoped)](https://img.shields.io/npm/v/mylas)
![node-current (scoped)](https://img.shields.io/node/v/mylas)  
Mylas is a npm package that makes the loading and storing of data from fs easy and reliable. And it supports multithreading, json with comments and loading buffers with multithreading. It can also find [node_modules](https://github.com/raouldeheer/Mylas/wiki/Dir#nodemodules) folders.   
<img src="https://raw.githubusercontent.com/raouldeheer/Mylas/HEAD/.github/logo.png" width="600"> 

## Installation
Install Mylas from NPM
```ts
npm i mylas
// Or
yarn add mylas
```

## Getting started

To get started have a look at our [wiki](https://github.com/raouldeheer/Mylas/wiki) 

## Examples

- [Json examples](https://github.com/raouldeheer/Mylas/wiki/Json)
- [File examples](https://github.com/raouldeheer/Mylas/wiki/File)
- [Buf examples](https://github.com/raouldeheer/Mylas/wiki/Buf)
- [Dir examples](https://github.com/raouldeheer/Mylas/wiki/Dir)

## Documentation
All features are listed at the [wiki](https://github.com/raouldeheer/Mylas/wiki)   

## Contributing
We would love more contributors! To start contributing read our [Contributing page](https://github.com/raouldeheer/Mylas/blob/main/.github/CONTRIBUTING.md).  

## Supported Versions
Check for supported versions at the [security policy](https://github.com/raouldeheer/Mylas/security/policy).  
