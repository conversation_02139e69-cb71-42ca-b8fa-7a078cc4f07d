import winston from 'winston';
import path from 'path';
import fs from 'fs-extra';
export class Logger {
    static instance;
    logger;
    sessionId;
    constructor() {
        this.initializeLogger();
    }
    static getInstance() {
        if (!Logger.instance) {
            Logger.instance = new Logger();
        }
        return Logger.instance;
    }
    initializeLogger() {
        const logsDir = path.join(process.cwd(), '.ai-cli', 'logs');
        fs.ensureDirSync(logsDir);
        const logFormat = winston.format.combine(winston.format.timestamp(), winston.format.errors({ stack: true }), winston.format.json(), winston.format.printf(({ timestamp, level, message, sessionId, ...meta }) => {
            const logEntry = {
                level: level,
                message: String(message),
                timestamp: new Date(String(timestamp)),
                sessionId: sessionId ? String(sessionId) : undefined,
                metadata: Object.keys(meta).length > 0 ? meta : undefined
            };
            return JSON.stringify(logEntry);
        }));
        this.logger = winston.createLogger({
            level: process.env.LOG_LEVEL || 'info',
            format: logFormat,
            transports: [
                new winston.transports.File({
                    filename: path.join(logsDir, 'error.log'),
                    level: 'error',
                    maxsize: 5242880, // 5MB
                    maxFiles: 5
                }),
                new winston.transports.File({
                    filename: path.join(logsDir, 'combined.log'),
                    maxsize: 5242880, // 5MB
                    maxFiles: 10
                }),
                new winston.transports.Console({
                    format: winston.format.combine(winston.format.colorize(), winston.format.simple(), winston.format.printf(({ level, message, timestamp }) => {
                        return `${timestamp} [${level}]: ${message}`;
                    }))
                })
            ]
        });
    }
    setSessionId(sessionId) {
        this.sessionId = sessionId;
    }
    error(message, metadata) {
        this.logger.error(message, { sessionId: this.sessionId, ...metadata });
    }
    warn(message, metadata) {
        this.logger.warn(message, { sessionId: this.sessionId, ...metadata });
    }
    info(message, metadata) {
        this.logger.info(message, { sessionId: this.sessionId, ...metadata });
    }
    debug(message, metadata) {
        this.logger.debug(message, { sessionId: this.sessionId, ...metadata });
    }
    verbose(message, metadata) {
        this.logger.verbose(message, { sessionId: this.sessionId, ...metadata });
    }
    logToolExecution(toolName, args, result, duration) {
        this.info(`Tool executed: ${toolName}`, {
            tool: toolName,
            arguments: args,
            result,
            duration,
            type: 'tool_execution'
        });
    }
    logLLMCall(provider, model, prompt, response, usage) {
        this.info(`LLM call: ${provider}/${model}`, {
            provider,
            model,
            promptLength: prompt.length,
            responseLength: response.length,
            usage,
            type: 'llm_call'
        });
    }
    logSessionEvent(event, metadata) {
        this.info(`Session event: ${event}`, {
            event,
            sessionId: this.sessionId,
            type: 'session_event',
            ...metadata
        });
    }
    logError(error, context) {
        this.error(`${context ? `${context}: ` : ''}${error.message}`, {
            error: {
                name: error.name,
                message: error.message,
                stack: error.stack
            },
            context,
            type: 'error'
        });
    }
}
//# sourceMappingURL=Logger.js.map