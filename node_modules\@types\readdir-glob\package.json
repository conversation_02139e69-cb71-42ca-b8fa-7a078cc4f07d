{"name": "@types/readdir-glob", "version": "1.1.5", "description": "TypeScript definitions for readdir-glob", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/readdir-glob", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/dolanmiu"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/readdir-glob"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "7bdc6e6f815f6791c4762b917d4437d2172faa334bd34f1ce9eb5307ecc565f1", "typeScriptVersion": "4.5"}