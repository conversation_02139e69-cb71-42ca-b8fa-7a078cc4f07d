import { Logger } from './Logger';
export class AI<PERSON>liError extends <PERSON>rro<PERSON> {
    code;
    context;
    constructor(message, code, context) {
        super(message);
        this.name = 'AICliError';
        this.code = code;
        this.context = context;
    }
}
export class ToolExecutionError extends AICliError {
    constructor(toolName, message, context) {
        super(`Tool execution failed: ${toolName} - ${message}`, 'TOOL_EXECUTION_ERROR', {
            toolName,
            ...context
        });
    }
}
export class LLMProviderError extends AICliError {
    constructor(provider, message, context) {
        super(`LLM Provider error: ${provider} - ${message}`, 'LLM_PROVIDER_ERROR', {
            provider,
            ...context
        });
    }
}
export class SessionError extends AICliError {
    constructor(message, context) {
        super(`Session error: ${message}`, 'SESSION_ERROR', context);
    }
}
export class ConfigurationError extends AICliError {
    constructor(message, context) {
        super(`Configuration error: ${message}`, 'CONFIGURATION_ERROR', context);
    }
}
export class FileOperationError extends AI<PERSON>liError {
    constructor(operation, path, message, context) {
        super(`File operation failed: ${operation} on ${path} - ${message}`, 'FILE_OPERATION_ERROR', {
            operation,
            path,
            ...context
        });
    }
}
export class ShellExecutionError extends AICliError {
    constructor(command, exitCode, stderr, context) {
        super(`Shell command failed: ${command} (exit code: ${exitCode})`, 'SHELL_EXECUTION_ERROR', {
            command,
            exitCode,
            stderr,
            ...context
        });
    }
}
export class ErrorHandler {
    static instance;
    logger;
    constructor() {
        this.logger = Logger.getInstance();
    }
    static getInstance() {
        if (!ErrorHandler.instance) {
            ErrorHandler.instance = new ErrorHandler();
        }
        return ErrorHandler.instance;
    }
    handleError(error, context) {
        this.logger.logError(error, context);
        if (error instanceof AICliError) {
            this.handleAICliError(error);
        }
        else {
            this.handleGenericError(error, context);
        }
    }
    handleAICliError(error) {
        switch (error.code) {
            case 'TOOL_EXECUTION_ERROR':
                console.error(`🔧 Tool Error: ${error.message}`);
                if (error.context?.toolName) {
                    console.error(`   Tool: ${error.context.toolName}`);
                }
                break;
            case 'LLM_PROVIDER_ERROR':
                console.error(`🤖 LLM Provider Error: ${error.message}`);
                if (error.context?.provider) {
                    console.error(`   Provider: ${error.context.provider}`);
                }
                break;
            case 'SESSION_ERROR':
                console.error(`📝 Session Error: ${error.message}`);
                break;
            case 'CONFIGURATION_ERROR':
                console.error(`⚙️  Configuration Error: ${error.message}`);
                break;
            case 'FILE_OPERATION_ERROR':
                console.error(`📁 File Operation Error: ${error.message}`);
                if (error.context?.operation && error.context?.path) {
                    console.error(`   Operation: ${error.context.operation} on ${error.context.path}`);
                }
                break;
            case 'SHELL_EXECUTION_ERROR':
                console.error(`💻 Shell Execution Error: ${error.message}`);
                if (error.context?.command) {
                    console.error(`   Command: ${error.context.command}`);
                }
                if (error.context?.stderr) {
                    console.error(`   Error Output: ${error.context.stderr}`);
                }
                break;
            default:
                console.error(`❌ Error: ${error.message}`);
        }
        if (process.env.NODE_ENV === 'development' && error.context) {
            console.error('   Context:', JSON.stringify(error.context, null, 2));
        }
    }
    handleGenericError(error, context) {
        console.error(`❌ Unexpected Error: ${error.message}`);
        if (context) {
            console.error(`   Context: ${context}`);
        }
        if (process.env.NODE_ENV === 'development') {
            console.error('   Stack:', error.stack);
        }
    }
    async handleAsyncError(operation, context) {
        try {
            return await operation();
        }
        catch (error) {
            this.handleError(error, context);
            return null;
        }
    }
    handleSyncError(operation, context) {
        try {
            return operation();
        }
        catch (error) {
            this.handleError(error, context);
            return null;
        }
    }
    createGracefulShutdown() {
        process.on('uncaughtException', (error) => {
            this.logger.error('Uncaught Exception', { error: error.message, stack: error.stack });
            console.error('💥 Uncaught Exception:', error.message);
            process.exit(1);
        });
        process.on('unhandledRejection', (reason, promise) => {
            this.logger.error('Unhandled Rejection', { reason, promise });
            console.error('💥 Unhandled Rejection:', reason);
            process.exit(1);
        });
        process.on('SIGINT', () => {
            this.logger.info('Received SIGINT, shutting down gracefully');
            console.log('\n👋 Shutting down gracefully...');
            process.exit(0);
        });
        process.on('SIGTERM', () => {
            this.logger.info('Received SIGTERM, shutting down gracefully');
            console.log('\n👋 Shutting down gracefully...');
            process.exit(0);
        });
    }
}
//# sourceMappingURL=ErrorHandler.js.map