{"name": "@types/unzipper", "version": "0.10.11", "description": "TypeScript definitions for unzipper", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/unzipper", "license": "MIT", "contributors": [{"name": "s73obrien", "githubUsername": "s73obrien", "url": "https://github.com/s73obrien"}, {"name": "<PERSON>", "githubUsername": "bartje321", "url": "https://github.com/bartje321"}, {"name": "<PERSON>", "githubUsername": "ken<PERSON>", "url": "https://github.com/kenhuman"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/unzipper"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "peerDependencies": {}, "typesPublisherContentHash": "7412f33ee3fbe532f1ae06c8958ef493820d6cde131f124c0e0484f57b3fb6e0", "typeScriptVersion": "5.0"}