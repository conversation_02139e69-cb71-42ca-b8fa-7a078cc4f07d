import { Session, AgentContext, Message } from '../types';
export declare class SessionManager {
    private static instance;
    private sessions;
    private currentSession;
    private sessionsDir;
    private logger;
    private configManager;
    private constructor();
    static getInstance(): SessionManager;
    private initializeSessionsDirectory;
    private loadExistingSessions;
    createSession(workingDirectory: string, context: AgentContext): Promise<Session>;
    loadSession(sessionId: string): Promise<Session>;
    getCurrentSession(): Session | null;
    saveSession(session: Session): Promise<void>;
    deleteSession(sessionId: string): Promise<boolean>;
    listSessions(): Session[];
    addMessage(message: Message): void;
    getConversationHistory(): Message[];
    clearConversationHistory(): void;
    cleanupExpiredSessions(): Promise<number>;
    getSessionStats(): {
        totalSessions: number;
        activeSessions: number;
        oldestSession: Date | null;
        newestSession: Date | null;
        totalMessages: number;
    };
    exportSession(sessionId: string, filePath: string): Promise<void>;
    importSession(filePath: string): Promise<Session>;
    backupSessions(): Promise<void>;
    restoreFromBackup(backupPath: string): Promise<void>;
    cleanupOldSessions(maxAgeHours?: number): Promise<number>;
    getSessionMetrics(): Promise<{
        totalSessions: number;
        activeSessions: number;
        totalMessages: number;
        averageMessagesPerSession: number;
        oldestSession: Date | null;
        newestSession: Date | null;
        totalStorageSize: number;
    }>;
}
//# sourceMappingURL=SessionManager.d.ts.map