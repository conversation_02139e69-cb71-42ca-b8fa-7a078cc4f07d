{"version": 3, "sources": ["../../src/lib/args/pathspec.ts", "../../src/lib/errors/git-error.ts", "../../src/lib/errors/git-response-error.ts", "../../src/lib/errors/task-configuration-error.ts", "../../src/lib/utils/util.ts", "../../src/lib/utils/argument-filters.ts", "../../src/lib/utils/exit-codes.ts", "../../src/lib/utils/git-output-streams.ts", "../../src/lib/utils/line-parser.ts", "../../src/lib/utils/simple-git-options.ts", "../../src/lib/utils/task-options.ts", "../../src/lib/utils/task-parser.ts", "../../src/lib/utils/index.ts", "../../src/lib/tasks/check-is-repo.ts", "../../src/lib/responses/CleanSummary.ts", "../../src/lib/tasks/task.ts", "../../src/lib/tasks/clean.ts", "../../src/lib/responses/ConfigList.ts", "../../src/lib/tasks/config.ts", "../../src/lib/tasks/diff-name-status.ts", "../../src/lib/tasks/grep.ts", "../../src/lib/tasks/reset.ts", "../../src/lib/git-logger.ts", "../../src/lib/runners/tasks-pending-queue.ts", "../../src/lib/runners/git-executor-chain.ts", "../../src/lib/runners/git-executor.ts", "../../src/lib/task-callback.ts", "../../src/lib/tasks/change-working-directory.ts", "../../src/lib/tasks/checkout.ts", "../../src/lib/tasks/count-objects.ts", "../../src/lib/parsers/parse-commit.ts", "../../src/lib/tasks/commit.ts", "../../src/lib/tasks/first-commit.ts", "../../src/lib/tasks/hash-object.ts", "../../src/lib/responses/InitSummary.ts", "../../src/lib/tasks/init.ts", "../../src/lib/args/log-format.ts", "../../src/lib/responses/DiffSummary.ts", "../../src/lib/parsers/parse-diff-summary.ts", "../../src/lib/parsers/parse-list-log-summary.ts", "../../src/lib/tasks/diff.ts", "../../src/lib/tasks/log.ts", "../../src/lib/responses/MergeSummary.ts", "../../src/lib/responses/PullSummary.ts", "../../src/lib/parsers/parse-remote-objects.ts", "../../src/lib/parsers/parse-remote-messages.ts", "../../src/lib/parsers/parse-pull.ts", "../../src/lib/parsers/parse-merge.ts", "../../src/lib/tasks/merge.ts", "../../src/lib/parsers/parse-push.ts", "../../src/lib/tasks/push.ts", "../../src/lib/tasks/show.ts", "../../src/lib/responses/FileStatusSummary.ts", "../../src/lib/responses/StatusSummary.ts", "../../src/lib/tasks/status.ts", "../../src/lib/tasks/version.ts", "../../src/lib/simple-git-api.ts", "../../src/lib/runners/scheduler.ts", "../../src/lib/tasks/apply-patch.ts", "../../src/lib/responses/BranchDeleteSummary.ts", "../../src/lib/parsers/parse-branch-delete.ts", "../../src/lib/responses/BranchSummary.ts", "../../src/lib/parsers/parse-branch.ts", "../../src/lib/tasks/branch.ts", "../../src/lib/responses/CheckIgnore.ts", "../../src/lib/tasks/check-ignore.ts", "../../src/lib/tasks/clone.ts", "../../src/lib/parsers/parse-fetch.ts", "../../src/lib/tasks/fetch.ts", "../../src/lib/parsers/parse-move.ts", "../../src/lib/tasks/move.ts", "../../src/lib/tasks/pull.ts", "../../src/lib/responses/GetRemoteSummary.ts", "../../src/lib/tasks/remote.ts", "../../src/lib/tasks/stash-list.ts", "../../src/lib/tasks/sub-module.ts", "../../src/lib/responses/TagList.ts", "../../src/lib/tasks/tag.ts", "../../src/git.js", "../../src/lib/api.ts", "../../src/lib/errors/git-construct-error.ts", "../../src/lib/errors/git-plugin-error.ts", "../../src/lib/plugins/abort-plugin.ts", "../../src/lib/plugins/block-unsafe-operations-plugin.ts", "../../src/lib/plugins/command-config-prefixing-plugin.ts", "../../src/lib/plugins/completion-detection.plugin.ts", "../../src/lib/plugins/custom-binary.plugin.ts", "../../src/lib/plugins/error-detection.plugin.ts", "../../src/lib/plugins/plugin-store.ts", "../../src/lib/plugins/progress-monitor-plugin.ts", "../../src/lib/plugins/spawn-options-plugin.ts", "../../src/lib/plugins/timout-plugin.ts", "../../src/lib/plugins/suffix-paths.plugin.ts", "../../src/lib/git-factory.ts", "../../src/lib/runners/promise-wrapped.ts", "../../src/esm.mjs"], "sourcesContent": ["const cache = new WeakMap<String, string[]>();\n\nexport function pathspec(...paths: string[]) {\n   const key = new String(paths);\n   cache.set(key, paths);\n\n   return key as string;\n}\n\nexport function isPathSpec(path: string | unknown): path is string {\n   return path instanceof String && cache.has(path);\n}\n\nexport function toPaths(pathSpec: string): string[] {\n   return cache.get(pathSpec) || [];\n}\n", "import type { SimpleGitTask } from '../types';\n\n/**\n * The `GitError` is thrown when the underlying `git` process throws a\n * fatal exception (eg an `ENOENT` exception when attempting to use a\n * non-writable directory as the root for your repo), and acts as the\n * base class for more specific errors thrown by the parsing of the\n * git response or errors in the configuration of the task about to\n * be run.\n *\n * When an exception is thrown, pending tasks in the same instance will\n * not be executed. The recommended way to run a series of tasks that\n * can independently fail without needing to prevent future tasks from\n * running is to catch them individually:\n *\n * ```typescript\n import { gitP, SimpleGit, GitError, PullResult } from 'simple-git';\n\n function catchTask (e: GitError) {\n   return e.\n }\n\n const git = gitP(repoWorkingDir);\n const pulled: PullResult | GitError = await git.pull().catch(catchTask);\n const pushed: string | GitError = await git.pushTags().catch(catchTask);\n ```\n */\nexport class GitError extends Error {\n   constructor(\n      public task?: SimpleGitTask<any>,\n      message?: string\n   ) {\n      super(message);\n      Object.setPrototypeOf(this, new.target.prototype);\n   }\n}\n", "import { GitError } from './git-error';\n\n/**\n * The `GitResponseError` is the wrapper for a parsed response that is treated as\n * a fatal error, for example attempting a `merge` can leave the repo in a corrupted\n * state when there are conflicts so the task will reject rather than resolve.\n *\n * For example, catching the merge conflict exception:\n *\n * ```typescript\n import { gitP, SimpleGit, GitResponseError, MergeSummary } from 'simple-git';\n\n const git = gitP(repoRoot);\n const mergeOptions: string[] = ['--no-ff', 'other-branch'];\n const mergeSummary: MergeSummary = await git.merge(mergeOptions)\n      .catch((e: GitResponseError<MergeSummary>) => e.git);\n\n if (mergeSummary.failed) {\n   // deal with the error\n }\n ```\n */\nexport class GitResponseError<T = any> extends GitError {\n   constructor(\n      /**\n       * `.git` access the parsed response that is treated as being an error\n       */\n      public readonly git: T,\n      message?: string\n   ) {\n      super(undefined, message || String(git));\n   }\n}\n", "import { GitError } from './git-error';\n\n/**\n * The `TaskConfigurationError` is thrown when a command was incorrectly\n * configured. An error of this kind means that no attempt was made to\n * run your command through the underlying `git` binary.\n *\n * Check the `.message` property for more detail on why your configuration\n * resulted in an error.\n */\nexport class TaskConfigurationError extends GitError {\n   constructor(message?: string) {\n      super(undefined, message);\n   }\n}\n", "import { exists, FOLDER } from '@kwsites/file-exists';\nimport { Maybe } from '../types';\n\nexport const NULL = '\\0';\n\nexport const NOOP: (...args: any[]) => void = () => {};\n\n/**\n * Returns either the source argument when it is a `Function`, or the default\n * `NOOP` function constant\n */\nexport function asFunction<T extends () => any>(source: T | any): T {\n   return typeof source === 'function' ? source : NOOP;\n}\n\n/**\n * Determines whether the supplied argument is both a function, and is not\n * the `NOOP` function.\n */\nexport function isUserFunction<T extends Function>(source: T | any): source is T {\n   return typeof source === 'function' && source !== NOOP;\n}\n\nexport function splitOn(input: string, char: string): [string, string] {\n   const index = input.indexOf(char);\n   if (index <= 0) {\n      return [input, ''];\n   }\n\n   return [input.substr(0, index), input.substr(index + 1)];\n}\n\nexport function first<T extends any[]>(input: T, offset?: number): Maybe<T[number]>;\nexport function first<T extends IArguments>(input: T, offset?: number): Maybe<unknown>;\nexport function first(input: any[] | IArguments, offset = 0): Maybe<unknown> {\n   return isArrayLike(input) && input.length > offset ? input[offset] : undefined;\n}\n\nexport function last<T extends any[]>(input: T, offset?: number): Maybe<T[number]>;\nexport function last<T extends IArguments>(input: T, offset?: number): Maybe<unknown>;\nexport function last<T>(input: T, offset?: number): Maybe<unknown>;\nexport function last(input: unknown, offset = 0) {\n   if (isArrayLike(input) && input.length > offset) {\n      return input[input.length - 1 - offset];\n   }\n}\n\ntype ArrayLike<T = any> = T[] | IArguments | { [index: number]: T; length: number };\n\nfunction isArrayLike(input: any): input is ArrayLike {\n   return !!(input && typeof input.length === 'number');\n}\n\nexport function toLinesWithContent(input = '', trimmed = true, separator = '\\n'): string[] {\n   return input.split(separator).reduce((output, line) => {\n      const lineContent = trimmed ? line.trim() : line;\n      if (lineContent) {\n         output.push(lineContent);\n      }\n      return output;\n   }, [] as string[]);\n}\n\ntype LineWithContentCallback<T = void> = (line: string) => T;\n\nexport function forEachLineWithContent<T>(\n   input: string,\n   callback: LineWithContentCallback<T>\n): T[] {\n   return toLinesWithContent(input, true).map((line) => callback(line));\n}\n\nexport function folderExists(path: string): boolean {\n   return exists(path, FOLDER);\n}\n\n/**\n * Adds `item` into the `target` `Array` or `Set` when it is not already present and returns the `item`.\n */\nexport function append<T>(target: T[] | Set<T>, item: T): typeof item {\n   if (Array.isArray(target)) {\n      if (!target.includes(item)) {\n         target.push(item);\n      }\n   } else {\n      target.add(item);\n   }\n   return item;\n}\n\n/**\n * Adds `item` into the `target` `Array` when it is not already present and returns the `target`.\n */\nexport function including<T>(target: T[], item: T): typeof target {\n   if (Array.isArray(target) && !target.includes(item)) {\n      target.push(item);\n   }\n\n   return target;\n}\n\nexport function remove<T>(target: Set<T> | T[], item: T): T {\n   if (Array.isArray(target)) {\n      const index = target.indexOf(item);\n      if (index >= 0) {\n         target.splice(index, 1);\n      }\n   } else {\n      target.delete(item);\n   }\n   return item;\n}\n\nexport const objectToString = Object.prototype.toString.call.bind(Object.prototype.toString) as (\n   input: any\n) => string;\n\nexport function asArray<T>(source: T | T[]): T[] {\n   return Array.isArray(source) ? source : [source];\n}\n\nexport function asCamelCase(str: string) {\n   return str.replace(/[\\s-]+(.)/g, (_all, chr) => {\n      return chr.toUpperCase();\n   });\n}\n\nexport function asStringArray<T>(source: T | T[]): string[] {\n   return asArray(source).map(String);\n}\n\nexport function asNumber(source: string | null | undefined, onNaN = 0) {\n   if (source == null) {\n      return onNaN;\n   }\n\n   const num = parseInt(source, 10);\n   return isNaN(num) ? onNaN : num;\n}\n\nexport function prefixedArray<T>(input: T[], prefix: T): T[] {\n   const output: T[] = [];\n   for (let i = 0, max = input.length; i < max; i++) {\n      output.push(prefix, input[i]);\n   }\n   return output;\n}\n\nexport function bufferToString(input: Buffer | Buffer[]): string {\n   return (Array.isArray(input) ? Buffer.concat(input) : input).toString('utf-8');\n}\n\n/**\n * Get a new object from a source object with only the listed properties.\n */\nexport function pick(source: Record<string, any>, properties: string[]) {\n   return Object.assign(\n      {},\n      ...properties.map((property) => (property in source ? { [property]: source[property] } : {}))\n   );\n}\n\nexport function delay(duration = 0): Promise<void> {\n   return new Promise((done) => setTimeout(done, duration));\n}\n\nexport function orVoid<T>(input: T | false) {\n   if (input === false) {\n      return undefined;\n   }\n   return input;\n}\n", "import { Maybe, Options, Primitives } from '../types';\nimport { objectToString } from './util';\nimport { isPathSpec } from '../args/pathspec';\n\nexport interface ArgumentFilterPredicate<T> {\n   (input: any): input is T;\n}\n\nexport function filterType<T, K>(\n   input: K,\n   filter: ArgumentFilterPredicate<T>\n): K extends T ? T : undefined;\nexport function filterType<T, K>(input: K, filter: ArgumentFilterPredicate<T>, def: T): T;\nexport function filterType<T, K>(input: K, filter: ArgumentFilterPredicate<T>, def?: T): Maybe<T> {\n   if (filter(input)) {\n      return input;\n   }\n   return arguments.length > 2 ? def : undefined;\n}\n\nexport const filterArray: ArgumentFilterPredicate<Array<any>> = (input): input is Array<any> => {\n   return Array.isArray(input);\n};\n\nexport function filterPrimitives(\n   input: unknown,\n   omit?: Array<'boolean' | 'string' | 'number'>\n): input is Primitives {\n   const type = isPathSpec(input) ? 'string' : typeof input;\n\n   return (\n      /number|string|boolean/.test(type) &&\n      (!omit || !omit.includes(type as 'boolean' | 'string' | 'number'))\n   );\n}\n\nexport const filterString: ArgumentFilterPredicate<string> = (input): input is string => {\n   return typeof input === 'string';\n};\n\nexport const filterStringArray: ArgumentFilterPredicate<string[]> = (input): input is string[] => {\n   return Array.isArray(input) && input.every(filterString);\n};\n\nexport const filterStringOrStringArray: ArgumentFilterPredicate<string | string[]> = (\n   input\n): input is string | string[] => {\n   return filterString(input) || (Array.isArray(input) && input.every(filterString));\n};\n\nexport function filterPlainObject<T extends Options>(input: T | unknown): input is T;\nexport function filterPlainObject<T extends Object>(input: T | unknown): input is T {\n   return !!input && objectToString(input) === '[object Object]';\n}\n\nexport function filterFunction(input: unknown): input is Function {\n   return typeof input === 'function';\n}\n\nexport const filterHasLength: ArgumentFilterPredicate<{ length: number }> = (\n   input\n): input is { length: number } => {\n   if (input == null || 'number|boolean|function'.includes(typeof input)) {\n      return false;\n   }\n   return Array.isArray(input) || typeof input === 'string' || typeof input.length === 'number';\n};\n", "/**\n * Known process exit codes used by the task parsers to determine whether an error\n * was one they can automatically handle\n */\nexport enum ExitCodes {\n   SUCCESS,\n   ERROR,\n   NOT_FOUND = -2,\n   UNCLEAN = 128,\n}\n", "import { TaskResponseFormat } from '../types';\n\nexport class GitOutputStreams<T extends TaskResponseFormat = Buffer> {\n   constructor(\n      public readonly stdOut: T,\n      public readonly stdErr: T\n   ) {}\n\n   asStrings(): GitOutputStreams<string> {\n      return new GitOutputStreams(this.stdOut.toString('utf8'), this.stdErr.toString('utf8'));\n   }\n}\n", "export class LineParser<T> {\n   protected matches: string[] = [];\n\n   private _regExp: RegExp[];\n\n   constructor(\n      regExp: RegExp | RegExp[],\n      useMatches?: (target: T, match: string[]) => boolean | void\n   ) {\n      this._regExp = Array.isArray(regExp) ? regExp : [regExp];\n      if (useMatches) {\n         this.useMatches = useMatches;\n      }\n   }\n\n   parse = (line: (offset: number) => string | undefined, target: T): boolean => {\n      this.resetMatches();\n\n      if (!this._regExp.every((reg, index) => this.addMatch(reg, index, line(index)))) {\n         return false;\n      }\n\n      return this.useMatches(target, this.prepareMatches()) !== false;\n   };\n\n   // @ts-ignore\n   protected useMatches(target: T, match: string[]): boolean | void {\n      throw new Error(`LineParser:useMatches not implemented`);\n   }\n\n   protected resetMatches() {\n      this.matches.length = 0;\n   }\n\n   protected prepareMatches() {\n      return this.matches;\n   }\n\n   protected addMatch(reg: RegExp, index: number, line?: string) {\n      const matched = line && reg.exec(line);\n      if (matched) {\n         this.pushMatch(index, matched);\n      }\n\n      return !!matched;\n   }\n\n   protected pushMatch(_index: number, matched: string[]) {\n      this.matches.push(...matched.slice(1));\n   }\n}\n\nexport class RemoteLineParser<T> extends LineParser<T> {\n   protected addMatch(reg: RegExp, index: number, line?: string): boolean {\n      return /^remote:\\s/.test(String(line)) && super.addMatch(reg, index, line);\n   }\n\n   protected pushMatch(index: number, matched: string[]) {\n      if (index > 0 || matched.length > 1) {\n         super.pushMatch(index, matched);\n      }\n   }\n}\n", "import { SimpleGitOptions } from '../types';\n\nconst defaultOptions: Omit<SimpleGitOptions, 'baseDir'> = {\n   binary: 'git',\n   maxConcurrentProcesses: 5,\n   config: [],\n   trimmed: false,\n};\n\nexport function createInstanceConfig(\n   ...options: Array<Partial<SimpleGitOptions> | undefined>\n): SimpleGitOptions {\n   const baseDir = process.cwd();\n   const config: SimpleGitOptions = Object.assign(\n      { baseDir, ...defaultOptions },\n      ...options.filter((o) => typeof o === 'object' && o)\n   );\n\n   config.baseDir = config.baseDir || baseDir;\n   config.trimmed = config.trimmed === true;\n\n   return config;\n}\n", "import {\n   filterArray,\n   filterFunction,\n   filterPlainObject,\n   filterPrimitives,\n   filterType,\n} from './argument-filters';\nimport { asFunction, isUserFunction, last } from './util';\nimport { Maybe, Options, OptionsValues } from '../types';\nimport { isPathSpec } from '../args/pathspec';\n\nexport function appendTaskOptions<T extends Options = Options>(\n   options: Maybe<T>,\n   commands: string[] = []\n): string[] {\n   if (!filterPlainObject<Options>(options)) {\n      return commands;\n   }\n\n   return Object.keys(options).reduce((commands: string[], key: string) => {\n      const value: OptionsValues = options[key];\n\n      if (isPathSpec(value)) {\n         commands.push(value);\n      } else if (filterPrimitives(value, ['boolean'])) {\n         commands.push(key + '=' + value);\n      } else {\n         commands.push(key);\n      }\n\n      return commands;\n   }, commands);\n}\n\nexport function getTrailingOptions(\n   args: IArguments,\n   initialPrimitive = 0,\n   objectOnly = false\n): string[] {\n   const command: string[] = [];\n\n   for (let i = 0, max = initialPrimitive < 0 ? args.length : initialPrimitive; i < max; i++) {\n      if ('string|number'.includes(typeof args[i])) {\n         command.push(String(args[i]));\n      }\n   }\n\n   appendTaskOptions(trailingOptionsArgument(args), command);\n   if (!objectOnly) {\n      command.push(...trailingArrayArgument(args));\n   }\n\n   return command;\n}\n\nfunction trailingArrayArgument(args: IArguments) {\n   const hasTrailingCallback = typeof last(args) === 'function';\n   return filterType(last(args, hasTrailingCallback ? 1 : 0), filterArray, []);\n}\n\n/**\n * Given any number of arguments, returns the trailing options argument, ignoring a trailing function argument\n * if there is one. When not found, the return value is null.\n */\nexport function trailingOptionsArgument(args: IArguments): Maybe<Options> {\n   const hasTrailingCallback = filterFunction(last(args));\n   return filterType(last(args, hasTrailingCallback ? 1 : 0), filterPlainObject);\n}\n\n/**\n * Returns either the source argument when it is a `Function`, or the default\n * `NOOP` function constant\n */\nexport function trailingFunctionArgument(\n   args: unknown[] | IArguments | unknown,\n   includeNoop = true\n): Maybe<(...args: any[]) => unknown> {\n   const callback = asFunction(last(args));\n   return includeNoop || isUserFunction(callback) ? callback : undefined;\n}\n", "import type { Maybe<PERSON><PERSON><PERSON>, TaskParser, TaskResponseFormat } from '../types';\nimport { GitOutputStreams } from './git-output-streams';\nimport { LineParser } from './line-parser';\nimport { asArray, toLinesWithContent } from './util';\n\nexport function callTaskParser<INPUT extends TaskResponseFormat, RESPONSE>(\n   parser: TaskParser<INPUT, RESPONSE>,\n   streams: GitOutputStreams<INPUT>\n) {\n   return parser(streams.stdOut, streams.stdErr);\n}\n\nexport function parseStringResponse<T>(\n   result: T,\n   parsers: LineParser<T>[],\n   texts: MaybeArray<string>,\n   trim = true\n): T {\n   asArray(texts).forEach((text) => {\n      for (let lines = toLinesWithContent(text, trim), i = 0, max = lines.length; i < max; i++) {\n         const line = (offset = 0) => {\n            if (i + offset >= max) {\n               return;\n            }\n            return lines[i + offset];\n         };\n\n         parsers.some(({ parse }) => parse(line, result));\n      }\n   });\n\n   return result;\n}\n", "export * from './argument-filters';\nexport * from './exit-codes';\nexport * from './git-output-streams';\nexport * from './line-parser';\nexport * from './simple-git-options';\nexport * from './task-options';\nexport * from './task-parser';\nexport * from './util';\n", "import { ExitCodes } from '../utils';\nimport { Maybe, StringTask } from '../types';\n\nexport enum CheckRepoActions {\n   BARE = 'bare',\n   IN_TREE = 'tree',\n   IS_REPO_ROOT = 'root',\n}\n\nconst onError: StringTask<boolean>['onError'] = ({ exitCode }, error, done, fail) => {\n   if (exitCode === ExitCodes.UNCLEAN && isNotRepoMessage(error)) {\n      return done(Buffer.from('false'));\n   }\n\n   fail(error);\n};\n\nconst parser: StringTask<boolean>['parser'] = (text) => {\n   return text.trim() === 'true';\n};\n\nexport function checkIsRepoTask(action: Maybe<CheckRepoActions>): StringTask<boolean> {\n   switch (action) {\n      case CheckRepoActions.BARE:\n         return checkIsBareRepoTask();\n      case CheckRepoActions.IS_REPO_ROOT:\n         return checkIsRepoRootTask();\n   }\n\n   const commands = ['rev-parse', '--is-inside-work-tree'];\n\n   return {\n      commands,\n      format: 'utf-8',\n      onError,\n      parser,\n   };\n}\n\nexport function checkIsRepoRootTask(): StringTask<boolean> {\n   const commands = ['rev-parse', '--git-dir'];\n\n   return {\n      commands,\n      format: 'utf-8',\n      onError,\n      parser(path) {\n         return /^\\.(git)?$/.test(path.trim());\n      },\n   };\n}\n\nexport function checkIsBareRepoTask(): StringTask<boolean> {\n   const commands = ['rev-parse', '--is-bare-repository'];\n\n   return {\n      commands,\n      format: 'utf-8',\n      onError,\n      parser,\n   };\n}\n\nfunction isNotRepoMessage(error: Error): boolean {\n   return /(Not a git repository|Kein Git-Repository)/i.test(String(error));\n}\n", "import { CleanSummary } from '../../../typings';\nimport { toLinesWithContent } from '../utils';\n\nexport class CleanResponse implements CleanSummary {\n   public paths: string[] = [];\n   public files: string[] = [];\n   public folders: string[] = [];\n\n   constructor(public readonly dryRun: boolean) {}\n}\n\nconst removalRegexp = /^[a-z]+\\s*/i;\nconst dryRunRemovalRegexp = /^[a-z]+\\s+[a-z]+\\s*/i;\nconst isFolderRegexp = /\\/$/;\n\nexport function cleanSummaryParser(dryRun: boolean, text: string): CleanSummary {\n   const summary = new CleanResponse(dryRun);\n   const regexp = dryRun ? dryRunRemovalRegexp : removalRegexp;\n\n   toLinesWithContent(text).forEach((line) => {\n      const removed = line.replace(regexp, '');\n\n      summary.paths.push(removed);\n      (isFolderRegexp.test(removed) ? summary.folders : summary.files).push(removed);\n   });\n\n   return summary;\n}\n", "import { TaskConfigurationError } from '../errors/task-configuration-error';\nimport type { BufferTask, EmptyTaskParser, SimpleGitTask, StringTask } from '../types';\n\nexport const EMPTY_COMMANDS: [] = [];\n\nexport type EmptyTask = {\n   commands: typeof EMPTY_COMMANDS;\n   format: 'empty';\n   parser: EmptyTaskParser;\n   onError?: undefined;\n};\n\nexport function adhocExecTask(parser: EmptyTaskParser): EmptyTask {\n   return {\n      commands: EMPTY_COMMANDS,\n      format: 'empty',\n      parser,\n   };\n}\n\nexport function configurationErrorTask(error: Error | string): EmptyTask {\n   return {\n      commands: EMPTY_COMMANDS,\n      format: 'empty',\n      parser() {\n         throw typeof error === 'string' ? new TaskConfigurationError(error) : error;\n      },\n   };\n}\n\nexport function straightThroughStringTask(commands: string[], trimmed = false): StringTask<string> {\n   return {\n      commands,\n      format: 'utf-8',\n      parser(text) {\n         return trimmed ? String(text).trim() : text;\n      },\n   };\n}\n\nexport function straightThroughBufferTask(commands: string[]): BufferTask<any> {\n   return {\n      commands,\n      format: 'buffer',\n      parser(buffer) {\n         return buffer;\n      },\n   };\n}\n\nexport function isBufferTask<R>(task: SimpleGitTask<R>): task is BufferTask<R> {\n   return task.format === 'buffer';\n}\n\nexport function isEmptyTask<R>(task: SimpleGitTask<R>): task is EmptyTask {\n   return task.format === 'empty' || !task.commands.length;\n}\n", "import { CleanSummary } from '../../../typings';\nimport { cleanSummaryParser } from '../responses/CleanSummary';\nimport { Maybe, StringTask } from '../types';\nimport { asStringArray } from '../utils';\nimport { configurationErrorTask } from './task';\n\nexport const CONFIG_ERROR_INTERACTIVE_MODE = 'Git clean interactive mode is not supported';\nexport const CONFIG_ERROR_MODE_REQUIRED = 'Git clean mode parameter (\"n\" or \"f\") is required';\nexport const CONFIG_ERROR_UNKNOWN_OPTION = 'Git clean unknown option found in: ';\n\n/**\n * All supported option switches available for use in a `git.clean` operation\n */\nexport enum CleanOptions {\n   DRY_RUN = 'n',\n   FORCE = 'f',\n   IGNORED_INCLUDED = 'x',\n   IGNORED_ONLY = 'X',\n   EXCLUDING = 'e',\n   QUIET = 'q',\n   RECURSIVE = 'd',\n}\n\n/**\n * The two modes `git.clean` can run in - one of these must be supplied in order\n * for the command to not throw a `TaskConfigurationError`\n */\nexport type CleanMode = CleanOptions.FORCE | CleanOptions.DRY_RUN;\n\nconst CleanOptionValues: Set<string> = new Set([\n   'i',\n   ...asStringArray(Object.values(CleanOptions as any)),\n]);\n\nexport function cleanWithOptionsTask(mode: CleanMode | string, customArgs: string[]) {\n   const { cleanMode, options, valid } = getCleanOptions(mode);\n\n   if (!cleanMode) {\n      return configurationErrorTask(CONFIG_ERROR_MODE_REQUIRED);\n   }\n\n   if (!valid.options) {\n      return configurationErrorTask(CONFIG_ERROR_UNKNOWN_OPTION + JSON.stringify(mode));\n   }\n\n   options.push(...customArgs);\n\n   if (options.some(isInteractiveMode)) {\n      return configurationErrorTask(CONFIG_ERROR_INTERACTIVE_MODE);\n   }\n\n   return cleanTask(cleanMode, options);\n}\n\nexport function cleanTask(mode: CleanMode, customArgs: string[]): StringTask<CleanSummary> {\n   const commands: string[] = ['clean', `-${mode}`, ...customArgs];\n\n   return {\n      commands,\n      format: 'utf-8',\n      parser(text: string): CleanSummary {\n         return cleanSummaryParser(mode === CleanOptions.DRY_RUN, text);\n      },\n   };\n}\n\nexport function isCleanOptionsArray(input: string[]): input is CleanOptions[] {\n   return Array.isArray(input) && input.every((test) => CleanOptionValues.has(test));\n}\n\nfunction getCleanOptions(input: string) {\n   let cleanMode: Maybe<CleanMode>;\n   let options: string[] = [];\n   let valid = { cleanMode: false, options: true };\n\n   input\n      .replace(/[^a-z]i/g, '')\n      .split('')\n      .forEach((char) => {\n         if (isCleanMode(char)) {\n            cleanMode = char;\n            valid.cleanMode = true;\n         } else {\n            valid.options = valid.options && isKnownOption((options[options.length] = `-${char}`));\n         }\n      });\n\n   return {\n      cleanMode,\n      options,\n      valid,\n   };\n}\n\nfunction isCleanMode(cleanMode?: string): cleanMode is CleanMode {\n   return cleanMode === CleanOptions.FORCE || cleanMode === CleanOptions.DRY_RUN;\n}\n\nfunction isKnownOption(option: string): boolean {\n   return /^-[a-z]$/i.test(option) && CleanOptionValues.has(option.charAt(1));\n}\n\nfunction isInteractiveMode(option: string): boolean {\n   if (/^-[^\\-]/.test(option)) {\n      return option.indexOf('i') > 0;\n   }\n\n   return option === '--interactive';\n}\n", "import { ConfigGetResult, ConfigListSummary, ConfigValues } from '../../../typings';\nimport { last, splitOn } from '../utils';\n\nexport class ConfigList implements ConfigListSummary {\n   public files: string[] = [];\n   public values: { [fileName: string]: ConfigValues } = Object.create(null);\n\n   private _all: ConfigValues | undefined;\n\n   public get all(): ConfigValues {\n      if (!this._all) {\n         this._all = this.files.reduce((all: ConfigValues, file: string) => {\n            return Object.assign(all, this.values[file]);\n         }, {});\n      }\n\n      return this._all;\n   }\n\n   public addFile(file: string): ConfigValues {\n      if (!(file in this.values)) {\n         const latest = last(this.files);\n         this.values[file] = latest ? Object.create(this.values[latest]) : {};\n\n         this.files.push(file);\n      }\n\n      return this.values[file];\n   }\n\n   public addValue(file: string, key: string, value: string) {\n      const values = this.addFile(file);\n\n      if (!values.hasOwnProperty(key)) {\n         values[key] = value;\n      } else if (Array.isArray(values[key])) {\n         (values[key] as string[]).push(value);\n      } else {\n         values[key] = [values[key] as string, value];\n      }\n\n      this._all = undefined;\n   }\n}\n\nexport function configListParser(text: string): ConfigList {\n   const config = new ConfigList();\n\n   for (const item of configParser(text)) {\n      config.addValue(item.file, String(item.key), item.value);\n   }\n\n   return config;\n}\n\nexport function configGetParser(text: string, key: string): ConfigGetResult {\n   let value: string | null = null;\n   const values: string[] = [];\n   const scopes: Map<string, string[]> = new Map();\n\n   for (const item of configParser(text, key)) {\n      if (item.key !== key) {\n         continue;\n      }\n\n      values.push((value = item.value));\n\n      if (!scopes.has(item.file)) {\n         scopes.set(item.file, []);\n      }\n\n      scopes.get(item.file)!.push(value);\n   }\n\n   return {\n      key,\n      paths: Array.from(scopes.keys()),\n      scopes,\n      value,\n      values,\n   };\n}\n\nfunction configFilePath(filePath: string): string {\n   return filePath.replace(/^(file):/, '');\n}\n\nfunction* configParser(text: string, requestedKey: string | null = null) {\n   const lines = text.split('\\0');\n\n   for (let i = 0, max = lines.length - 1; i < max; ) {\n      const file = configFilePath(lines[i++]);\n\n      let value = lines[i++];\n      let key = requestedKey;\n\n      if (value.includes('\\n')) {\n         const line = splitOn(value, '\\n');\n         key = line[0];\n         value = line[1];\n      }\n\n      yield { file, key, value };\n   }\n}\n", "import type { ConfigGetResult, ConfigListSum<PERSON>y, SimpleGit } from '../../../typings';\nimport { configGetParser, configListParser } from '../responses/ConfigList';\nimport type { SimpleGitApi } from '../simple-git-api';\nimport type { StringTask } from '../types';\nimport { trailingFunctionArgument } from '../utils';\n\nexport enum GitConfigScope {\n   system = 'system',\n   global = 'global',\n   local = 'local',\n   worktree = 'worktree',\n}\n\nfunction asConfigScope<T extends GitConfigScope | undefined>(\n   scope: GitConfigScope | unknown,\n   fallback: T\n): GitConfigScope | T {\n   if (typeof scope === 'string' && GitConfigScope.hasOwnProperty(scope)) {\n      return scope as GitConfigScope;\n   }\n   return fallback;\n}\n\nfunction addConfigTask(\n   key: string,\n   value: string,\n   append: boolean,\n   scope: GitConfigScope\n): StringTask<string> {\n   const commands: string[] = ['config', `--${scope}`];\n\n   if (append) {\n      commands.push('--add');\n   }\n\n   commands.push(key, value);\n\n   return {\n      commands,\n      format: 'utf-8',\n      parser(text: string): string {\n         return text;\n      },\n   };\n}\n\nfunction getConfigTask(key: string, scope?: GitConfigScope): StringTask<ConfigGetResult> {\n   const commands: string[] = ['config', '--null', '--show-origin', '--get-all', key];\n\n   if (scope) {\n      commands.splice(1, 0, `--${scope}`);\n   }\n\n   return {\n      commands,\n      format: 'utf-8',\n      parser(text) {\n         return configGetParser(text, key);\n      },\n   };\n}\n\nfunction listConfigTask(scope?: GitConfigScope): StringTask<ConfigListSummary> {\n   const commands = ['config', '--list', '--show-origin', '--null'];\n\n   if (scope) {\n      commands.push(`--${scope}`);\n   }\n\n   return {\n      commands,\n      format: 'utf-8',\n      parser(text: string) {\n         return configListParser(text);\n      },\n   };\n}\n\nexport default function (): Pick<SimpleGit, 'addConfig' | 'getConfig' | 'listConfig'> {\n   return {\n      addConfig(this: SimpleGitApi, key: string, value: string, ...rest: unknown[]) {\n         return this._runTask(\n            addConfigTask(\n               key,\n               value,\n               rest[0] === true,\n               asConfigScope(rest[1], GitConfigScope.local)\n            ),\n            trailingFunctionArgument(arguments)\n         );\n      },\n\n      getConfig(this: SimpleGitApi, key: string, scope?: GitConfigScope) {\n         return this._runTask(\n            getConfigTask(key, asConfigScope(scope, undefined)),\n            trailingFunctionArgument(arguments)\n         );\n      },\n\n      listConfig(this: SimpleGitApi, ...rest: unknown[]) {\n         return this._runTask(\n            listConfigTask(asConfigScope(rest[0], undefined)),\n            trailingFunctionArgument(arguments)\n         );\n      },\n   };\n}\n", "export enum DiffNameStatus {\n   ADDED = 'A',\n   COPIED = 'C',\n   DELETED = 'D',\n   MODIFIED = 'M',\n   RENAMED = 'R',\n   CHANGED = 'T',\n   UNMERGED = 'U',\n   UNKNOWN = 'X',\n   BROKEN = 'B',\n}\n\nconst diffNameStatus = new Set(Object.values(DiffNameStatus));\n\nexport function isDiffNameStatus(input: string): input is DiffNameStatus {\n   return diffNameStatus.has(input as DiffNameStatus);\n}\n", "import { GrepResult, SimpleGit } from '../../../typings';\nimport { SimpleGitApi } from '../simple-git-api';\nimport {\n   asNumber,\n   forEachLineWithContent,\n   getTrailingOptions,\n   NULL,\n   prefixedArray,\n   trailingFunctionArgument,\n} from '../utils';\n\nimport { configurationErrorTask } from './task';\n\nconst disallowedOptions = ['-h'];\n\nconst Query = Symbol('grepQuery');\n\nexport interface GitGrepQuery extends Iterable<string> {\n   /** Adds one or more terms to be grouped as an \"and\" to any other terms */\n   and(...and: string[]): this;\n\n   /** Adds one or more search terms - git.grep will \"or\" this to other terms */\n   param(...param: string[]): this;\n}\n\nclass GrepQuery implements GitGrepQuery {\n   private [Query]: string[] = [];\n\n   *[Symbol.iterator]() {\n      for (const query of this[Query]) {\n         yield query;\n      }\n   }\n\n   and(...and: string[]) {\n      and.length && this[Query].push('--and', '(', ...prefixedArray(and, '-e'), ')');\n      return this;\n   }\n\n   param(...param: string[]) {\n      this[Query].push(...prefixedArray(param, '-e'));\n      return this;\n   }\n}\n\n/**\n * Creates a new builder for a `git.grep` query with optional params\n */\nexport function grepQueryBuilder(...params: string[]): GitGrepQuery {\n   return new GrepQuery().param(...params);\n}\n\nfunction parseGrep(grep: string): GrepResult {\n   const paths: GrepResult['paths'] = new Set<string>();\n   const results: GrepResult['results'] = {};\n\n   forEachLineWithContent(grep, (input) => {\n      const [path, line, preview] = input.split(NULL);\n      paths.add(path);\n      (results[path] = results[path] || []).push({\n         line: asNumber(line),\n         path,\n         preview,\n      });\n   });\n\n   return {\n      paths,\n      results,\n   };\n}\n\nexport default function (): Pick<SimpleGit, 'grep'> {\n   return {\n      grep(this: SimpleGitApi, searchTerm: string | GitGrepQuery) {\n         const then = trailingFunctionArgument(arguments);\n         const options = getTrailingOptions(arguments);\n\n         for (const option of disallowedOptions) {\n            if (options.includes(option)) {\n               return this._runTask(\n                  configurationErrorTask(`git.grep: use of \"${option}\" is not supported.`),\n                  then\n               );\n            }\n         }\n\n         if (typeof searchTerm === 'string') {\n            searchTerm = grepQueryBuilder().param(searchTerm);\n         }\n\n         const commands = ['grep', '--null', '-n', '--full-name', ...options, ...searchTerm];\n\n         return this._runTask(\n            {\n               commands,\n               format: 'utf-8',\n               parser(stdOut) {\n                  return parseGrep(stdOut);\n               },\n            },\n            then\n         );\n      },\n   };\n}\n", "import { straightThroughStringTask } from './task';\nimport { Maybe, OptionFlags, Options } from '../types';\n\nexport enum ResetMode {\n   MIXED = 'mixed',\n   SOFT = 'soft',\n   HARD = 'hard',\n   MERGE = 'merge',\n   KEEP = 'keep',\n}\n\nconst ResetModes = Array.from(Object.values(ResetMode));\n\nexport type ResetOptions = Options &\n   OptionFlags<'-q' | '--quiet' | '--no-quiet' | '--pathspec-from-nul'> &\n   OptionFlags<'--pathspec-from-file', string>;\n\nexport function resetTask(mode: Maybe<ResetMode>, customArgs: string[]) {\n   const commands: string[] = ['reset'];\n   if (isValidResetMode(mode)) {\n      commands.push(`--${mode}`);\n   }\n   commands.push(...customArgs);\n\n   return straightThroughStringTask(commands);\n}\n\nexport function getResetMode(mode: ResetMode | any): Maybe<ResetMode> {\n   if (isValidResetMode(mode)) {\n      return mode;\n   }\n\n   switch (typeof mode) {\n      case 'string':\n      case 'undefined':\n         return ResetMode.SOFT;\n   }\n\n   return;\n}\n\nfunction isValidResetMode(mode: ResetMode | any): mode is ResetMode {\n   return ResetModes.includes(mode);\n}\n", "import debug, { Debugger } from 'debug';\nimport {\n   append,\n   filterHasLength,\n   filterString,\n   filterType,\n   NOOP,\n   objectToString,\n   remove,\n} from './utils';\nimport { Maybe } from './types';\n\ndebug.formatters.L = (value: any) => String(filterHasLength(value) ? value.length : '-');\ndebug.formatters.B = (value: Buffer) => {\n   if (Buffer.isBuffer(value)) {\n      return value.toString('utf8');\n   }\n   return objectToString(value);\n};\n\ntype OutputLoggingHandler = (message: string, ...args: any[]) => void;\n\nfunction createLog() {\n   return debug('simple-git');\n}\n\nexport interface OutputLogger extends OutputLoggingHandler {\n   readonly label: string;\n\n   info: OutputLoggingHandler;\n   step(nextStep?: string): OutputLogger;\n   sibling(name: string): OutputLogger;\n}\n\nfunction prefixedLogger(\n   to: Debugger,\n   prefix: string,\n   forward?: OutputLoggingHandler\n): OutputLoggingHandler {\n   if (!prefix || !String(prefix).replace(/\\s*/, '')) {\n      return !forward\n         ? to\n         : (message, ...args) => {\n              to(message, ...args);\n              forward(message, ...args);\n           };\n   }\n\n   return (message, ...args) => {\n      to(`%s ${message}`, prefix, ...args);\n      if (forward) {\n         forward(message, ...args);\n      }\n   };\n}\n\nfunction childLoggerName(\n   name: Maybe<string>,\n   childDebugger: Maybe<Debugger>,\n   { namespace: parentNamespace }: Debugger\n): string {\n   if (typeof name === 'string') {\n      return name;\n   }\n   const childNamespace = (childDebugger && childDebugger.namespace) || '';\n\n   if (childNamespace.startsWith(parentNamespace)) {\n      return childNamespace.substr(parentNamespace.length + 1);\n   }\n\n   return childNamespace || parentNamespace;\n}\n\nexport function createLogger(\n   label: string,\n   verbose?: string | Debugger,\n   initialStep?: string,\n   infoDebugger = createLog()\n): OutputLogger {\n   const labelPrefix = (label && `[${label}]`) || '';\n\n   const spawned: OutputLogger[] = [];\n   const debugDebugger: Maybe<Debugger> =\n      typeof verbose === 'string' ? infoDebugger.extend(verbose) : verbose;\n   const key = childLoggerName(filterType(verbose, filterString), debugDebugger, infoDebugger);\n\n   return step(initialStep);\n\n   function sibling(name: string, initial?: string) {\n      return append(\n         spawned,\n         createLogger(label, key.replace(/^[^:]+/, name), initial, infoDebugger)\n      );\n   }\n\n   function step(phase?: string) {\n      const stepPrefix = (phase && `[${phase}]`) || '';\n      const debug = (debugDebugger && prefixedLogger(debugDebugger, stepPrefix)) || NOOP;\n      const info = prefixedLogger(infoDebugger, `${labelPrefix} ${stepPrefix}`, debug);\n\n      return Object.assign(debugDebugger ? debug : info, {\n         label,\n         sibling,\n         info,\n         step,\n      });\n   }\n}\n\n/**\n * The `GitLogger` is used by the main `SimpleGit` runner to handle logging\n * any warnings or errors.\n */\nexport class GitLogger {\n   public error: OutputLoggingHandler;\n\n   public warn: OutputLoggingHandler;\n\n   constructor(private _out: Debugger = createLog()) {\n      this.error = prefixedLogger(_out, '[ERROR]');\n      this.warn = prefixedLogger(_out, '[WARN]');\n   }\n\n   silent(silence = false) {\n      if (silence !== this._out.enabled) {\n         return;\n      }\n\n      const { namespace } = this._out;\n      const env = (process.env.DEBUG || '').split(',').filter((s) => !!s);\n      const hasOn = env.includes(namespace);\n      const hasOff = env.includes(`-${namespace}`);\n\n      // enabling the log\n      if (!silence) {\n         if (hasOff) {\n            remove(env, `-${namespace}`);\n         } else {\n            env.push(namespace);\n         }\n      } else {\n         if (hasOn) {\n            remove(env, namespace);\n         } else {\n            env.push(`-${namespace}`);\n         }\n      }\n\n      debug.enable(env.join(','));\n   }\n}\n", "import { SimpleGitTask } from '../types';\nimport { GitError } from '../errors/git-error';\nimport { createLogger, OutputLogger } from '../git-logger';\n\ntype AnySimpleGitTask = SimpleGitTask<any>;\n\ntype TaskInProgress = {\n   name: string;\n   logger: OutputLogger;\n   task: AnySimpleGitTask;\n};\n\nexport class TasksPendingQueue {\n   private _queue: Map<AnySimpleGitTask, TaskInProgress> = new Map();\n\n   constructor(private logLabel = 'GitExecutor') {}\n\n   private withProgress(task: AnySimpleGitTask) {\n      return this._queue.get(task);\n   }\n\n   private createProgress(task: AnySimpleGitTask): TaskInProgress {\n      const name = TasksPendingQueue.getName(task.commands[0]);\n      const logger = createLogger(this.logLabel, name);\n\n      return {\n         task,\n         logger,\n         name,\n      };\n   }\n\n   push(task: AnySimpleGitTask): TaskInProgress {\n      const progress = this.createProgress(task);\n      progress.logger('Adding task to the queue, commands = %o', task.commands);\n\n      this._queue.set(task, progress);\n\n      return progress;\n   }\n\n   fatal(err: GitError) {\n      for (const [task, { logger }] of Array.from(this._queue.entries())) {\n         if (task === err.task) {\n            logger.info(`Failed %o`, err);\n            logger(\n               `Fatal exception, any as-yet un-started tasks run through this executor will not be attempted`\n            );\n         } else {\n            logger.info(\n               `A fatal exception occurred in a previous task, the queue has been purged: %o`,\n               err.message\n            );\n         }\n\n         this.complete(task);\n      }\n\n      if (this._queue.size !== 0) {\n         throw new Error(`Queue size should be zero after fatal: ${this._queue.size}`);\n      }\n   }\n\n   complete(task: AnySimpleGitTask) {\n      const progress = this.withProgress(task);\n      if (progress) {\n         this._queue.delete(task);\n      }\n   }\n\n   attempt(task: AnySimpleGitTask): TaskInProgress {\n      const progress = this.withProgress(task);\n      if (!progress) {\n         throw new GitError(undefined, 'TasksPendingQueue: attempt called for an unknown task');\n      }\n      progress.logger('Starting task');\n\n      return progress;\n   }\n\n   static getName(name = 'empty') {\n      return `task:${name}:${++TasksPendingQueue.counter}`;\n   }\n\n   private static counter = 0;\n}\n", "import { spawn, SpawnOptions } from 'child_process';\nimport { GitError } from '../errors/git-error';\nimport { OutputLogger } from '../git-logger';\nimport { PluginStore } from '../plugins';\nimport { EmptyTask, isBufferTask, isEmptyTask } from '../tasks/task';\nimport {\n   GitExecutorResult,\n   Maybe,\n   outputHandler,\n   RunnableTask,\n   SimpleGitExecutor,\n   SimpleGitTask,\n} from '../types';\nimport { callTaskParser, first, GitOutputStreams, objectToString } from '../utils';\nimport { Scheduler } from './scheduler';\nimport { TasksPendingQueue } from './tasks-pending-queue';\n\nexport class GitExecutorChain implements SimpleGitExecutor {\n   private _chain: Promise<any> = Promise.resolve();\n   private _queue = new TasksPendingQueue();\n   private _cwd: string | undefined;\n\n   public get cwd() {\n      return this._cwd || this._executor.cwd;\n   }\n\n   public set cwd(cwd: string) {\n      this._cwd = cwd;\n   }\n\n   public get env() {\n      return this._executor.env;\n   }\n\n   public get outputHandler() {\n      return this._executor.outputHandler;\n   }\n\n   constructor(\n      private _executor: SimpleGitExecutor,\n      private _scheduler: Scheduler,\n      private _plugins: PluginStore\n   ) {}\n\n   public chain() {\n      return this;\n   }\n\n   public push<R>(task: SimpleGitTask<R>): Promise<R> {\n      this._queue.push(task);\n\n      return (this._chain = this._chain.then(() => this.attemptTask(task)));\n   }\n\n   private async attemptTask<R>(task: SimpleGitTask<R>): Promise<void | R> {\n      const onScheduleComplete = await this._scheduler.next();\n      const onQueueComplete = () => this._queue.complete(task);\n\n      try {\n         const { logger } = this._queue.attempt(task);\n         return (await (isEmptyTask(task)\n            ? this.attemptEmptyTask(task, logger)\n            : this.attemptRemoteTask(task, logger))) as R;\n      } catch (e) {\n         throw this.onFatalException(task, e as Error);\n      } finally {\n         onQueueComplete();\n         onScheduleComplete();\n      }\n   }\n\n   private onFatalException<R>(task: SimpleGitTask<R>, e: Error) {\n      const gitError =\n         e instanceof GitError ? Object.assign(e, { task }) : new GitError(task, e && String(e));\n\n      this._chain = Promise.resolve();\n      this._queue.fatal(gitError);\n\n      return gitError;\n   }\n\n   private async attemptRemoteTask<R>(task: RunnableTask<R>, logger: OutputLogger) {\n      const binary = this._plugins.exec('spawn.binary', '', pluginContext(task, task.commands));\n      const args = this._plugins.exec(\n         'spawn.args',\n         [...task.commands],\n         pluginContext(task, task.commands)\n      );\n\n      const raw = await this.gitResponse(\n         task,\n         binary,\n         args,\n         this.outputHandler,\n         logger.step('SPAWN')\n      );\n      const outputStreams = await this.handleTaskData(task, args, raw, logger.step('HANDLE'));\n\n      logger(`passing response to task's parser as a %s`, task.format);\n\n      if (isBufferTask(task)) {\n         return callTaskParser(task.parser, outputStreams);\n      }\n\n      return callTaskParser(task.parser, outputStreams.asStrings());\n   }\n\n   private async attemptEmptyTask(task: EmptyTask, logger: OutputLogger) {\n      logger(`empty task bypassing child process to call to task's parser`);\n      return task.parser(this);\n   }\n\n   private handleTaskData<R>(\n      task: SimpleGitTask<R>,\n      args: string[],\n      result: GitExecutorResult,\n      logger: OutputLogger\n   ): Promise<GitOutputStreams> {\n      const { exitCode, rejection, stdOut, stdErr } = result;\n\n      return new Promise((done, fail) => {\n         logger(`Preparing to handle process response exitCode=%d stdOut=`, exitCode);\n\n         const { error } = this._plugins.exec(\n            'task.error',\n            { error: rejection },\n            {\n               ...pluginContext(task, args),\n               ...result,\n            }\n         );\n\n         if (error && task.onError) {\n            logger.info(`exitCode=%s handling with custom error handler`);\n\n            return task.onError(\n               result,\n               error,\n               (newStdOut) => {\n                  logger.info(`custom error handler treated as success`);\n                  logger(`custom error returned a %s`, objectToString(newStdOut));\n\n                  done(\n                     new GitOutputStreams(\n                        Array.isArray(newStdOut) ? Buffer.concat(newStdOut) : newStdOut,\n                        Buffer.concat(stdErr)\n                     )\n                  );\n               },\n               fail\n            );\n         }\n\n         if (error) {\n            logger.info(\n               `handling as error: exitCode=%s stdErr=%s rejection=%o`,\n               exitCode,\n               stdErr.length,\n               rejection\n            );\n            return fail(error);\n         }\n\n         logger.info(`retrieving task output complete`);\n         done(new GitOutputStreams(Buffer.concat(stdOut), Buffer.concat(stdErr)));\n      });\n   }\n\n   private async gitResponse<R>(\n      task: SimpleGitTask<R>,\n      command: string,\n      args: string[],\n      outputHandler: Maybe<outputHandler>,\n      logger: OutputLogger\n   ): Promise<GitExecutorResult> {\n      const outputLogger = logger.sibling('output');\n      const spawnOptions: SpawnOptions = this._plugins.exec(\n         'spawn.options',\n         {\n            cwd: this.cwd,\n            env: this.env,\n            windowsHide: true,\n         },\n         pluginContext(task, task.commands)\n      );\n\n      return new Promise((done) => {\n         const stdOut: Buffer[] = [];\n         const stdErr: Buffer[] = [];\n\n         logger.info(`%s %o`, command, args);\n         logger('%O', spawnOptions);\n\n         let rejection = this._beforeSpawn(task, args);\n         if (rejection) {\n            return done({\n               stdOut,\n               stdErr,\n               exitCode: 9901,\n               rejection,\n            });\n         }\n\n         this._plugins.exec('spawn.before', undefined, {\n            ...pluginContext(task, args),\n            kill(reason) {\n               rejection = reason || rejection;\n            },\n         });\n\n         const spawned = spawn(command, args, spawnOptions);\n\n         spawned.stdout!.on(\n            'data',\n            onDataReceived(stdOut, 'stdOut', logger, outputLogger.step('stdOut'))\n         );\n         spawned.stderr!.on(\n            'data',\n            onDataReceived(stdErr, 'stdErr', logger, outputLogger.step('stdErr'))\n         );\n\n         spawned.on('error', onErrorReceived(stdErr, logger));\n\n         if (outputHandler) {\n            logger(`Passing child process stdOut/stdErr to custom outputHandler`);\n            outputHandler(command, spawned.stdout!, spawned.stderr!, [...args]);\n         }\n\n         this._plugins.exec('spawn.after', undefined, {\n            ...pluginContext(task, args),\n            spawned,\n            close(exitCode: number, reason?: Error) {\n               done({\n                  stdOut,\n                  stdErr,\n                  exitCode,\n                  rejection: rejection || reason,\n               });\n            },\n            kill(reason: Error) {\n               if (spawned.killed) {\n                  return;\n               }\n\n               rejection = reason;\n               spawned.kill('SIGINT');\n            },\n         });\n      });\n   }\n\n   private _beforeSpawn<R>(task: SimpleGitTask<R>, args: string[]) {\n      let rejection: Maybe<Error>;\n      this._plugins.exec('spawn.before', undefined, {\n         ...pluginContext(task, args),\n         kill(reason) {\n            rejection = reason || rejection;\n         },\n      });\n\n      return rejection;\n   }\n}\n\nfunction pluginContext<R>(task: SimpleGitTask<R>, commands: string[]) {\n   return {\n      method: first(task.commands) || '',\n      commands,\n   };\n}\n\nfunction onErrorReceived(target: Buffer[], logger: OutputLogger) {\n   return (err: Error) => {\n      logger(`[ERROR] child process exception %o`, err);\n      target.push(Buffer.from(String(err.stack), 'ascii'));\n   };\n}\n\nfunction onDataReceived(\n   target: Buffer[],\n   name: string,\n   logger: OutputLogger,\n   output: OutputLogger\n) {\n   return (buffer: Buffer) => {\n      logger(`%s received %L bytes`, name, buffer);\n      output(`%B`, buffer);\n      target.push(buffer);\n   };\n}\n", "import type { PluginStore } from '../plugins';\nimport type { GitExecutorEnv, output<PERSON><PERSON><PERSON>, SimpleGitExecutor, SimpleGitTask } from '../types';\n\nimport { GitExecutor<PERSON>hain } from './git-executor-chain';\nimport { Scheduler } from './scheduler';\n\nexport class GitExecutor implements SimpleGitExecutor {\n   private _chain = new GitExecutorChain(this, this._scheduler, this._plugins);\n\n   public env: GitExecutorEnv;\n   public outputHandler?: outputHandler;\n\n   constructor(\n      public cwd: string,\n      private _scheduler: Scheduler,\n      private _plugins: PluginStore\n   ) {}\n\n   chain(): SimpleGitExecutor {\n      return new GitExecutorChain(this, this._scheduler, this._plugins);\n   }\n\n   push<R>(task: SimpleGitTask<R>): Promise<R> {\n      return this._chain.push(task);\n   }\n}\n", "import { GitError } from './errors/git-error';\nimport { GitResponseError } from './errors/git-response-error';\nimport { SimpleGitTask, SimpleGitTaskCallback } from './types';\nimport { NOOP } from './utils';\n\nexport function taskCallback<R>(\n   task: SimpleGitTask<R>,\n   response: Promise<R>,\n   callback: SimpleGitTaskCallback<R> = NOOP\n) {\n   const onSuccess = (data: R) => {\n      callback(null, data);\n   };\n\n   const onError = (err: GitError | GitResponseError) => {\n      if (err?.task === task) {\n         callback(\n            err instanceof GitResponseError ? addDeprecationNoticeToError(err) : err,\n            undefined as any\n         );\n      }\n   };\n\n   response.then(onSuccess, onError);\n}\n\nfunction addDeprecationNoticeToError(err: GitResponseError) {\n   let log = (name: string) => {\n      console.warn(\n         `simple-git deprecation notice: accessing GitResponseError.${name} should be GitResponseError.git.${name}, this will no longer be available in version 3`\n      );\n      log = NOOP;\n   };\n\n   return Object.create(err, Object.getOwnPropertyNames(err.git).reduce(descriptorReducer, {}));\n\n   function descriptorReducer(all: PropertyDescriptorMap, name: string): typeof all {\n      if (name in err) {\n         return all;\n      }\n\n      all[name] = {\n         enumerable: false,\n         configurable: false,\n         get() {\n            log(name);\n            return err.git[name];\n         },\n      };\n\n      return all;\n   }\n}\n", "import { folderExists } from '../utils';\nimport { SimpleGitExecutor } from '../types';\nimport { adhocExecTask } from './task';\n\nexport function changeWorkingDirectoryTask(directory: string, root?: SimpleGitExecutor) {\n   return adhocExecTask((instance: SimpleGitExecutor) => {\n      if (!folderExists(directory)) {\n         throw new Error(`Git.cwd: cannot change to non-directory \"${directory}\"`);\n      }\n\n      return ((root || instance).cwd = directory);\n   });\n}\n", "import type { SimpleGit } from '../../../typings';\nimport type { SimpleGitApi } from '../simple-git-api';\nimport { getTrailingOptions, remove, trailingFunctionArgument } from '../utils';\nimport { straightThroughStringTask } from './task';\n\nfunction checkoutTask(args: string[]) {\n   const commands = ['checkout', ...args];\n   if (commands[1] === '-b' && commands.includes('-B')) {\n      commands[1] = remove(commands, '-B');\n   }\n\n   return straightThroughStringTask(commands);\n}\n\nexport default function (): Pick<SimpleGit, 'checkout' | 'checkoutBranch' | 'checkoutLocalBranch'> {\n   return {\n      checkout(this: SimpleGitApi) {\n         return this._runTask(\n            checkoutTask(getTrailingOptions(arguments, 1)),\n            trailingFunctionArgument(arguments)\n         );\n      },\n\n      checkoutBranch(this: SimpleGitApi, branchName, startPoint) {\n         return this._runTask(\n            checkoutTask(['-b', branchName, startPoint, ...getTrailingOptions(arguments)]),\n            trailingFunctionArgument(arguments)\n         );\n      },\n\n      checkoutLocalBranch(this: SimpleGitApi, branchName) {\n         return this._runTask(\n            checkoutTask(['-b', branchName, ...getTrailingOptions(arguments)]),\n            trailingFunctionArgument(arguments)\n         );\n      },\n   };\n}\n", "import type { SimpleGitApi } from '../simple-git-api';\nimport type { SimpleGit } from '../../../typings';\nimport { asCamelCase, asNumber, LineParser, parseStringResponse } from '../utils';\n\nexport interface CountObjectsResult {\n   count: number;\n   size: number;\n   inPack: number;\n   packs: number;\n   sizePack: number;\n   prunePackable: number;\n   garbage: number;\n   sizeGarbage: number;\n}\n\nfunction countObjectsResponse(): CountObjectsResult {\n   return {\n      count: 0,\n      garbage: 0,\n      inPack: 0,\n      packs: 0,\n      prunePackable: 0,\n      size: 0,\n      sizeGarbage: 0,\n      sizePack: 0,\n   };\n}\n\nconst parser: LineParser<CountObjectsResult> = new LineParser(\n   /([a-z-]+): (\\d+)$/,\n   (result, [key, value]) => {\n      const property = asCamelCase(key);\n      if (result.hasOwnProperty(property)) {\n         result[property as keyof typeof result] = asNumber(value);\n      }\n   }\n);\n\nexport default function (): Pick<SimpleGit, 'countObjects'> {\n   return {\n      countObjects(this: SimpleGitApi) {\n         return this._runTask({\n            commands: ['count-objects', '--verbose'],\n            format: 'utf-8',\n            parser(stdOut: string) {\n               return parseStringResponse(countObjectsResponse(), [parser], stdOut);\n            },\n         });\n      },\n   };\n}\n", "import { CommitResult } from '../../../typings';\nimport { LineParser, parseStringResponse } from '../utils';\n\nconst parsers: LineParser<CommitResult>[] = [\n   new LineParser(/^\\[([^\\s]+)( \\([^)]+\\))? ([^\\]]+)/, (result, [branch, root, commit]) => {\n      result.branch = branch;\n      result.commit = commit;\n      result.root = !!root;\n   }),\n   new LineParser(/\\s*Author:\\s(.+)/i, (result, [author]) => {\n      const parts = author.split('<');\n      const email = parts.pop();\n\n      if (!email || !email.includes('@')) {\n         return;\n      }\n\n      result.author = {\n         email: email.substr(0, email.length - 1),\n         name: parts.join('<').trim(),\n      };\n   }),\n   new LineParser(\n      /(\\d+)[^,]*(?:,\\s*(\\d+)[^,]*)(?:,\\s*(\\d+))/g,\n      (result, [changes, insertions, deletions]) => {\n         result.summary.changes = parseInt(changes, 10) || 0;\n         result.summary.insertions = parseInt(insertions, 10) || 0;\n         result.summary.deletions = parseInt(deletions, 10) || 0;\n      }\n   ),\n   new LineParser(\n      /^(\\d+)[^,]*(?:,\\s*(\\d+)[^(]+\\(([+-]))?/,\n      (result, [changes, lines, direction]) => {\n         result.summary.changes = parseInt(changes, 10) || 0;\n         const count = parseInt(lines, 10) || 0;\n         if (direction === '-') {\n            result.summary.deletions = count;\n         } else if (direction === '+') {\n            result.summary.insertions = count;\n         }\n      }\n   ),\n];\n\nexport function parseCommitResult(stdOut: string): CommitResult {\n   const result: CommitResult = {\n      author: null,\n      branch: '',\n      commit: '',\n      root: false,\n      summary: {\n         changes: 0,\n         insertions: 0,\n         deletions: 0,\n      },\n   };\n   return parseStringResponse(result, parsers, stdOut);\n}\n", "import type { CommitResult, SimpleGit } from '../../../typings';\nimport type { SimpleGitApi } from '../simple-git-api';\nimport type { StringTask } from '../types';\nimport { parseCommitResult } from '../parsers/parse-commit';\nimport {\n   asArray,\n   filterArray,\n   filterStringOrStringArray,\n   filterType,\n   getTrailingOptions,\n   prefixedArray,\n   trailingFunctionArgument,\n} from '../utils';\nimport { configurationErrorTask } from './task';\n\nexport function commitTask(\n   message: string[],\n   files: string[],\n   customArgs: string[]\n): StringTask<CommitResult> {\n   const commands: string[] = [\n      '-c',\n      'core.abbrev=40',\n      'commit',\n      ...prefixedArray(message, '-m'),\n      ...files,\n      ...customArgs,\n   ];\n\n   return {\n      commands,\n      format: 'utf-8',\n      parser: parseCommitResult,\n   };\n}\n\nexport default function (): Pick<SimpleGit, 'commit'> {\n   return {\n      commit(this: SimpleGit<PERSON><PERSON>, message: string | string[], ...rest: unknown[]) {\n         const next = trailingFunctionArgument(arguments);\n         const task =\n            rejectDeprecatedSignatures(message) ||\n            commitTask(\n               asArray(message),\n               asArray(filterType(rest[0], filterStringOrStringArray, [])),\n               [...filterType(rest[1], filterArray, []), ...getTrailingOptions(arguments, 0, true)]\n            );\n\n         return this._runTask(task, next);\n      },\n   };\n\n   function rejectDeprecatedSignatures(message?: unknown) {\n      return (\n         !filterStringOrStringArray(message) &&\n         configurationErrorTask(\n            `git.commit: requires the commit message to be supplied as a string/string[]`\n         )\n      );\n   }\n}\n", "import { Response, SimpleGit } from '../../../typings';\nimport { SimpleGitApi } from '../simple-git-api';\nimport { trailingFunctionArgument } from '../utils';\nimport { straightThroughStringTask } from './task';\n\nexport default function (): Pick<SimpleGit, 'firstCommit'> {\n   return {\n      firstCommit(this: SimpleGitApi): Response<string> {\n         return this._runTask(\n            straightThroughStringTask(['rev-list', '--max-parents=0', 'HEAD'], true),\n            trailingFunctionArgument(arguments)\n         );\n      },\n   };\n}\n", "import { straightThroughStringTask } from './task';\nimport { StringTask } from '../types';\n\n/**\n * Task used by `git.hashObject`\n */\nexport function hashObjectTask(filePath: string, write: boolean): StringTask<string> {\n   const commands = ['hash-object', filePath];\n   if (write) {\n      commands.push('-w');\n   }\n\n   return straightThroughStringTask(commands, true);\n}\n", "import { InitResult } from '../../../typings';\n\nexport class InitSummary implements InitResult {\n   constructor(\n      public readonly bare: boolean,\n      public readonly path: string,\n      public readonly existing: boolean,\n      public readonly gitDir: string\n   ) {}\n}\n\nconst initResponseRegex = /^Init.+ repository in (.+)$/;\nconst reInitResponseRegex = /^Rein.+ in (.+)$/;\n\nexport function parseInit(bare: boolean, path: string, text: string) {\n   const response = String(text).trim();\n   let result;\n\n   if ((result = initResponseRegex.exec(response))) {\n      return new InitSummary(bare, path, false, result[1]);\n   }\n\n   if ((result = reInitResponseRegex.exec(response))) {\n      return new InitSummary(bare, path, true, result[1]);\n   }\n\n   let gitDir = '';\n   const tokens = response.split(' ');\n   while (tokens.length) {\n      const token = tokens.shift();\n      if (token === 'in') {\n         gitDir = tokens.join(' ');\n         break;\n      }\n   }\n\n   return new InitSummary(bare, path, /^re/i.test(response), gitDir);\n}\n", "import { InitResult } from '../../../typings';\nimport { parseInit } from '../responses/InitSummary';\nimport { StringTask } from '../types';\n\nconst bareCommand = '--bare';\n\nfunction hasBareCommand(command: string[]) {\n   return command.includes(bareCommand);\n}\n\nexport function initTask(bare = false, path: string, customArgs: string[]): StringTask<InitResult> {\n   const commands = ['init', ...customArgs];\n   if (bare && !hasBareCommand(commands)) {\n      commands.splice(1, 0, bareCommand);\n   }\n\n   return {\n      commands,\n      format: 'utf-8',\n      parser(text: string): InitResult {\n         return parseInit(commands.includes('--bare'), path, text);\n      },\n   };\n}\n", "export enum LogFormat {\n   NONE = '',\n   STAT = '--stat',\n   NUM_STAT = '--numstat',\n   NAME_ONLY = '--name-only',\n   NAME_STATUS = '--name-status',\n}\n\nconst logFormatRegex = /^--(stat|numstat|name-only|name-status)(=|$)/;\n\nexport function logFormatFromCommand(customArgs: string[]) {\n   for (let i = 0; i < customArgs.length; i++) {\n      const format = logFormatRegex.exec(customArgs[i]);\n      if (format) {\n         return `--${format[1]}` as LogFormat;\n      }\n   }\n\n   return LogFormat.NONE;\n}\n\nexport function isLogFormat(customArg: string | unknown) {\n   return logFormatRegex.test(customArg as string);\n}\n", "import { DiffResult, DiffResultBinaryFile, DiffResultTextFile } from '../../../typings';\n\n/***\n * The DiffSummary is returned as a response to getting `git().status()`\n */\nexport class DiffSummary implements DiffResult {\n   changed = 0;\n   deletions = 0;\n   insertions = 0;\n\n   files: Array<DiffResultTextFile | DiffResultBinaryFile> = [];\n}\n", "import { DiffResult } from '../../../typings';\nimport { LogFormat } from '../args/log-format';\nimport { DiffSummary } from '../responses/DiffSummary';\nimport { isDiffNameStatus } from '../tasks/diff-name-status';\nimport { asNumber, LineParser, orVoid, parseStringResponse } from '../utils';\n\nconst statParser = [\n   new LineParser<DiffResult>(\n      /^(.+)\\s+\\|\\s+(\\d+)(\\s+[+\\-]+)?$/,\n      (result, [file, changes, alterations = '']) => {\n         result.files.push({\n            file: file.trim(),\n            changes: asNumber(changes),\n            insertions: alterations.replace(/[^+]/g, '').length,\n            deletions: alterations.replace(/[^-]/g, '').length,\n            binary: false,\n         });\n      }\n   ),\n   new LineParser<DiffResult>(\n      /^(.+) \\|\\s+Bin ([0-9.]+) -> ([0-9.]+) ([a-z]+)/,\n      (result, [file, before, after]) => {\n         result.files.push({\n            file: file.trim(),\n            before: asNumber(before),\n            after: asNumber(after),\n            binary: true,\n         });\n      }\n   ),\n   new LineParser<DiffResult>(\n      /(\\d+) files? changed\\s*((?:, \\d+ [^,]+){0,2})/,\n      (result, [changed, summary]) => {\n         const inserted = /(\\d+) i/.exec(summary);\n         const deleted = /(\\d+) d/.exec(summary);\n\n         result.changed = asNumber(changed);\n         result.insertions = asNumber(inserted?.[1]);\n         result.deletions = asNumber(deleted?.[1]);\n      }\n   ),\n];\n\nconst numStatParser = [\n   new LineParser<DiffResult>(\n      /(\\d+)\\t(\\d+)\\t(.+)$/,\n      (result, [changesInsert, changesDelete, file]) => {\n         const insertions = asNumber(changesInsert);\n         const deletions = asNumber(changesDelete);\n\n         result.changed++;\n         result.insertions += insertions;\n         result.deletions += deletions;\n\n         result.files.push({\n            file,\n            changes: insertions + deletions,\n            insertions,\n            deletions,\n            binary: false,\n         });\n      }\n   ),\n   new LineParser<DiffResult>(/-\\t-\\t(.+)$/, (result, [file]) => {\n      result.changed++;\n\n      result.files.push({\n         file,\n         after: 0,\n         before: 0,\n         binary: true,\n      });\n   }),\n];\n\nconst nameOnlyParser = [\n   new LineParser<DiffResult>(/(.+)$/, (result, [file]) => {\n      result.changed++;\n      result.files.push({\n         file,\n         changes: 0,\n         insertions: 0,\n         deletions: 0,\n         binary: false,\n      });\n   }),\n];\n\nconst nameStatusParser = [\n   new LineParser<DiffResult>(\n      /([ACDMRTUXB])([0-9]{0,3})\\t(.[^\\t]*)(\\t(.[^\\t]*))?$/,\n      (result, [status, similarity, from, _to, to]) => {\n         result.changed++;\n         result.files.push({\n            file: to ?? from,\n            changes: 0,\n            insertions: 0,\n            deletions: 0,\n            binary: false,\n            status: orVoid(isDiffNameStatus(status) && status),\n            from: orVoid(!!to && from !== to && from),\n            similarity: asNumber(similarity),\n         });\n      }\n   ),\n];\n\nconst diffSummaryParsers: Record<LogFormat, LineParser<DiffResult>[]> = {\n   [LogFormat.NONE]: statParser,\n   [LogFormat.STAT]: statParser,\n   [LogFormat.NUM_STAT]: numStatParser,\n   [LogFormat.NAME_STATUS]: nameStatusParser,\n   [LogFormat.NAME_ONLY]: nameOnlyParser,\n};\n\nexport function getDiffParser(format = LogFormat.NONE) {\n   const parser = diffSummaryParsers[format];\n\n   return (stdOut: string) => parseStringResponse(new DiffSummary(), parser, stdOut, false);\n}\n", "import { ListLogLine, LogResult } from '../../../typings';\nimport { toLinesWithContent } from '../utils';\nimport { getDiffParser } from './parse-diff-summary';\nimport { LogFormat } from '../args/log-format';\n\nexport const START_BOUNDARY = 'òòòòòò ';\n\nexport const COMMIT_BOUNDARY = ' òò';\n\nexport const SPLITTER = ' ò ';\n\nconst defaultFieldNames = ['hash', 'date', 'message', 'refs', 'author_name', 'author_email'];\n\nfunction lineBuilder(tokens: string[], fields: string[]): any {\n   return fields.reduce(\n      (line, field, index) => {\n         line[field] = tokens[index] || '';\n         return line;\n      },\n      Object.create({ diff: null }) as any\n   );\n}\n\nexport function createListLogSummaryParser<T = any>(\n   splitter = SPLITTER,\n   fields = defaultFieldNames,\n   logFormat = LogFormat.NONE\n) {\n   const parseDiffResult = getDiffParser(logFormat);\n\n   return function (stdOut: string): LogResult<T> {\n      const all: ReadonlyArray<T & ListLogLine> = toLinesWithContent(\n         stdOut.trim(),\n         false,\n         START_BOUNDARY\n      ).map(function (item) {\n         const lineDetail = item.split(COMMIT_BOUNDARY);\n         const listLogLine: T & ListLogLine = lineBuilder(lineDetail[0].split(splitter), fields);\n\n         if (lineDetail.length > 1 && !!lineDetail[1].trim()) {\n            listLogLine.diff = parseDiffResult(lineDetail[1]);\n         }\n\n         return listLogLine;\n      });\n\n      return {\n         all,\n         latest: (all.length && all[0]) || null,\n         total: all.length,\n      };\n   };\n}\n", "import { StringTask } from '../types';\nimport { DiffResult } from '../../../typings';\nimport { isLogFormat, LogFormat, logFormatFromCommand } from '../args/log-format';\nimport { getDiffParser } from '../parsers/parse-diff-summary';\nimport { configurationErrorTask, EmptyTask } from './task';\n\nexport function diffSummaryTask(customArgs: string[]): StringTask<DiffResult> | EmptyTask {\n   let logFormat = logFormatFromCommand(customArgs);\n\n   const commands = ['diff'];\n\n   if (logFormat === LogFormat.NONE) {\n      logFormat = LogFormat.STAT;\n      commands.push('--stat=4096');\n   }\n\n   commands.push(...customArgs);\n\n   return (\n      validateLogFormatConfig(commands) || {\n         commands,\n         format: 'utf-8',\n         parser: getDiffParser(logFormat),\n      }\n   );\n}\n\nexport function validateLogFormatConfig(customArgs: unknown[]): EmptyTask | void {\n   const flags = customArgs.filter(isLogFormat);\n\n   if (flags.length > 1) {\n      return configurationErrorTask(\n         `Summary flags are mutually exclusive - pick one of ${flags.join(',')}`\n      );\n   }\n\n   if (flags.length && customArgs.includes('-z')) {\n      return configurationErrorTask(\n         `Summary flag ${flags} parsing is not compatible with null termination option '-z'`\n      );\n   }\n}\n", "import type { Options, StringTask } from '../types';\nimport type { LogResult, SimpleGit } from '../../../typings';\nimport { logFormatFromCommand } from '../args/log-format';\nimport { pathspec } from '../args/pathspec';\nimport {\n   COMMIT_BOUNDARY,\n   createListLogSummaryParser,\n   SPLITTER,\n   START_BOUNDARY,\n} from '../parsers/parse-list-log-summary';\nimport {\n   appendTaskOptions,\n   filterArray,\n   filterPrimitives,\n   filterString,\n   filterType,\n   trailingFunctionArgument,\n   trailingOptionsArgument,\n} from '../utils';\nimport { SimpleGitApi } from '../simple-git-api';\nimport { configurationErrorTask } from './task';\nimport { validateLogFormatConfig } from './diff';\n\nenum excludeOptions {\n   '--pretty',\n   'max-count',\n   'maxCount',\n   'n',\n   'file',\n   'format',\n   'from',\n   'to',\n   'splitter',\n   'symmetric',\n   'mailMap',\n   'multiLine',\n   'strictDate',\n}\n\nexport interface DefaultLogFields {\n   hash: string;\n   date: string;\n   message: string;\n   refs: string;\n   body: string;\n   author_name: string;\n   author_email: string;\n}\n\nexport type LogOptions<T = DefaultLogFields> = {\n   file?: string;\n   format?: T;\n   from?: string;\n   mailMap?: boolean;\n   maxCount?: number;\n   multiLine?: boolean;\n   splitter?: string;\n   strictDate?: boolean;\n   symmetric?: boolean;\n   to?: string;\n};\n\ninterface ParsedLogOptions {\n   fields: string[];\n   splitter: string;\n   commands: string[];\n}\n\nfunction prettyFormat(\n   format: Record<string, string | unknown>,\n   splitter: string\n): [string[], string] {\n   const fields: string[] = [];\n   const formatStr: string[] = [];\n\n   Object.keys(format).forEach((field) => {\n      fields.push(field);\n      formatStr.push(String(format[field]));\n   });\n\n   return [fields, formatStr.join(splitter)];\n}\n\nfunction userOptions<T extends Options>(input: T): Options {\n   return Object.keys(input).reduce((out, key) => {\n      if (!(key in excludeOptions)) {\n         out[key] = input[key];\n      }\n      return out;\n   }, {} as Options);\n}\n\nexport function parseLogOptions<T extends Options>(\n   opt: Options | LogOptions<T> = {},\n   customArgs: string[] = []\n): ParsedLogOptions {\n   const splitter = filterType(opt.splitter, filterString, SPLITTER);\n   const format =\n      !filterPrimitives(opt.format) && opt.format\n         ? opt.format\n         : {\n              hash: '%H',\n              date: opt.strictDate === false ? '%ai' : '%aI',\n              message: '%s',\n              refs: '%D',\n              body: opt.multiLine ? '%B' : '%b',\n              author_name: opt.mailMap !== false ? '%aN' : '%an',\n              author_email: opt.mailMap !== false ? '%aE' : '%ae',\n           };\n\n   const [fields, formatStr] = prettyFormat(format, splitter);\n\n   const suffix: string[] = [];\n   const command: string[] = [\n      `--pretty=format:${START_BOUNDARY}${formatStr}${COMMIT_BOUNDARY}`,\n      ...customArgs,\n   ];\n\n   const maxCount: number | undefined = (opt as any).n || (opt as any)['max-count'] || opt.maxCount;\n   if (maxCount) {\n      command.push(`--max-count=${maxCount}`);\n   }\n\n   if (opt.from || opt.to) {\n      const rangeOperator = opt.symmetric !== false ? '...' : '..';\n      suffix.push(`${opt.from || ''}${rangeOperator}${opt.to || ''}`);\n   }\n\n   if (filterString(opt.file)) {\n      command.push('--follow', pathspec(opt.file));\n   }\n\n   appendTaskOptions(userOptions(opt as Options), command);\n\n   return {\n      fields,\n      splitter,\n      commands: [...command, ...suffix],\n   };\n}\n\nexport function logTask<T>(\n   splitter: string,\n   fields: string[],\n   customArgs: string[]\n): StringTask<LogResult<T>> {\n   const parser = createListLogSummaryParser(splitter, fields, logFormatFromCommand(customArgs));\n\n   return {\n      commands: ['log', ...customArgs],\n      format: 'utf-8',\n      parser,\n   };\n}\n\nexport default function (): Pick<SimpleGit, 'log'> {\n   return {\n      log<T extends Options>(this: SimpleGitApi, ...rest: unknown[]) {\n         const next = trailingFunctionArgument(arguments);\n         const options = parseLogOptions<T>(\n            trailingOptionsArgument(arguments),\n            filterType(arguments[0], filterArray)\n         );\n         const task =\n            rejectDeprecatedSignatures(...rest) ||\n            validateLogFormatConfig(options.commands) ||\n            createLogTask(options);\n\n         return this._runTask(task, next);\n      },\n   };\n\n   function createLogTask(options: ParsedLogOptions) {\n      return logTask(options.splitter, options.fields, options.commands);\n   }\n\n   function rejectDeprecatedSignatures(from?: unknown, to?: unknown) {\n      return (\n         filterString(from) &&\n         filterString(to) &&\n         configurationErrorTask(\n            `git.log(string, string) should be replaced with git.log({ from: string, to: string })`\n         )\n      );\n   }\n}\n", "import {\n   MergeConflict,\n   MergeConflictDeletion,\n   MergeDetail,\n   MergeResultStatus,\n} from '../../../typings';\n\nexport class MergeSummaryConflict implements MergeConflict {\n   constructor(\n      public readonly reason: string,\n      public readonly file: string | null = null,\n      public readonly meta?: MergeConflictDeletion\n   ) {}\n\n   toString() {\n      return `${this.file}:${this.reason}`;\n   }\n}\n\nexport class MergeSummaryDetail implements MergeDetail {\n   public conflicts: MergeConflict[] = [];\n   public merges: string[] = [];\n   public result: MergeResultStatus = 'success';\n\n   get failed() {\n      return this.conflicts.length > 0;\n   }\n\n   get reason() {\n      return this.result;\n   }\n\n   toString() {\n      if (this.conflicts.length) {\n         return `CONFLICTS: ${this.conflicts.join(', ')}`;\n      }\n\n      return 'OK';\n   }\n}\n", "import {\n   PullDetailFileChanges,\n   PullDetailSummary,\n   PullFailedResult,\n   PullResult,\n} from '../../../typings';\n\nexport class PullSummary implements PullResult {\n   public remoteMessages = {\n      all: [],\n   };\n   public created = [];\n   public deleted: string[] = [];\n   public files: string[] = [];\n   public deletions: PullDetailFileChanges = {};\n   public insertions: PullDetailFileChanges = {};\n   public summary: PullDetailSummary = {\n      changes: 0,\n      deletions: 0,\n      insertions: 0,\n   };\n}\n\nexport class PullFailedSummary implements PullFailedResult {\n   remote = '';\n   hash = {\n      local: '',\n      remote: '',\n   };\n   branch = {\n      local: '',\n      remote: '',\n   };\n   message = '';\n\n   toString() {\n      return this.message;\n   }\n}\n", "import {\n   RemoteMessageResult,\n   RemoteMessages,\n   RemoteMessagesObjectEnumeration,\n} from '../../../typings';\nimport { asNumber, RemoteLineParser } from '../utils';\n\nfunction objectEnumerationResult<T extends RemoteMessages = RemoteMessages>(\n   remoteMessages: T\n): RemoteMessagesObjectEnumeration {\n   return (remoteMessages.objects = remoteMessages.objects || {\n      compressing: 0,\n      counting: 0,\n      enumerating: 0,\n      packReused: 0,\n      reused: { count: 0, delta: 0 },\n      total: { count: 0, delta: 0 },\n   });\n}\n\nfunction asObjectCount(source: string) {\n   const count = /^\\s*(\\d+)/.exec(source);\n   const delta = /delta (\\d+)/i.exec(source);\n\n   return {\n      count: asNumber((count && count[1]) || '0'),\n      delta: asNumber((delta && delta[1]) || '0'),\n   };\n}\n\nexport const remoteMessagesObjectParsers: RemoteLineParser<RemoteMessageResult<RemoteMessages>>[] =\n   [\n      new RemoteLineParser(\n         /^remote:\\s*(enumerating|counting|compressing) objects: (\\d+),/i,\n         (result, [action, count]) => {\n            const key = action.toLowerCase();\n            const enumeration = objectEnumerationResult(result.remoteMessages);\n\n            Object.assign(enumeration, { [key]: asNumber(count) });\n         }\n      ),\n      new RemoteLineParser(\n         /^remote:\\s*(enumerating|counting|compressing) objects: \\d+% \\(\\d+\\/(\\d+)\\),/i,\n         (result, [action, count]) => {\n            const key = action.toLowerCase();\n            const enumeration = objectEnumerationResult(result.remoteMessages);\n\n            Object.assign(enumeration, { [key]: asNumber(count) });\n         }\n      ),\n      new RemoteLineParser(\n         /total ([^,]+), reused ([^,]+), pack-reused (\\d+)/i,\n         (result, [total, reused, packReused]) => {\n            const objects = objectEnumerationResult(result.remoteMessages);\n            objects.total = asObjectCount(total);\n            objects.reused = asObjectCount(reused);\n            objects.packReused = asNumber(packReused);\n         }\n      ),\n   ];\n", "import { PushResultRemoteMessages, RemoteMessageResult, RemoteMessages } from '../../../typings';\nimport { asNumber, parseStringResponse, RemoteLineParser } from '../utils';\nimport { remoteMessagesObjectParsers } from './parse-remote-objects';\n\nconst parsers: RemoteLineParser<RemoteMessageResult<PushResultRemoteMessages | RemoteMessages>>[] =\n   [\n      new RemoteLineParser(/^remote:\\s*(.+)$/, (result, [text]) => {\n         result.remoteMessages.all.push(text.trim());\n         return false;\n      }),\n      ...remoteMessagesObjectParsers,\n      new RemoteLineParser(\n         [/create a (?:pull|merge) request/i, /\\s(https?:\\/\\/\\S+)$/],\n         (result, [pullRequestUrl]) => {\n            (result.remoteMessages as PushResultRemoteMessages).pullRequestUrl = pullRequestUrl;\n         }\n      ),\n      new RemoteLineParser(\n         [/found (\\d+) vulnerabilities.+\\(([^)]+)\\)/i, /\\s(https?:\\/\\/\\S+)$/],\n         (result, [count, summary, url]) => {\n            (result.remoteMessages as PushResultRemoteMessages).vulnerabilities = {\n               count: asNumber(count),\n               summary,\n               url,\n            };\n         }\n      ),\n   ];\n\nexport function parseRemoteMessages<T extends RemoteMessages = RemoteMessages>(\n   _stdOut: string,\n   stdErr: string\n): RemoteMessageResult {\n   return parseStringResponse({ remoteMessages: new RemoteMessageSummary() as T }, parsers, stdErr);\n}\n\nexport class RemoteMessageSummary implements RemoteMessages {\n   public readonly all: string[] = [];\n}\n", "import { <PERSON>ull<PERSON><PERSON><PERSON>, <PERSON>ull<PERSON>ailed<PERSON><PERSON><PERSON>, PullR<PERSON>ult, RemoteMessages } from '../../../typings';\nimport { PullFailedSummary, PullSummary } from '../responses/PullSummary';\nimport { TaskParser } from '../types';\nimport { append, LineParser, parseStringResponse } from '../utils';\nimport { parseRemoteMessages } from './parse-remote-messages';\n\nconst FILE_UPDATE_REGEX = /^\\s*(.+?)\\s+\\|\\s+\\d+\\s*(\\+*)(-*)/;\nconst SUMMARY_REGEX = /(\\d+)\\D+((\\d+)\\D+\\(\\+\\))?(\\D+(\\d+)\\D+\\(-\\))?/;\nconst ACTION_REGEX = /^(create|delete) mode \\d+ (.+)/;\n\nconst parsers: LineParser<PullResult>[] = [\n   new LineParser(FILE_UPDATE_REGEX, (result, [file, insertions, deletions]) => {\n      result.files.push(file);\n\n      if (insertions) {\n         result.insertions[file] = insertions.length;\n      }\n\n      if (deletions) {\n         result.deletions[file] = deletions.length;\n      }\n   }),\n   new LineParser(SUMMARY_REGEX, (result, [changes, , insertions, , deletions]) => {\n      if (insertions !== undefined || deletions !== undefined) {\n         result.summary.changes = +changes || 0;\n         result.summary.insertions = +insertions || 0;\n         result.summary.deletions = +deletions || 0;\n         return true;\n      }\n      return false;\n   }),\n   new LineParser(ACTION_REGEX, (result, [action, file]) => {\n      append(result.files, file);\n      append(action === 'create' ? result.created : result.deleted, file);\n   }),\n];\n\nconst errorParsers: LineParser<PullFailedResult>[] = [\n   new LineParser(/^from\\s(.+)$/i, (result, [remote]) => void (result.remote = remote)),\n   new LineParser(/^fatal:\\s(.+)$/, (result, [message]) => void (result.message = message)),\n   new LineParser(\n      /([a-z0-9]+)\\.\\.([a-z0-9]+)\\s+(\\S+)\\s+->\\s+(\\S+)$/,\n      (result, [hashLocal, hashRemote, branchLocal, branchRemote]) => {\n         result.branch.local = branchLocal;\n         result.hash.local = hashLocal;\n         result.branch.remote = branchRemote;\n         result.hash.remote = hashRemote;\n      }\n   ),\n];\n\nexport const parsePullDetail: TaskParser<string, PullDetail> = (stdOut, stdErr) => {\n   return parseStringResponse(new PullSummary(), parsers, [stdOut, stdErr]);\n};\n\nexport const parsePullResult: TaskParser<string, PullResult> = (stdOut, stdErr) => {\n   return Object.assign(\n      new PullSummary(),\n      parsePullDetail(stdOut, stdErr),\n      parseRemoteMessages<RemoteMessages>(stdOut, stdErr)\n   );\n};\n\nexport function parsePullErrorResult(stdOut: string, stdErr: string) {\n   const pullError = parseStringResponse(new PullFailedSummary(), errorParsers, [stdOut, stdErr]);\n\n   return pullError.message && pullError;\n}\n", "import { MergeDetail, MergeResult } from '../../../typings';\nimport { MergeSummaryConflict, MergeSummaryDetail } from '../responses/MergeSummary';\nimport { TaskParser } from '../types';\nimport { LineParser, parseStringResponse } from '../utils';\nimport { parsePullResult } from './parse-pull';\n\nconst parsers: LineParser<MergeDetail>[] = [\n   new LineParser(/^Auto-merging\\s+(.+)$/, (summary, [autoMerge]) => {\n      summary.merges.push(autoMerge);\n   }),\n   new LineParser(/^CONFLICT\\s+\\((.+)\\): Merge conflict in (.+)$/, (summary, [reason, file]) => {\n      summary.conflicts.push(new MergeSummaryConflict(reason, file));\n   }),\n   new LineParser(\n      /^CONFLICT\\s+\\((.+\\/delete)\\): (.+) deleted in (.+) and/,\n      (summary, [reason, file, deleteRef]) => {\n         summary.conflicts.push(new MergeSummaryConflict(reason, file, { deleteRef }));\n      }\n   ),\n   new LineParser(/^CONFLICT\\s+\\((.+)\\):/, (summary, [reason]) => {\n      summary.conflicts.push(new MergeSummaryConflict(reason, null));\n   }),\n   new LineParser(/^Automatic merge failed;\\s+(.+)$/, (summary, [result]) => {\n      summary.result = result;\n   }),\n];\n\n/**\n * Parse the complete response from `git.merge`\n */\nexport const parseMergeResult: TaskParser<string, MergeResult> = (stdOut, stdErr) => {\n   return Object.assign(parseMergeDetail(stdOut, stdErr), parsePullResult(stdOut, stdErr));\n};\n\n/**\n * Parse the merge specific detail (ie: not the content also available in the pull detail) from `git.mnerge`\n * @param stdOut\n */\nexport const parseMergeDetail: TaskParser<string, MergeDetail> = (stdOut) => {\n   return parseStringResponse(new MergeSummaryDetail(), parsers, stdOut);\n};\n", "import { MergeResult } from '../../../typings';\nimport { GitResponseError } from '../errors/git-response-error';\nimport { parseMergeResult } from '../parsers/parse-merge';\nimport { StringTask } from '../types';\nimport { configurationErrorTask, EmptyTask } from './task';\n\nexport function mergeTask(customArgs: string[]): EmptyTask | StringTask<MergeResult> {\n   if (!customArgs.length) {\n      return configurationErrorTask('Git.merge requires at least one option');\n   }\n\n   return {\n      commands: ['merge', ...customArgs],\n      format: 'utf-8',\n      parser(stdOut, stdErr): MergeResult {\n         const merge = parseMergeResult(stdOut, stdErr);\n         if (merge.failed) {\n            throw new GitResponseError(merge);\n         }\n\n         return merge;\n      },\n   };\n}\n", "import {\n   PushDetail,\n   PushResult,\n   PushResultPushedItem,\n   PushResultRemoteMessages,\n} from '../../../typings';\nimport { TaskParser } from '../types';\nimport { LineParser, parseStringResponse } from '../utils';\nimport { parseRemoteMessages } from './parse-remote-messages';\n\nfunction pushResultPushedItem(local: string, remote: string, status: string): PushResultPushedItem {\n   const deleted = status.includes('deleted');\n   const tag = status.includes('tag') || /^refs\\/tags/.test(local);\n   const alreadyUpdated = !status.includes('new');\n\n   return {\n      deleted,\n      tag,\n      branch: !tag,\n      new: !alreadyUpdated,\n      alreadyUpdated,\n      local,\n      remote,\n   };\n}\n\nconst parsers: LineParser<PushDetail>[] = [\n   new LineParser(/^Pushing to (.+)$/, (result, [repo]) => {\n      result.repo = repo;\n   }),\n   new LineParser(/^updating local tracking ref '(.+)'/, (result, [local]) => {\n      result.ref = {\n         ...(result.ref || {}),\n         local,\n      };\n   }),\n   new LineParser(/^[=*-]\\s+([^:]+):(\\S+)\\s+\\[(.+)]$/, (result, [local, remote, type]) => {\n      result.pushed.push(pushResultPushedItem(local, remote, type));\n   }),\n   new LineParser(\n      /^Branch '([^']+)' set up to track remote branch '([^']+)' from '([^']+)'/,\n      (result, [local, remote, remoteName]) => {\n         result.branch = {\n            ...(result.branch || {}),\n            local,\n            remote,\n            remoteName,\n         };\n      }\n   ),\n   new LineParser(\n      /^([^:]+):(\\S+)\\s+([a-z0-9]+)\\.\\.([a-z0-9]+)$/,\n      (result, [local, remote, from, to]) => {\n         result.update = {\n            head: {\n               local,\n               remote,\n            },\n            hash: {\n               from,\n               to,\n            },\n         };\n      }\n   ),\n];\n\nexport const parsePushResult: TaskParser<string, PushResult> = (stdOut, stdErr) => {\n   const pushDetail = parsePushDetail(stdOut, stdErr);\n   const responseDetail = parseRemoteMessages<PushResultRemoteMessages>(stdOut, stdErr);\n\n   return {\n      ...pushDetail,\n      ...responseDetail,\n   };\n};\n\nexport const parsePushDetail: TaskParser<string, PushDetail> = (stdOut, stdErr) => {\n   return parseStringResponse({ pushed: [] }, parsers, [stdOut, stdErr]);\n};\n", "import { PushResult } from '../../../typings';\nimport { parsePushResult as parser } from '../parsers/parse-push';\nimport { StringTask } from '../types';\nimport { append, remove } from '../utils';\n\ntype PushRef = { remote?: string; branch?: string };\n\nexport function pushTagsTask(ref: PushRef = {}, customArgs: string[]): StringTask<PushResult> {\n   append(customArgs, '--tags');\n   return pushTask(ref, customArgs);\n}\n\nexport function pushTask(ref: PushRef = {}, customArgs: string[]): StringTask<PushResult> {\n   const commands = ['push', ...customArgs];\n   if (ref.branch) {\n      commands.splice(1, 0, ref.branch);\n   }\n   if (ref.remote) {\n      commands.splice(1, 0, ref.remote);\n   }\n\n   remove(commands, '-v');\n   append(commands, '--verbose');\n   append(commands, '--porcelain');\n\n   return {\n      commands,\n      format: 'utf-8',\n      parser,\n   };\n}\n", "import { SimpleGit } from '../../../typings';\nimport { SimpleGitApi } from '../simple-git-api';\nimport { getTrailingOptions, trailingFunctionArgument } from '../utils';\nimport { straightThroughBufferTask, straightThroughStringTask } from './task';\n\nexport default function (): Pick<SimpleGit, 'showBuffer' | 'show'> {\n   return {\n      showBuffer(this: SimpleGitApi) {\n         const commands = ['show', ...getTrailingOptions(arguments, 1)];\n         if (!commands.includes('--binary')) {\n            commands.splice(1, 0, '--binary');\n         }\n\n         return this._runTask(\n            straightThroughBufferTask(commands),\n            trailingFunctionArgument(arguments)\n         );\n      },\n\n      show(this: SimpleGitApi) {\n         const commands = ['show', ...getTrailingOptions(arguments, 1)];\n         return this._runTask(\n            straightThroughStringTask(commands),\n            trailingFunctionArgument(arguments)\n         );\n      },\n   };\n}\n", "import { FileStatusResult } from '../../../typings';\n\nexport const fromPathRegex = /^(.+)\\0(.+)$/;\n\nexport class FileStatusSummary implements FileStatusResult {\n   public readonly from: string | undefined;\n\n   constructor(\n      public path: string,\n      public index: string,\n      public working_dir: string\n   ) {\n      if (index === 'R' || working_dir === 'R') {\n         const detail = fromPathRegex.exec(path) || [null, path, path];\n         this.from = detail[2] || '';\n         this.path = detail[1] || '';\n      }\n   }\n}\n", "import { StatusResult } from '../../../typings';\nimport { append, NULL } from '../utils';\nimport { FileStatusSummary } from './FileStatusSummary';\n\ntype StatusLineParser = (result: StatusResult, file: string) => void;\n\nexport class StatusSummary implements StatusResult {\n   public not_added = [];\n   public conflicted = [];\n   public created = [];\n   public deleted = [];\n   public ignored = undefined;\n   public modified = [];\n   public renamed = [];\n   public files = [];\n   public staged = [];\n   public ahead = 0;\n   public behind = 0;\n   public current = null;\n   public tracking = null;\n   public detached = false;\n\n   public isClean = () => {\n      return !this.files.length;\n   };\n}\n\nenum PorcelainFileStatus {\n   ADDED = 'A',\n   DELETED = 'D',\n   MODIFIED = 'M',\n   RENAMED = 'R',\n   COPIED = 'C',\n   UNMERGED = 'U',\n   UNTRACKED = '?',\n   IGNORED = '!',\n   NONE = ' ',\n}\n\nfunction renamedFile(line: string) {\n   const [to, from] = line.split(NULL);\n\n   return {\n      from: from || to,\n      to,\n   };\n}\n\nfunction parser(\n   indexX: PorcelainFileStatus,\n   indexY: PorcelainFileStatus,\n   handler: StatusLineParser\n): [string, StatusLineParser] {\n   return [`${indexX}${indexY}`, handler];\n}\n\nfunction conflicts(indexX: PorcelainFileStatus, ...indexY: PorcelainFileStatus[]) {\n   return indexY.map((y) => parser(indexX, y, (result, file) => append(result.conflicted, file)));\n}\n\nconst parsers: Map<string, StatusLineParser> = new Map([\n   parser(PorcelainFileStatus.NONE, PorcelainFileStatus.ADDED, (result, file) =>\n      append(result.created, file)\n   ),\n   parser(PorcelainFileStatus.NONE, PorcelainFileStatus.DELETED, (result, file) =>\n      append(result.deleted, file)\n   ),\n   parser(PorcelainFileStatus.NONE, PorcelainFileStatus.MODIFIED, (result, file) =>\n      append(result.modified, file)\n   ),\n\n   parser(\n      PorcelainFileStatus.ADDED,\n      PorcelainFileStatus.NONE,\n      (result, file) => append(result.created, file) && append(result.staged, file)\n   ),\n   parser(\n      PorcelainFileStatus.ADDED,\n      PorcelainFileStatus.MODIFIED,\n      (result, file) =>\n         append(result.created, file) &&\n         append(result.staged, file) &&\n         append(result.modified, file)\n   ),\n\n   parser(\n      PorcelainFileStatus.DELETED,\n      PorcelainFileStatus.NONE,\n      (result, file) => append(result.deleted, file) && append(result.staged, file)\n   ),\n\n   parser(\n      PorcelainFileStatus.MODIFIED,\n      PorcelainFileStatus.NONE,\n      (result, file) => append(result.modified, file) && append(result.staged, file)\n   ),\n   parser(\n      PorcelainFileStatus.MODIFIED,\n      PorcelainFileStatus.MODIFIED,\n      (result, file) => append(result.modified, file) && append(result.staged, file)\n   ),\n\n   parser(PorcelainFileStatus.RENAMED, PorcelainFileStatus.NONE, (result, file) => {\n      append(result.renamed, renamedFile(file));\n   }),\n   parser(PorcelainFileStatus.RENAMED, PorcelainFileStatus.MODIFIED, (result, file) => {\n      const renamed = renamedFile(file);\n      append(result.renamed, renamed);\n      append(result.modified, renamed.to);\n   }),\n   parser(PorcelainFileStatus.IGNORED, PorcelainFileStatus.IGNORED, (_result, _file) => {\n      append((_result.ignored = _result.ignored || []), _file);\n   }),\n\n   parser(PorcelainFileStatus.UNTRACKED, PorcelainFileStatus.UNTRACKED, (result, file) =>\n      append(result.not_added, file)\n   ),\n\n   ...conflicts(PorcelainFileStatus.ADDED, PorcelainFileStatus.ADDED, PorcelainFileStatus.UNMERGED),\n   ...conflicts(\n      PorcelainFileStatus.DELETED,\n      PorcelainFileStatus.DELETED,\n      PorcelainFileStatus.UNMERGED\n   ),\n   ...conflicts(\n      PorcelainFileStatus.UNMERGED,\n      PorcelainFileStatus.ADDED,\n      PorcelainFileStatus.DELETED,\n      PorcelainFileStatus.UNMERGED\n   ),\n\n   [\n      '##',\n      (result, line) => {\n         const aheadReg = /ahead (\\d+)/;\n         const behindReg = /behind (\\d+)/;\n         const currentReg = /^(.+?(?=(?:\\.{3}|\\s|$)))/;\n         const trackingReg = /\\.{3}(\\S*)/;\n         const onEmptyBranchReg = /\\son\\s([\\S]+)$/;\n         let regexResult;\n\n         regexResult = aheadReg.exec(line);\n         result.ahead = (regexResult && +regexResult[1]) || 0;\n\n         regexResult = behindReg.exec(line);\n         result.behind = (regexResult && +regexResult[1]) || 0;\n\n         regexResult = currentReg.exec(line);\n         result.current = regexResult && regexResult[1];\n\n         regexResult = trackingReg.exec(line);\n         result.tracking = regexResult && regexResult[1];\n\n         regexResult = onEmptyBranchReg.exec(line);\n         result.current = (regexResult && regexResult[1]) || result.current;\n\n         result.detached = /\\(no branch\\)/.test(line);\n      },\n   ],\n]);\n\nexport const parseStatusSummary = function (text: string): StatusResult {\n   const lines = text.split(NULL);\n   const status = new StatusSummary();\n\n   for (let i = 0, l = lines.length; i < l; ) {\n      let line = lines[i++].trim();\n\n      if (!line) {\n         continue;\n      }\n\n      if (line.charAt(0) === PorcelainFileStatus.RENAMED) {\n         line += NULL + (lines[i++] || '');\n      }\n\n      splitLine(status, line);\n   }\n\n   return status;\n};\n\nfunction splitLine(result: StatusResult, lineStr: string) {\n   const trimmed = lineStr.trim();\n   switch (' ') {\n      case trimmed.charAt(2):\n         return data(trimmed.charAt(0), trimmed.charAt(1), trimmed.substr(3));\n      case trimmed.charAt(1):\n         return data(PorcelainFileStatus.NONE, trimmed.charAt(0), trimmed.substr(2));\n      default:\n         return;\n   }\n\n   function data(index: string, workingDir: string, path: string) {\n      const raw = `${index}${workingDir}`;\n      const handler = parsers.get(raw);\n\n      if (handler) {\n         handler(result, path);\n      }\n\n      if (raw !== '##' && raw !== '!!') {\n         result.files.push(new FileStatusSummary(path, index, workingDir));\n      }\n   }\n}\n", "import { StatusResult } from '../../../typings';\nimport { parseStatusSummary } from '../responses/StatusSummary';\nimport { StringTask } from '../types';\n\nconst ignoredOptions = ['--null', '-z'];\n\nexport function statusTask(customArgs: string[]): StringTask<StatusResult> {\n   const commands = [\n      'status',\n      '--porcelain',\n      '-b',\n      '-u',\n      '--null',\n      ...customArgs.filter((arg) => !ignoredOptions.includes(arg)),\n   ];\n\n   return {\n      format: 'utf-8',\n      commands,\n      parser(text: string) {\n         return parseStatusSummary(text);\n      },\n   };\n}\n", "import type { SimpleGitApi } from '../simple-git-api';\nimport type { SimpleGit } from '../../../typings';\nimport { asNumber, ExitCodes, LineParser, parseStringResponse } from '../utils';\n\nexport interface VersionResult {\n   major: number;\n   minor: number;\n   patch: number | string;\n   agent: string;\n   installed: boolean;\n}\n\nconst NOT_INSTALLED = 'installed=false';\n\nfunction versionResponse(\n   major = 0,\n   minor = 0,\n   patch: string | number = 0,\n   agent = '',\n   installed = true\n): VersionResult {\n   return Object.defineProperty(\n      {\n         major,\n         minor,\n         patch,\n         agent,\n         installed,\n      },\n      'toString',\n      {\n         value() {\n            return `${this.major}.${this.minor}.${this.patch}`;\n         },\n         configurable: false,\n         enumerable: false,\n      }\n   );\n}\n\nfunction notInstalledResponse() {\n   return versionResponse(0, 0, 0, '', false);\n}\n\nexport default function (): Pick<SimpleGit, 'version'> {\n   return {\n      version(this: SimpleGitApi) {\n         return this._runTask({\n            commands: ['--version'],\n            format: 'utf-8',\n            parser: versionParser,\n            onError(result, error, done, fail) {\n               if (result.exitCode === ExitCodes.NOT_FOUND) {\n                  return done(Buffer.from(NOT_INSTALLED));\n               }\n\n               fail(error);\n            },\n         });\n      },\n   };\n}\n\nconst parsers: LineParser<VersionResult>[] = [\n   new LineParser(\n      /version (\\d+)\\.(\\d+)\\.(\\d+)(?:\\s*\\((.+)\\))?/,\n      (result, [major, minor, patch, agent = '']) => {\n         Object.assign(\n            result,\n            versionResponse(asNumber(major), asNumber(minor), asNumber(patch), agent)\n         );\n      }\n   ),\n   new LineParser(\n      /version (\\d+)\\.(\\d+)\\.(\\D+)(.+)?$/,\n      (result, [major, minor, patch, agent = '']) => {\n         Object.assign(result, versionResponse(asNumber(major), asNumber(minor), patch, agent));\n      }\n   ),\n];\n\nfunction versionParser(stdOut: string) {\n   if (stdOut === NOT_INSTALLED) {\n      return notInstalledResponse();\n   }\n\n   return parseStringResponse(versionResponse(0, 0, 0, stdOut), parsers, stdOut);\n}\n", "import { SimpleGitBase } from '../../typings';\nimport { taskCallback } from './task-callback';\nimport { changeWorkingDirectoryTask } from './tasks/change-working-directory';\nimport checkout from './tasks/checkout';\nimport countObjects from './tasks/count-objects';\nimport commit from './tasks/commit';\nimport config from './tasks/config';\nimport firstCommit from './tasks/first-commit';\nimport grep from './tasks/grep';\nimport { hashObjectTask } from './tasks/hash-object';\nimport { initTask } from './tasks/init';\nimport log from './tasks/log';\nimport { mergeTask } from './tasks/merge';\nimport { pushTask } from './tasks/push';\nimport show from './tasks/show';\nimport { statusTask } from './tasks/status';\nimport { configurationErrorTask, straightThroughStringTask } from './tasks/task';\nimport version from './tasks/version';\nimport { output<PERSON><PERSON><PERSON>, SimpleGitExecutor, SimpleGitTask, SimpleGitTaskCallback } from './types';\nimport {\n   asArray,\n   filterString,\n   filterType,\n   getTrailingOptions,\n   trailingFunctionArgument,\n} from './utils';\n\nexport class SimpleGitApi implements SimpleGitBase {\n   constructor(private _executor: SimpleGitExecutor) {}\n\n   protected _runTask<T>(task: SimpleGitTask<T>, then?: SimpleGitTaskCallback<T>) {\n      const chain = this._executor.chain();\n      const promise = chain.push(task);\n\n      if (then) {\n         taskCallback(task, promise, then);\n      }\n\n      return Object.create(this, {\n         then: { value: promise.then.bind(promise) },\n         catch: { value: promise.catch.bind(promise) },\n         _executor: { value: chain },\n      });\n   }\n\n   add(files: string | string[]) {\n      return this._runTask(\n         straightThroughStringTask(['add', ...asArray(files)]),\n         trailingFunctionArgument(arguments)\n      );\n   }\n\n   cwd(directory: string | { path: string; root?: boolean }) {\n      const next = trailingFunctionArgument(arguments);\n\n      if (typeof directory === 'string') {\n         return this._runTask(changeWorkingDirectoryTask(directory, this._executor), next);\n      }\n\n      if (typeof directory?.path === 'string') {\n         return this._runTask(\n            changeWorkingDirectoryTask(\n               directory.path,\n               (directory.root && this._executor) || undefined\n            ),\n            next\n         );\n      }\n\n      return this._runTask(\n         configurationErrorTask('Git.cwd: workingDirectory must be supplied as a string'),\n         next\n      );\n   }\n\n   hashObject(path: string, write: boolean | unknown) {\n      return this._runTask(\n         hashObjectTask(path, write === true),\n         trailingFunctionArgument(arguments)\n      );\n   }\n\n   init(bare?: boolean | unknown) {\n      return this._runTask(\n         initTask(bare === true, this._executor.cwd, getTrailingOptions(arguments)),\n         trailingFunctionArgument(arguments)\n      );\n   }\n\n   merge() {\n      return this._runTask(\n         mergeTask(getTrailingOptions(arguments)),\n         trailingFunctionArgument(arguments)\n      );\n   }\n\n   mergeFromTo(remote: string, branch: string) {\n      if (!(filterString(remote) && filterString(branch))) {\n         return this._runTask(\n            configurationErrorTask(\n               `Git.mergeFromTo requires that the 'remote' and 'branch' arguments are supplied as strings`\n            )\n         );\n      }\n\n      return this._runTask(\n         mergeTask([remote, branch, ...getTrailingOptions(arguments)]),\n         trailingFunctionArgument(arguments, false)\n      );\n   }\n\n   outputHandler(handler: outputHandler) {\n      this._executor.outputHandler = handler;\n      return this;\n   }\n\n   push() {\n      const task = pushTask(\n         {\n            remote: filterType(arguments[0], filterString),\n            branch: filterType(arguments[1], filterString),\n         },\n         getTrailingOptions(arguments)\n      );\n\n      return this._runTask(task, trailingFunctionArgument(arguments));\n   }\n\n   stash() {\n      return this._runTask(\n         straightThroughStringTask(['stash', ...getTrailingOptions(arguments)]),\n         trailingFunctionArgument(arguments)\n      );\n   }\n\n   status() {\n      return this._runTask(\n         statusTask(getTrailingOptions(arguments)),\n         trailingFunctionArgument(arguments)\n      );\n   }\n}\n\nObject.assign(\n   SimpleGitApi.prototype,\n   checkout(),\n   commit(),\n   config(),\n   countObjects(),\n   firstCommit(),\n   grep(),\n   log(),\n   show(),\n   version()\n);\n", "import { append, remove } from '../utils';\nimport { createDeferred, DeferredPromise } from '@kwsites/promise-deferred';\nimport { createLogger } from '../git-logger';\n\ntype ScheduleCompleteCallback = () => void;\ntype ScheduledTask = Pick<DeferredPromise<ScheduleCompleteCallback>, 'promise' | 'done'> & {\n   id: number;\n};\n\nconst createScheduledTask: () => ScheduledTask = (() => {\n   let id = 0;\n   return () => {\n      id++;\n      const { promise, done } = createDeferred<ScheduleCompleteCallback>();\n\n      return {\n         promise,\n         done,\n         id,\n      };\n   };\n})();\n\nexport class Scheduler {\n   private logger = createLogger('', 'scheduler');\n   private pending: ScheduledTask[] = [];\n   private running: ScheduledTask[] = [];\n\n   constructor(private concurrency = 2) {\n      this.logger(`Constructed, concurrency=%s`, concurrency);\n   }\n\n   private schedule() {\n      if (!this.pending.length || this.running.length >= this.concurrency) {\n         this.logger(\n            `Schedule attempt ignored, pending=%s running=%s concurrency=%s`,\n            this.pending.length,\n            this.running.length,\n            this.concurrency\n         );\n         return;\n      }\n\n      const task = append(this.running, this.pending.shift()!);\n      this.logger(`Attempting id=%s`, task.id);\n      task.done(() => {\n         this.logger(`Completing id=`, task.id);\n         remove(this.running, task);\n         this.schedule();\n      });\n   }\n\n   next(): Promise<ScheduleCompleteCallback> {\n      const { promise, id } = append(this.pending, createScheduledTask());\n      this.logger(`Scheduling id=%s`, id);\n\n      this.schedule();\n\n      return promise;\n   }\n}\n", "import { straightThroughStringTask } from './task';\nimport { OptionFlags, Options, StringTask } from '../types';\n\nexport type ApplyOptions = Options &\n   OptionFlags<\n      | '--stat'\n      | '--numstat'\n      | '--summary'\n      | '--check'\n      | '--index'\n      | '--intent-to-add'\n      | '--3way'\n      | '--apply'\n      | '--no-add'\n      | '-R'\n      | '--reverse'\n      | '--allow-binary-replacement'\n      | '--binary'\n      | '--reject'\n      | '-z'\n      | '--inaccurate-eof'\n      | '--recount'\n      | '--cached'\n      | '--ignore-space-change'\n      | '--ignore-whitespace'\n      | '--verbose'\n      | '--unsafe-paths'\n   > &\n   OptionFlags<'--whitespace', 'nowarn' | 'warn' | 'fix' | 'error' | 'error-all'> &\n   OptionFlags<'--build-fake-ancestor' | '--exclude' | '--include' | '--directory', string> &\n   OptionFlags<'-p' | '-C', number>;\n\nexport function applyPatchTask(patches: string[], customArgs: string[]): StringTask<string> {\n   return straightThroughStringTask(['apply', ...customArgs, ...patches]);\n}\n", "import {\n   BranchMultiDeleteResult,\n   BranchSingleDeleteFailure,\n   BranchSingleDeleteResult,\n   BranchSingleDeleteSuccess,\n} from '../../../typings';\n\nexport class BranchDeletionBatch implements BranchMultiDeleteResult {\n   all: BranchSingleDeleteResult[] = [];\n   branches: { [branchName: string]: BranchSingleDeleteResult } = {};\n   errors: BranchSingleDeleteResult[] = [];\n\n   get success(): boolean {\n      return !this.errors.length;\n   }\n}\n\nexport function branchDeletionSuccess(branch: string, hash: string): BranchSingleDeleteSuccess {\n   return {\n      branch,\n      hash,\n      success: true,\n   };\n}\n\nexport function branchDeletionFailure(branch: string): BranchSingleDeleteFailure {\n   return {\n      branch,\n      hash: null,\n      success: false,\n   };\n}\n\nexport function isSingleBranchDeleteFailure(\n   test: BranchSingleDeleteResult\n): test is BranchSingleDeleteSuccess {\n   return test.success;\n}\n", "import { BranchMultiDeleteResult } from '../../../typings';\nimport {\n   BranchDeletionBatch,\n   branchDeletionFailure,\n   branchDeletionSuccess,\n} from '../responses/BranchDeleteSummary';\nimport { TaskParser } from '../types';\nimport { ExitCodes, LineParser, parseStringResponse } from '../utils';\n\nconst deleteSuccessRegex = /(\\S+)\\s+\\(\\S+\\s([^)]+)\\)/;\nconst deleteErrorRegex = /^error[^']+'([^']+)'/m;\n\nconst parsers: LineParser<BranchMultiDeleteResult>[] = [\n   new LineParser(deleteSuccessRegex, (result, [branch, hash]) => {\n      const deletion = branchDeletionSuccess(branch, hash);\n\n      result.all.push(deletion);\n      result.branches[branch] = deletion;\n   }),\n   new LineParser(deleteErrorRegex, (result, [branch]) => {\n      const deletion = branchDeletionFailure(branch);\n\n      result.errors.push(deletion);\n      result.all.push(deletion);\n      result.branches[branch] = deletion;\n   }),\n];\n\nexport const parseBranchDeletions: TaskParser<string, BranchMultiDeleteResult> = (\n   stdOut,\n   stdErr\n) => {\n   return parseStringResponse(new BranchDeletionBatch(), parsers, [stdOut, stdErr]);\n};\n\nexport function hasBranchDeletionError(data: string, processExitCode: ExitCodes): boolean {\n   return processExitCode === ExitCodes.ERROR && deleteErrorRegex.test(data);\n}\n", "import type { BranchSummary, BranchSummaryBranch } from '../../../typings';\n\nexport enum BranchStatusIdentifier {\n   CURRENT = '*',\n   LINKED = '+',\n}\n\nexport class BranchSummaryResult implements BranchSummary {\n   public all: string[] = [];\n   public branches: { [p: string]: BranchSummaryBranch } = {};\n   public current: string = '';\n   public detached: boolean = false;\n\n   push(\n      status: BranchStatusIdentifier | unknown,\n      detached: boolean,\n      name: string,\n      commit: string,\n      label: string\n   ) {\n      if (status === BranchStatusIdentifier.CURRENT) {\n         this.detached = detached;\n         this.current = name;\n      }\n\n      this.all.push(name);\n      this.branches[name] = {\n         current: status === BranchStatusIdentifier.CURRENT,\n         linkedWorkTree: status === BranchStatusIdentifier.LINKED,\n         name,\n         commit,\n         label,\n      };\n   }\n}\n", "import type { BranchSummary } from '../../../typings';\nimport { BranchSummaryResult } from '../responses/BranchSummary';\nimport { LineParser, parseStringResponse } from '../utils';\n\nconst parsers: LineParser<BranchSummaryResult>[] = [\n   new LineParser(\n      /^([*+]\\s)?\\((?:HEAD )?detached (?:from|at) (\\S+)\\)\\s+([a-z0-9]+)\\s(.*)$/,\n      (result, [current, name, commit, label]) => {\n         result.push(branchStatus(current), true, name, commit, label);\n      }\n   ),\n   new LineParser(\n      /^([*+]\\s)?(\\S+)\\s+([a-z0-9]+)\\s?(.*)$/s,\n      (result, [current, name, commit, label]) => {\n         result.push(branchStatus(current), false, name, commit, label);\n      }\n   ),\n];\n\nfunction branchStatus(input?: string) {\n   return input ? input.charAt(0) : '';\n}\n\nexport function parseBranchSummary(stdOut: string): BranchSummary {\n   return parseStringResponse(new BranchSummaryResult(), parsers, stdOut);\n}\n", "import { BranchMultiDeleteResult, BranchSingleDeleteResult, BranchSummary } from '../../../typings';\nimport { StringTask } from '../types';\nimport { GitResponseError } from '../errors/git-response-error';\nimport { hasBranchDeletionError, parseBranchDeletions } from '../parsers/parse-branch-delete';\nimport { parseBranchSummary } from '../parsers/parse-branch';\nimport { bufferToString } from '../utils';\n\nexport function containsDeleteBranchCommand(commands: string[]) {\n   const deleteCommands = ['-d', '-D', '--delete'];\n   return commands.some((command) => deleteCommands.includes(command));\n}\n\nexport function branchTask(\n   customArgs: string[]\n): StringTask<BranchSummary | BranchSingleDeleteResult> {\n   const isDelete = containsDeleteBranchCommand(customArgs);\n   const commands = ['branch', ...customArgs];\n\n   if (commands.length === 1) {\n      commands.push('-a');\n   }\n\n   if (!commands.includes('-v')) {\n      commands.splice(1, 0, '-v');\n   }\n\n   return {\n      format: 'utf-8',\n      commands,\n      parser(stdOut, stdErr) {\n         if (isDelete) {\n            return parseBranchDeletions(stdOut, stdErr).all[0];\n         }\n\n         return parseBranchSummary(stdOut);\n      },\n   };\n}\n\nexport function branchLocalTask(): StringTask<BranchSummary> {\n   const parser = parseBranchSummary;\n\n   return {\n      format: 'utf-8',\n      commands: ['branch', '-v'],\n      parser,\n   };\n}\n\nexport function deleteBranchesTask(\n   branches: string[],\n   forceDelete = false\n): StringTask<BranchMultiDeleteResult> {\n   return {\n      format: 'utf-8',\n      commands: ['branch', '-v', forceDelete ? '-D' : '-d', ...branches],\n      parser(stdOut, stdErr) {\n         return parseBranchDeletions(stdOut, stdErr);\n      },\n      onError({ exitCode, stdOut }, error, done, fail) {\n         if (!hasBranchDeletionError(String(error), exitCode)) {\n            return fail(error);\n         }\n\n         done(stdOut);\n      },\n   };\n}\n\nexport function deleteBranchTask(\n   branch: string,\n   forceDelete = false\n): StringTask<BranchSingleDeleteResult> {\n   const task: StringTask<BranchSingleDeleteResult> = {\n      format: 'utf-8',\n      commands: ['branch', '-v', forceDelete ? '-D' : '-d', branch],\n      parser(stdOut, stdErr) {\n         return parseBranchDeletions(stdOut, stdErr).branches[branch]!;\n      },\n      onError({ exitCode, stdErr, stdOut }, error, _, fail) {\n         if (!hasBranchDeletionError(String(error), exitCode)) {\n            return fail(error);\n         }\n\n         throw new GitResponseError(\n            task.parser(bufferToString(stdOut), bufferToString(stdErr)),\n            String(error)\n         );\n      },\n   };\n\n   return task;\n}\n", "/**\n * Parser for the `check-ignore` command - returns each file as a string array\n */\nexport const parseCheckIgnore = (text: string): string[] => {\n   return text\n      .split(/\\n/g)\n      .map((line) => line.trim())\n      .filter((file) => !!file);\n};\n", "import { StringTask } from '../types';\nimport { parseCheckIgnore } from '../responses/CheckIgnore';\n\nexport function checkIgnoreTask(paths: string[]): StringTask<string[]> {\n   return {\n      commands: ['check-ignore', ...paths],\n      format: 'utf-8',\n      parser: parseCheckIgnore,\n   };\n}\n", "import { configurationErrorTask, EmptyTask, straightThroughStringTask } from './task';\nimport { OptionFlags, Options, StringTask } from '../types';\nimport { append, filterString } from '../utils';\n\nexport type CloneOptions = Options &\n   OptionFlags<\n      | '--bare'\n      | '--dissociate'\n      | '--mirror'\n      | '--no-checkout'\n      | '--no-remote-submodules'\n      | '--no-shallow-submodules'\n      | '--no-single-branch'\n      | '--no-tags'\n      | '--remote-submodules'\n      | '--single-branch'\n      | '--shallow-submodules'\n      | '--verbose'\n   > &\n   OptionFlags<'--depth' | '-j' | '--jobs', number> &\n   OptionFlags<\n      | '--branch'\n      | '--origin'\n      | '--recurse-submodules'\n      | '--separate-git-dir'\n      | '--shallow-exclude'\n      | '--shallow-since'\n      | '--template',\n      string\n   >;\n\nfunction disallowedCommand(command: string) {\n   return /^--upload-pack(=|$)/.test(command);\n}\n\nexport function cloneTask(\n   repo: string | undefined,\n   directory: string | undefined,\n   customArgs: string[]\n): StringTask<string> | EmptyTask {\n   const commands = ['clone', ...customArgs];\n\n   filterString(repo) && commands.push(repo);\n   filterString(directory) && commands.push(directory);\n\n   const banned = commands.find(disallowedCommand);\n   if (banned) {\n      return configurationErrorTask(`git.fetch: potential exploit argument blocked.`);\n   }\n\n   return straightThroughStringTask(commands);\n}\n\nexport function cloneMirrorTask(\n   repo: string | undefined,\n   directory: string | undefined,\n   customArgs: string[]\n) {\n   append(customArgs, '--mirror');\n\n   return cloneTask(repo, directory, customArgs);\n}\n", "import { FetchResult } from '../../../typings';\nimport { LineParser, parseStringResponse } from '../utils';\n\nconst parsers: LineParser<FetchResult>[] = [\n   new LineParser(/From (.+)$/, (result, [remote]) => {\n      result.remote = remote;\n   }),\n   new LineParser(/\\* \\[new branch]\\s+(\\S+)\\s*-> (.+)$/, (result, [name, tracking]) => {\n      result.branches.push({\n         name,\n         tracking,\n      });\n   }),\n   new LineParser(/\\* \\[new tag]\\s+(\\S+)\\s*-> (.+)$/, (result, [name, tracking]) => {\n      result.tags.push({\n         name,\n         tracking,\n      });\n   }),\n   new LineParser(/- \\[deleted]\\s+\\S+\\s*-> (.+)$/, (result, [tracking]) => {\n      result.deleted.push({\n         tracking,\n      });\n   }),\n   new LineParser(\n      /\\s*([^.]+)\\.\\.(\\S+)\\s+(\\S+)\\s*-> (.+)$/,\n      (result, [from, to, name, tracking]) => {\n         result.updated.push({\n            name,\n            tracking,\n            to,\n            from,\n         });\n      }\n   ),\n];\n\nexport function parseFetchResult(stdOut: string, stdErr: string): FetchResult {\n   const result: FetchResult = {\n      raw: stdOut,\n      remote: null,\n      branches: [],\n      tags: [],\n      updated: [],\n      deleted: [],\n   };\n   return parseStringResponse(result, parsers, [stdOut, stdErr]);\n}\n", "import { FetchResult } from '../../../typings';\nimport { parseFetchResult } from '../parsers/parse-fetch';\nimport { StringTask } from '../types';\n\nimport { configurationErrorTask, EmptyTask } from './task';\n\nfunction disallowedCommand(command: string) {\n   return /^--upload-pack(=|$)/.test(command);\n}\n\nexport function fetchTask(\n   remote: string,\n   branch: string,\n   customArgs: string[]\n): StringTask<FetchResult> | EmptyTask {\n   const commands = ['fetch', ...customArgs];\n   if (remote && branch) {\n      commands.push(remote, branch);\n   }\n\n   const banned = commands.find(disallowedCommand);\n   if (banned) {\n      return configurationErrorTask(`git.fetch: potential exploit argument blocked.`);\n   }\n\n   return {\n      commands,\n      format: 'utf-8',\n      parser: parseFetchResult,\n   };\n}\n", "import { MoveResult } from '../../../typings';\nimport { LineParser, parseStringResponse } from '../utils';\n\nconst parsers: LineParser<MoveResult>[] = [\n   new LineParser(/^Renaming (.+) to (.+)$/, (result, [from, to]) => {\n      result.moves.push({ from, to });\n   }),\n];\n\nexport function parseMoveResult(stdOut: string): MoveResult {\n   return parseStringResponse({ moves: [] }, parsers, stdOut);\n}\n", "import { MoveResult } from '../../../typings';\nimport { parseMoveResult } from '../parsers/parse-move';\nimport { StringTask } from '../types';\nimport { asArray } from '../utils';\n\nexport function moveTask(from: string | string[], to: string): StringTask<MoveResult> {\n   return {\n      commands: ['mv', '-v', ...asArray(from), to],\n      format: 'utf-8',\n      parser: parseMoveResult,\n   };\n}\n", "import { PullResult } from '../../../typings';\nimport { GitResponseError } from '../errors/git-response-error';\nimport { parsePullErrorResult, parsePullResult } from '../parsers/parse-pull';\nimport { Maybe, StringTask } from '../types';\nimport { bufferToString } from '../utils';\n\nexport function pullTask(\n   remote: Maybe<string>,\n   branch: Maybe<string>,\n   customArgs: string[]\n): StringTask<PullResult> {\n   const commands: string[] = ['pull', ...customArgs];\n   if (remote && branch) {\n      commands.splice(1, 0, remote, branch);\n   }\n\n   return {\n      commands,\n      format: 'utf-8',\n      parser(stdOut, stdErr): PullResult {\n         return parsePullResult(stdOut, stdErr);\n      },\n      onError(result, _error, _done, fail) {\n         const pullError = parsePullErrorResult(\n            bufferToString(result.stdOut),\n            bufferToString(result.stdErr)\n         );\n         if (pullError) {\n            return fail(new GitResponseError(pullError));\n         }\n\n         fail(_error);\n      },\n   };\n}\n", "import { forEachLineWithContent } from '../utils';\n\nexport interface RemoteWithoutRefs {\n   name: string;\n}\n\nexport interface RemoteWithRefs extends RemoteWithoutRefs {\n   refs: {\n      fetch: string;\n      push: string;\n   };\n}\n\nexport function parseGetRemotes(text: string): RemoteWithoutRefs[] {\n   const remotes: { [name: string]: RemoteWithoutRefs } = {};\n\n   forEach(text, ([name]) => (remotes[name] = { name }));\n\n   return Object.values(remotes);\n}\n\nexport function parseGetRemotesVerbose(text: string): RemoteWithRefs[] {\n   const remotes: { [name: string]: RemoteWithRefs } = {};\n\n   forEach(text, ([name, url, purpose]) => {\n      if (!remotes.hasOwnProperty(name)) {\n         remotes[name] = {\n            name: name,\n            refs: { fetch: '', push: '' },\n         };\n      }\n\n      if (purpose && url) {\n         remotes[name].refs[purpose.replace(/[^a-z]/g, '') as keyof RemoteWithRefs['refs']] = url;\n      }\n   });\n\n   return Object.values(remotes);\n}\n\nfunction forEach(text: string, handler: (line: string[]) => void) {\n   forEachLineWithContent(text, (line) => handler(line.split(/\\s+/)));\n}\n", "import { parseGetRemotes, parseGetRemotesVerbose } from '../responses/GetRemoteSummary';\nimport { StringTask } from '../types';\nimport { straightThroughStringTask } from './task';\n\nexport function addRemoteTask(\n   remoteName: string,\n   remoteRepo: string,\n   customArgs: string[]\n): StringTask<string> {\n   return straightThroughStringTask(['remote', 'add', ...customArgs, remoteName, remoteRepo]);\n}\n\nexport function getRemotesTask(verbose: boolean): StringTask<any> {\n   const commands = ['remote'];\n   if (verbose) {\n      commands.push('-v');\n   }\n\n   return {\n      commands,\n      format: 'utf-8',\n      parser: verbose ? parseGetRemotesVerbose : parseGetRemotes,\n   };\n}\n\nexport function listRemotesTask(customArgs: string[]): StringTask<string> {\n   const commands = [...customArgs];\n   if (commands[0] !== 'ls-remote') {\n      commands.unshift('ls-remote');\n   }\n\n   return straightThroughStringTask(commands);\n}\n\nexport function remoteTask(customArgs: string[]): StringTask<string> {\n   const commands = [...customArgs];\n   if (commands[0] !== 'remote') {\n      commands.unshift('remote');\n   }\n\n   return straightThroughStringTask(commands);\n}\n\nexport function removeRemoteTask(remoteName: string) {\n   return straightThroughStringTask(['remote', 'remove', remoteName]);\n}\n", "import { LogOptions, LogResult } from '../../../typings';\nimport { logFormatFromCommand } from '../args/log-format';\nimport { createListLogSummaryParser } from '../parsers/parse-list-log-summary';\nimport type { StringTask } from '../types';\nimport { validateLogFormatConfig } from './diff';\nimport { parseLogOptions } from './log';\nimport type { EmptyTask } from './task';\n\nexport function stashListTask(\n   opt: LogOptions = {},\n   customArgs: string[]\n): EmptyTask | StringTask<LogResult> {\n   const options = parseLogOptions<any>(opt);\n   const commands = ['stash', 'list', ...options.commands, ...customArgs];\n   const parser = createListLogSummaryParser(\n      options.splitter,\n      options.fields,\n      logFormatFromCommand(commands)\n   );\n\n   return (\n      validateLogFormatConfig(commands) || {\n         commands,\n         format: 'utf-8',\n         parser,\n      }\n   );\n}\n", "import { StringTask } from '../types';\nimport { straightThroughStringTask } from './task';\n\nexport function addSubModuleTask(repo: string, path: string): StringTask<string> {\n   return subModuleTask(['add', repo, path]);\n}\n\nexport function initSubModuleTask(customArgs: string[]): StringTask<string> {\n   return subModuleTask(['init', ...customArgs]);\n}\n\nexport function subModuleTask(customArgs: string[]): StringTask<string> {\n   const commands = [...customArgs];\n   if (commands[0] !== 'submodule') {\n      commands.unshift('submodule');\n   }\n\n   return straightThroughStringTask(commands);\n}\n\nexport function updateSubModuleTask(customArgs: string[]): StringTask<string> {\n   return subModuleTask(['update', ...customArgs]);\n}\n", "import { TagResult } from '../../../typings';\n\nexport class TagList implements TagResult {\n   constructor(\n      public readonly all: string[],\n      public readonly latest: string | undefined\n   ) {}\n}\n\nexport const parseTagList = function (data: string, customSort = false) {\n   const tags = data.split('\\n').map(trimmed).filter(Boolean);\n\n   if (!customSort) {\n      tags.sort(function (tagA, tagB) {\n         const partsA = tagA.split('.');\n         const partsB = tagB.split('.');\n\n         if (partsA.length === 1 || partsB.length === 1) {\n            return singleSorted(toNumber(partsA[0]), toNumber(partsB[0]));\n         }\n\n         for (let i = 0, l = Math.max(partsA.length, partsB.length); i < l; i++) {\n            const diff = sorted(toNumber(partsA[i]), toNumber(partsB[i]));\n\n            if (diff) {\n               return diff;\n            }\n         }\n\n         return 0;\n      });\n   }\n\n   const latest = customSort ? tags[0] : [...tags].reverse().find((tag) => tag.indexOf('.') >= 0);\n\n   return new TagList(tags, latest);\n};\n\nfunction singleSorted(a: number, b: number): number {\n   const aIsNum = isNaN(a);\n   const bIsNum = isNaN(b);\n\n   if (aIsNum !== bIsNum) {\n      return aIsNum ? 1 : -1;\n   }\n\n   return aIsNum ? sorted(a, b) : 0;\n}\n\nfunction sorted(a: number, b: number) {\n   return a === b ? 0 : a > b ? 1 : -1;\n}\n\nfunction trimmed(input: string) {\n   return input.trim();\n}\n\nfunction toNumber(input: string | undefined) {\n   if (typeof input === 'string') {\n      return parseInt(input.replace(/^\\D+/g, ''), 10) || 0;\n   }\n\n   return 0;\n}\n", "import { TagResult } from '../../../typings';\nimport { parseTagList } from '../responses/TagList';\nimport { StringTask } from '../types';\n\n/**\n * Task used by `git.tags`\n */\nexport function tagListTask(customArgs: string[] = []): StringTask<TagResult> {\n   const hasCustomSort = customArgs.some((option) => /^--sort=/.test(option));\n\n   return {\n      format: 'utf-8',\n      commands: ['tag', '-l', ...customArgs],\n      parser(text: string) {\n         return parseTagList(text, hasCustomSort);\n      },\n   };\n}\n\n/**\n * Task used by `git.addTag`\n */\nexport function addTagTask(name: string): StringTask<{ name: string }> {\n   return {\n      format: 'utf-8',\n      commands: ['tag', name],\n      parser() {\n         return { name };\n      },\n   };\n}\n\n/**\n * Task used by `git.addTag`\n */\nexport function addAnnotatedTagTask(\n   name: string,\n   tagMessage: string\n): StringTask<{ name: string }> {\n   return {\n      format: 'utf-8',\n      commands: ['tag', '-a', '-m', tagMessage, name],\n      parser() {\n         return { name };\n      },\n   };\n}\n", "const { GitExecutor } = require('./lib/runners/git-executor');\nconst { SimpleGitApi } = require('./lib/simple-git-api');\n\nconst { Scheduler } = require('./lib/runners/scheduler');\nconst { configurationErrorTask } = require('./lib/tasks/task');\nconst {\n   asArray,\n   filterArray,\n   filterPrimitives,\n   filterString,\n   filterStringOrStringArray,\n   filterType,\n   getTrailingOptions,\n   trailingFunctionArgument,\n   trailingOptionsArgument,\n} = require('./lib/utils');\nconst { applyPatchTask } = require('./lib/tasks/apply-patch');\nconst {\n   branchTask,\n   branchLocalTask,\n   deleteBranchesTask,\n   deleteBranchTask,\n} = require('./lib/tasks/branch');\nconst { checkIgnoreTask } = require('./lib/tasks/check-ignore');\nconst { checkIsRepoTask } = require('./lib/tasks/check-is-repo');\nconst { cloneTask, cloneMirrorTask } = require('./lib/tasks/clone');\nconst { cleanWithOptionsTask, isCleanOptionsArray } = require('./lib/tasks/clean');\nconst { diffSummaryTask } = require('./lib/tasks/diff');\nconst { fetchTask } = require('./lib/tasks/fetch');\nconst { moveTask } = require('./lib/tasks/move');\nconst { pullTask } = require('./lib/tasks/pull');\nconst { pushTagsTask } = require('./lib/tasks/push');\nconst {\n   addRemoteTask,\n   getRemotesTask,\n   listRemotesTask,\n   remoteTask,\n   removeRemoteTask,\n} = require('./lib/tasks/remote');\nconst { getResetMode, resetTask } = require('./lib/tasks/reset');\nconst { stashListTask } = require('./lib/tasks/stash-list');\nconst {\n   addSubModuleTask,\n   initSubModuleTask,\n   subModuleTask,\n   updateSubModuleTask,\n} = require('./lib/tasks/sub-module');\nconst { addAnnotatedTagTask, addTagTask, tagListTask } = require('./lib/tasks/tag');\nconst { straightThroughBufferTask, straightThroughStringTask } = require('./lib/tasks/task');\n\nfunction Git(options, plugins) {\n   this._plugins = plugins;\n   this._executor = new GitExecutor(\n      options.baseDir,\n      new Scheduler(options.maxConcurrentProcesses),\n      plugins\n   );\n\n   this._trimmed = options.trimmed;\n}\n\n(Git.prototype = Object.create(SimpleGitApi.prototype)).constructor = Git;\n\n/**\n * Sets the path to a custom git binary, should either be `git` when there is an installation of git available on\n * the system path, or a fully qualified path to the executable.\n */\nGit.prototype.customBinary = function (command) {\n   this._plugins.reconfigure('binary', command);\n   return this;\n};\n\n/**\n * Sets an environment variable for the spawned child process, either supply both a name and value as strings or\n * a single object to entirely replace the current environment variables.\n *\n * @param {string|Object} name\n * @param {string} [value]\n * @returns {Git}\n */\nGit.prototype.env = function (name, value) {\n   if (arguments.length === 1 && typeof name === 'object') {\n      this._executor.env = name;\n   } else {\n      (this._executor.env = this._executor.env || {})[name] = value;\n   }\n\n   return this;\n};\n\n/**\n * List the stash(s) of the local repo\n */\nGit.prototype.stashList = function (options) {\n   return this._runTask(\n      stashListTask(\n         trailingOptionsArgument(arguments) || {},\n         (filterArray(options) && options) || []\n      ),\n      trailingFunctionArgument(arguments)\n   );\n};\n\nfunction createCloneTask(api, task, repoPath, localPath) {\n   if (typeof repoPath !== 'string') {\n      return configurationErrorTask(`git.${api}() requires a string 'repoPath'`);\n   }\n\n   return task(repoPath, filterType(localPath, filterString), getTrailingOptions(arguments));\n}\n\n/**\n * Clone a git repo\n */\nGit.prototype.clone = function () {\n   return this._runTask(\n      createCloneTask('clone', cloneTask, ...arguments),\n      trailingFunctionArgument(arguments)\n   );\n};\n\n/**\n * Mirror a git repo\n */\nGit.prototype.mirror = function () {\n   return this._runTask(\n      createCloneTask('mirror', cloneMirrorTask, ...arguments),\n      trailingFunctionArgument(arguments)\n   );\n};\n\n/**\n * Moves one or more files to a new destination.\n *\n * @see https://git-scm.com/docs/git-mv\n *\n * @param {string|string[]} from\n * @param {string} to\n */\nGit.prototype.mv = function (from, to) {\n   return this._runTask(moveTask(from, to), trailingFunctionArgument(arguments));\n};\n\n/**\n * Internally uses pull and tags to get the list of tags then checks out the latest tag.\n *\n * @param {Function} [then]\n */\nGit.prototype.checkoutLatestTag = function (then) {\n   var git = this;\n   return this.pull(function () {\n      git.tags(function (err, tags) {\n         git.checkout(tags.latest, then);\n      });\n   });\n};\n\n/**\n * Pull the updated contents of the current repo\n */\nGit.prototype.pull = function (remote, branch, options, then) {\n   return this._runTask(\n      pullTask(\n         filterType(remote, filterString),\n         filterType(branch, filterString),\n         getTrailingOptions(arguments)\n      ),\n      trailingFunctionArgument(arguments)\n   );\n};\n\n/**\n * Fetch the updated contents of the current repo.\n *\n * @example\n *   .fetch('upstream', 'master') // fetches from master on remote named upstream\n *   .fetch(function () {}) // runs fetch against default remote and branch and calls function\n *\n * @param {string} [remote]\n * @param {string} [branch]\n */\nGit.prototype.fetch = function (remote, branch) {\n   return this._runTask(\n      fetchTask(\n         filterType(remote, filterString),\n         filterType(branch, filterString),\n         getTrailingOptions(arguments)\n      ),\n      trailingFunctionArgument(arguments)\n   );\n};\n\n/**\n * Disables/enables the use of the console for printing warnings and errors, by default messages are not shown in\n * a production environment.\n *\n * @param {boolean} silence\n * @returns {Git}\n */\nGit.prototype.silent = function (silence) {\n   console.warn(\n      'simple-git deprecation notice: git.silent: logging should be configured using the `debug` library / `DEBUG` environment variable, this will be an error in version 3'\n   );\n   return this;\n};\n\n/**\n * List all tags. When using git 2.7.0 or above, include an options object with `\"--sort\": \"property-name\"` to\n * sort the tags by that property instead of using the default semantic versioning sort.\n *\n * Note, supplying this option when it is not supported by your Git version will cause the operation to fail.\n *\n * @param {Object} [options]\n * @param {Function} [then]\n */\nGit.prototype.tags = function (options, then) {\n   return this._runTask(\n      tagListTask(getTrailingOptions(arguments)),\n      trailingFunctionArgument(arguments)\n   );\n};\n\n/**\n * Rebases the current working copy. Options can be supplied either as an array of string parameters\n * to be sent to the `git rebase` command, or a standard options object.\n */\nGit.prototype.rebase = function () {\n   return this._runTask(\n      straightThroughStringTask(['rebase', ...getTrailingOptions(arguments)]),\n      trailingFunctionArgument(arguments)\n   );\n};\n\n/**\n * Reset a repo\n */\nGit.prototype.reset = function (mode) {\n   return this._runTask(\n      resetTask(getResetMode(mode), getTrailingOptions(arguments)),\n      trailingFunctionArgument(arguments)\n   );\n};\n\n/**\n * Revert one or more commits in the local working copy\n */\nGit.prototype.revert = function (commit) {\n   const next = trailingFunctionArgument(arguments);\n\n   if (typeof commit !== 'string') {\n      return this._runTask(configurationErrorTask('Commit must be a string'), next);\n   }\n\n   return this._runTask(\n      straightThroughStringTask(['revert', ...getTrailingOptions(arguments, 0, true), commit]),\n      next\n   );\n};\n\n/**\n * Add a lightweight tag to the head of the current branch\n */\nGit.prototype.addTag = function (name) {\n   const task =\n      typeof name === 'string'\n         ? addTagTask(name)\n         : configurationErrorTask('Git.addTag requires a tag name');\n\n   return this._runTask(task, trailingFunctionArgument(arguments));\n};\n\n/**\n * Add an annotated tag to the head of the current branch\n */\nGit.prototype.addAnnotatedTag = function (tagName, tagMessage) {\n   return this._runTask(\n      addAnnotatedTagTask(tagName, tagMessage),\n      trailingFunctionArgument(arguments)\n   );\n};\n\n/**\n * Delete a local branch\n */\nGit.prototype.deleteLocalBranch = function (branchName, forceDelete, then) {\n   return this._runTask(\n      deleteBranchTask(branchName, typeof forceDelete === 'boolean' ? forceDelete : false),\n      trailingFunctionArgument(arguments)\n   );\n};\n\n/**\n * Delete one or more local branches\n */\nGit.prototype.deleteLocalBranches = function (branchNames, forceDelete, then) {\n   return this._runTask(\n      deleteBranchesTask(branchNames, typeof forceDelete === 'boolean' ? forceDelete : false),\n      trailingFunctionArgument(arguments)\n   );\n};\n\n/**\n * List all branches\n *\n * @param {Object | string[]} [options]\n * @param {Function} [then]\n */\nGit.prototype.branch = function (options, then) {\n   return this._runTask(\n      branchTask(getTrailingOptions(arguments)),\n      trailingFunctionArgument(arguments)\n   );\n};\n\n/**\n * Return list of local branches\n *\n * @param {Function} [then]\n */\nGit.prototype.branchLocal = function (then) {\n   return this._runTask(branchLocalTask(), trailingFunctionArgument(arguments));\n};\n\n/**\n * Executes any command against the git binary.\n */\nGit.prototype.raw = function (commands) {\n   const createRestCommands = !Array.isArray(commands);\n   const command = [].slice.call(createRestCommands ? arguments : commands, 0);\n\n   for (let i = 0; i < command.length && createRestCommands; i++) {\n      if (!filterPrimitives(command[i])) {\n         command.splice(i, command.length - i);\n         break;\n      }\n   }\n\n   command.push(...getTrailingOptions(arguments, 0, true));\n\n   var next = trailingFunctionArgument(arguments);\n\n   if (!command.length) {\n      return this._runTask(\n         configurationErrorTask('Raw: must supply one or more command to execute'),\n         next\n      );\n   }\n\n   return this._runTask(straightThroughStringTask(command, this._trimmed), next);\n};\n\nGit.prototype.submoduleAdd = function (repo, path, then) {\n   return this._runTask(addSubModuleTask(repo, path), trailingFunctionArgument(arguments));\n};\n\nGit.prototype.submoduleUpdate = function (args, then) {\n   return this._runTask(\n      updateSubModuleTask(getTrailingOptions(arguments, true)),\n      trailingFunctionArgument(arguments)\n   );\n};\n\nGit.prototype.submoduleInit = function (args, then) {\n   return this._runTask(\n      initSubModuleTask(getTrailingOptions(arguments, true)),\n      trailingFunctionArgument(arguments)\n   );\n};\n\nGit.prototype.subModule = function (options, then) {\n   return this._runTask(\n      subModuleTask(getTrailingOptions(arguments)),\n      trailingFunctionArgument(arguments)\n   );\n};\n\nGit.prototype.listRemote = function () {\n   return this._runTask(\n      listRemotesTask(getTrailingOptions(arguments)),\n      trailingFunctionArgument(arguments)\n   );\n};\n\n/**\n * Adds a remote to the list of remotes.\n */\nGit.prototype.addRemote = function (remoteName, remoteRepo, then) {\n   return this._runTask(\n      addRemoteTask(remoteName, remoteRepo, getTrailingOptions(arguments)),\n      trailingFunctionArgument(arguments)\n   );\n};\n\n/**\n * Removes an entry by name from the list of remotes.\n */\nGit.prototype.removeRemote = function (remoteName, then) {\n   return this._runTask(removeRemoteTask(remoteName), trailingFunctionArgument(arguments));\n};\n\n/**\n * Gets the currently available remotes, setting the optional verbose argument to true includes additional\n * detail on the remotes themselves.\n */\nGit.prototype.getRemotes = function (verbose, then) {\n   return this._runTask(getRemotesTask(verbose === true), trailingFunctionArgument(arguments));\n};\n\n/**\n * Call any `git remote` function with arguments passed as an array of strings.\n *\n * @param {string[]} options\n * @param {Function} [then]\n */\nGit.prototype.remote = function (options, then) {\n   return this._runTask(\n      remoteTask(getTrailingOptions(arguments)),\n      trailingFunctionArgument(arguments)\n   );\n};\n\n/**\n * Call any `git tag` function with arguments passed as an array of strings.\n *\n * @param {string[]} options\n * @param {Function} [then]\n */\nGit.prototype.tag = function (options, then) {\n   const command = getTrailingOptions(arguments);\n\n   if (command[0] !== 'tag') {\n      command.unshift('tag');\n   }\n\n   return this._runTask(straightThroughStringTask(command), trailingFunctionArgument(arguments));\n};\n\n/**\n * Updates repository server info\n *\n * @param {Function} [then]\n */\nGit.prototype.updateServerInfo = function (then) {\n   return this._runTask(\n      straightThroughStringTask(['update-server-info']),\n      trailingFunctionArgument(arguments)\n   );\n};\n\n/**\n * Pushes the current tag changes to a remote which can be either a URL or named remote. When not specified uses the\n * default configured remote spec.\n *\n * @param {string} [remote]\n * @param {Function} [then]\n */\nGit.prototype.pushTags = function (remote, then) {\n   const task = pushTagsTask(\n      { remote: filterType(remote, filterString) },\n      getTrailingOptions(arguments)\n   );\n\n   return this._runTask(task, trailingFunctionArgument(arguments));\n};\n\n/**\n * Removes the named files from source control.\n */\nGit.prototype.rm = function (files) {\n   return this._runTask(\n      straightThroughStringTask(['rm', '-f', ...asArray(files)]),\n      trailingFunctionArgument(arguments)\n   );\n};\n\n/**\n * Removes the named files from source control but keeps them on disk rather than deleting them entirely. To\n * completely remove the files, use `rm`.\n *\n * @param {string|string[]} files\n */\nGit.prototype.rmKeepLocal = function (files) {\n   return this._runTask(\n      straightThroughStringTask(['rm', '--cached', ...asArray(files)]),\n      trailingFunctionArgument(arguments)\n   );\n};\n\n/**\n * Returns a list of objects in a tree based on commit hash. Passing in an object hash returns the object's content,\n * size, and type.\n *\n * Passing \"-p\" will instruct cat-file to determine the object type, and display its formatted contents.\n *\n * @param {string[]} [options]\n * @param {Function} [then]\n */\nGit.prototype.catFile = function (options, then) {\n   return this._catFile('utf-8', arguments);\n};\n\nGit.prototype.binaryCatFile = function () {\n   return this._catFile('buffer', arguments);\n};\n\nGit.prototype._catFile = function (format, args) {\n   var handler = trailingFunctionArgument(args);\n   var command = ['cat-file'];\n   var options = args[0];\n\n   if (typeof options === 'string') {\n      return this._runTask(\n         configurationErrorTask('Git.catFile: options must be supplied as an array of strings'),\n         handler\n      );\n   }\n\n   if (Array.isArray(options)) {\n      command.push.apply(command, options);\n   }\n\n   const task =\n      format === 'buffer' ? straightThroughBufferTask(command) : straightThroughStringTask(command);\n\n   return this._runTask(task, handler);\n};\n\nGit.prototype.diff = function (options, then) {\n   const task = filterString(options)\n      ? configurationErrorTask(\n           'git.diff: supplying options as a single string is no longer supported, switch to an array of strings'\n        )\n      : straightThroughStringTask(['diff', ...getTrailingOptions(arguments)]);\n\n   return this._runTask(task, trailingFunctionArgument(arguments));\n};\n\nGit.prototype.diffSummary = function () {\n   return this._runTask(\n      diffSummaryTask(getTrailingOptions(arguments, 1)),\n      trailingFunctionArgument(arguments)\n   );\n};\n\nGit.prototype.applyPatch = function (patches) {\n   const task = !filterStringOrStringArray(patches)\n      ? configurationErrorTask(\n           `git.applyPatch requires one or more string patches as the first argument`\n        )\n      : applyPatchTask(asArray(patches), getTrailingOptions([].slice.call(arguments, 1)));\n\n   return this._runTask(task, trailingFunctionArgument(arguments));\n};\n\nGit.prototype.revparse = function () {\n   const commands = ['rev-parse', ...getTrailingOptions(arguments, true)];\n   return this._runTask(\n      straightThroughStringTask(commands, true),\n      trailingFunctionArgument(arguments)\n   );\n};\n\n/**\n */\nGit.prototype.clean = function (mode, options, then) {\n   const usingCleanOptionsArray = isCleanOptionsArray(mode);\n   const cleanMode =\n      (usingCleanOptionsArray && mode.join('')) || filterType(mode, filterString) || '';\n   const customArgs = getTrailingOptions([].slice.call(arguments, usingCleanOptionsArray ? 1 : 0));\n\n   return this._runTask(\n      cleanWithOptionsTask(cleanMode, customArgs),\n      trailingFunctionArgument(arguments)\n   );\n};\n\nGit.prototype.exec = function (then) {\n   const task = {\n      commands: [],\n      format: 'utf-8',\n      parser() {\n         if (typeof then === 'function') {\n            then();\n         }\n      },\n   };\n\n   return this._runTask(task);\n};\n\n/**\n * Clears the queue of pending commands and returns the wrapper instance for chaining.\n *\n * @returns {Git}\n */\nGit.prototype.clearQueue = function () {\n   // TODO:\n   // this._executor.clear();\n   return this;\n};\n\n/**\n * Check if a pathname or pathnames are excluded by .gitignore\n *\n * @param {string|string[]} pathnames\n * @param {Function} [then]\n */\nGit.prototype.checkIgnore = function (pathnames, then) {\n   return this._runTask(\n      checkIgnoreTask(asArray(filterType(pathnames, filterStringOrStringArray, []))),\n      trailingFunctionArgument(arguments)\n   );\n};\n\nGit.prototype.checkIsRepo = function (checkType, then) {\n   return this._runTask(\n      checkIsRepoTask(filterType(checkType, filterString)),\n      trailingFunctionArgument(arguments)\n   );\n};\n\nmodule.exports = Git;\n", "import { pathspec } from './args/pathspec';\nimport { GitConstructError } from './errors/git-construct-error';\nimport { GitError } from './errors/git-error';\nimport { GitPluginError } from './errors/git-plugin-error';\nimport { GitResponseError } from './errors/git-response-error';\nimport { TaskConfigurationError } from './errors/task-configuration-error';\nimport { CheckRepoActions } from './tasks/check-is-repo';\nimport { CleanOptions } from './tasks/clean';\nimport { GitConfigScope } from './tasks/config';\nimport { DiffNameStatus } from './tasks/diff-name-status';\nimport { grepQueryBuilder } from './tasks/grep';\nimport { ResetMode } from './tasks/reset';\n\nexport {\n   CheckRepoActions,\n   CleanOptions,\n   DiffNameStatus,\n   GitConfigScope,\n   GitConstructError,\n   GitError,\n   GitPluginError,\n   GitResponseError,\n   ResetMode,\n   TaskConfigurationError,\n   grepQueryBuilder,\n   pathspec,\n};\n", "import { GitError } from './git-error';\nimport { SimpleGitOptions } from '../types';\n\n/**\n * The `GitConstructError` is thrown when an error occurs in the constructor\n * of the `simple-git` instance itself. Most commonly as a result of using\n * a `baseDir` option that points to a folder that either does not exist,\n * or cannot be read by the user the node script is running as.\n *\n * Check the `.message` property for more detail including the properties\n * passed to the constructor.\n */\nexport class GitConstructError extends GitError {\n   constructor(\n      public readonly config: SimpleGitOptions,\n      message: string\n   ) {\n      super(undefined, message);\n   }\n}\n", "import { SimpleGitOptions, SimpleGitTask } from '../types';\nimport { GitError } from './git-error';\n\nexport class GitPluginError extends GitError {\n   constructor(\n      public task?: SimpleGitTask<any>,\n      public readonly plugin?: keyof SimpleGitOptions,\n      message?: string\n   ) {\n      super(task, message);\n      Object.setPrototypeOf(this, new.target.prototype);\n   }\n}\n", "import { SimpleGitOptions } from '../types';\nimport { SimpleGitPlugin } from './simple-git-plugin';\nimport { GitPluginError } from '../errors/git-plugin-error';\n\nexport function abortPlugin(signal: SimpleGitOptions['abort']) {\n   if (!signal) {\n      return;\n   }\n\n   const onSpawnAfter: SimpleGitPlugin<'spawn.after'> = {\n      type: 'spawn.after',\n      action(_data, context) {\n         function kill() {\n            context.kill(new GitPluginError(undefined, 'abort', 'Abort signal received'));\n         }\n\n         signal.addEventListener('abort', kill);\n\n         context.spawned.on('close', () => signal.removeEventListener('abort', kill));\n      },\n   };\n\n   const onSpawnBefore: SimpleGitPlugin<'spawn.before'> = {\n      type: 'spawn.before',\n      action(_data, context) {\n         if (signal.aborted) {\n            context.kill(new GitPluginError(undefined, 'abort', 'Abort already signaled'));\n         }\n      },\n   };\n\n   return [onSpawnBefore, onSpawnAfter];\n}\n", "import type { SimpleGitPlugin } from './simple-git-plugin';\n\nimport { GitPluginError } from '../errors/git-plugin-error';\nimport type { SimpleGitPluginConfig } from '../types';\n\nfunction isConfigSwitch(arg: string | unknown) {\n   return typeof arg === 'string' && arg.trim().toLowerCase() === '-c';\n}\n\nfunction preventProtocolOverride(arg: string, next: string) {\n   if (!isConfigSwitch(arg)) {\n      return;\n   }\n\n   if (!/^\\s*protocol(.[a-z]+)?.allow/.test(next)) {\n      return;\n   }\n\n   throw new GitPluginError(\n      undefined,\n      'unsafe',\n      'Configuring protocol.allow is not permitted without enabling allowUnsafeExtProtocol'\n   );\n}\n\nfunction preventUploadPack(arg: string, method: string) {\n   if (/^\\s*--(upload|receive)-pack/.test(arg)) {\n      throw new GitPluginError(\n         undefined,\n         'unsafe',\n         `Use of --upload-pack or --receive-pack is not permitted without enabling allowUnsafePack`\n      );\n   }\n\n   if (method === 'clone' && /^\\s*-u\\b/.test(arg)) {\n      throw new GitPluginError(\n         undefined,\n         'unsafe',\n         `Use of clone with option -u is not permitted without enabling allowUnsafePack`\n      );\n   }\n\n   if (method === 'push' && /^\\s*--exec\\b/.test(arg)) {\n      throw new GitPluginError(\n         undefined,\n         'unsafe',\n         `Use of push with option --exec is not permitted without enabling allowUnsafePack`\n      );\n   }\n}\n\nexport function blockUnsafeOperationsPlugin({\n   allowUnsafeProtocolOverride = false,\n   allowUnsafePack = false,\n}: SimpleGitPluginConfig['unsafe'] = {}): SimpleGitPlugin<'spawn.args'> {\n   return {\n      type: 'spawn.args',\n      action(args, context) {\n         args.forEach((current, index) => {\n            const next = index < args.length ? args[index + 1] : '';\n\n            allowUnsafeProtocolOverride || preventProtocolOverride(current, next);\n            allowUnsafePack || preventUploadPack(current, context.method);\n         });\n\n         return args;\n      },\n   };\n}\n", "import { prefixedArray } from '../utils';\nimport { SimpleGitPlugin } from './simple-git-plugin';\n\nexport function commandConfigPrefixingPlugin(\n   configuration: string[]\n): SimpleGitPlugin<'spawn.args'> {\n   const prefix = prefixedArray(configuration, '-c');\n\n   return {\n      type: 'spawn.args',\n      action(data) {\n         return [...prefix, ...data];\n      },\n   };\n}\n", "import { deferred, DeferredPromise } from '@kwsites/promise-deferred';\nimport { SimpleGitPluginConfig } from '../types';\nimport { delay } from '../utils';\nimport { SimpleGitPlugin } from './simple-git-plugin';\n\nconst never = deferred().promise;\n\nexport function completionDetectionPlugin({\n   onClose = true,\n   onExit = 50,\n}: SimpleGitPluginConfig['completion'] = {}): SimpleGitPlugin<'spawn.after'> {\n   function createEvents() {\n      let exitCode = -1;\n      const events = {\n         close: deferred(),\n         closeTimeout: deferred(),\n         exit: deferred(),\n         exitTimeout: deferred(),\n      };\n\n      const result = Promise.race([\n         onClose === false ? never : events.closeTimeout.promise,\n         onExit === false ? never : events.exitTimeout.promise,\n      ]);\n\n      configureTimeout(onClose, events.close, events.closeTimeout);\n      configureTimeout(onExit, events.exit, events.exitTimeout);\n\n      return {\n         close(code: number) {\n            exitCode = code;\n            events.close.done();\n         },\n         exit(code: number) {\n            exitCode = code;\n            events.exit.done();\n         },\n         get exitCode() {\n            return exitCode;\n         },\n         result,\n      };\n   }\n\n   function configureTimeout(\n      flag: boolean | number,\n      event: DeferredPromise<void>,\n      timeout: DeferredPromise<void>\n   ) {\n      if (flag === false) {\n         return;\n      }\n\n      (flag === true ? event.promise : event.promise.then(() => delay(flag))).then(timeout.done);\n   }\n\n   return {\n      type: 'spawn.after',\n      async action(_data, { spawned, close }) {\n         const events = createEvents();\n\n         let deferClose = true;\n         let quickClose = () => void (deferClose = false);\n\n         spawned.stdout?.on('data', quickClose);\n         spawned.stderr?.on('data', quickClose);\n         spawned.on('error', quickClose);\n\n         spawned.on('close', (code: number) => events.close(code));\n         spawned.on('exit', (code: number) => events.exit(code));\n\n         try {\n            await events.result;\n            if (deferClose) {\n               await delay(50);\n            }\n            close(events.exitCode);\n         } catch (err) {\n            close(events.exitCode, err as Error);\n         }\n      },\n   };\n}\n", "import type { SimpleGitOptions } from '../types';\n\nimport { GitPluginError } from '../errors/git-plugin-error';\nimport { asArray } from '../utils';\nimport { PluginStore } from './plugin-store';\n\nconst WRONG_NUMBER_ERR = `Invalid value supplied for custom binary, requires a single string or an array containing either one or two strings`;\nconst WRONG_CHARS_ERR = `Invalid value supplied for custom binary, restricted characters must be removed or supply the unsafe.allowUnsafeCustomBinary option`;\n\nfunction isBadArgument(arg: string) {\n   return !arg || !/^([a-z]:)?([a-z0-9/.\\\\_-]+)$/i.test(arg);\n}\n\nfunction toBinaryConfig(\n   input: string[],\n   allowUnsafe: boolean\n): { binary: string; prefix?: string } {\n   if (input.length < 1 || input.length > 2) {\n      throw new GitPluginError(undefined, 'binary', WRONG_NUMBER_ERR);\n   }\n\n   const isBad = input.some(isBadArgument);\n   if (isBad) {\n      if (allowUnsafe) {\n         console.warn(WRONG_CHARS_ERR);\n      } else {\n         throw new GitPluginError(undefined, 'binary', WRONG_CHARS_ERR);\n      }\n   }\n\n   const [binary, prefix] = input;\n   return {\n      binary,\n      prefix,\n   };\n}\n\nexport function customBinaryPlugin(\n   plugins: PluginStore,\n   input: SimpleGitOptions['binary'] = ['git'],\n   allowUnsafe = false\n) {\n   let config = toBinaryConfig(asArray(input), allowUnsafe);\n\n   plugins.on('binary', (input) => {\n      config = toBinaryConfig(asArray(input), allowUnsafe);\n   });\n\n   plugins.append('spawn.binary', () => {\n      return config.binary;\n   });\n\n   plugins.append('spawn.args', (data) => {\n      return config.prefix ? [config.prefix, ...data] : data;\n   });\n}\n", "import { GitError } from '../errors/git-error';\nimport { GitExecutorResult, SimpleGitPluginConfig } from '../types';\nimport { SimpleGitPlugin } from './simple-git-plugin';\n\ntype TaskResult = Omit<GitExecutorResult, 'rejection'>;\n\nfunction isTaskError(result: TaskResult) {\n   return !!(result.exitCode && result.stdErr.length);\n}\n\nfunction getErrorMessage(result: TaskResult) {\n   return Buffer.concat([...result.stdOut, ...result.stdErr]);\n}\n\nexport function errorDetectionHandler(\n   overwrite = false,\n   isError = isTaskError,\n   errorMessage: (result: TaskResult) => Buffer | Error = getErrorMessage\n) {\n   return (error: Buffer | Error | undefined, result: TaskResult) => {\n      if ((!overwrite && error) || !isError(result)) {\n         return error;\n      }\n\n      return errorMessage(result);\n   };\n}\n\nexport function errorDetectionPlugin(\n   config: SimpleGitPluginConfig['errors']\n): SimpleGitPlugin<'task.error'> {\n   return {\n      type: 'task.error',\n      action(data, context) {\n         const error = config(data.error, {\n            stdErr: context.stdErr,\n            stdOut: context.stdOut,\n            exitCode: context.exitCode,\n         });\n\n         if (Buffer.isBuffer(error)) {\n            return { error: new GitError(undefined, error.toString('utf-8')) };\n         }\n\n         return {\n            error,\n         };\n      },\n   };\n}\n", "import { EventEmitter } from 'node:events';\n\nimport type {\n   SimpleGitPlugin,\n   SimpleGitPluginType,\n   SimpleGitPluginTypes,\n} from './simple-git-plugin';\nimport { append, asArray } from '../utils';\nimport type { SimpleGitPluginConfig } from '../types';\n\nexport class PluginStore {\n   private plugins: Set<SimpleGitPlugin<SimpleGitPluginType>> = new Set();\n   private events = new EventEmitter();\n\n   on<K extends keyof SimpleGitPluginConfig>(\n      type: K,\n      listener: (data: SimpleGitPluginConfig[K]) => void\n   ) {\n      this.events.on(type, listener);\n   }\n\n   reconfigure<K extends keyof SimpleGitPluginConfig>(type: K, data: SimpleGitPluginConfig[K]) {\n      this.events.emit(type, data);\n   }\n\n   public append<T extends SimpleGitPluginType>(type: T, action: SimpleGitPlugin<T>['action']) {\n      const plugin = append(this.plugins, { type, action });\n\n      return () => this.plugins.delete(plugin);\n   }\n\n   public add<T extends SimpleGitPluginType>(\n      plugin: void | SimpleGitPlugin<T> | SimpleGitPlugin<T>[]\n   ) {\n      const plugins: SimpleGitPlugin<T>[] = [];\n\n      asArray(plugin).forEach((plugin) => plugin && this.plugins.add(append(plugins, plugin)));\n\n      return () => {\n         plugins.forEach((plugin) => this.plugins.delete(plugin));\n      };\n   }\n\n   public exec<T extends SimpleGitPluginType>(\n      type: T,\n      data: SimpleGitPluginTypes[T]['data'],\n      context: SimpleGitPluginTypes[T]['context']\n   ): typeof data {\n      let output = data;\n      const contextual = Object.freeze(Object.create(context));\n\n      for (const plugin of this.plugins) {\n         if (plugin.type === type) {\n            output = plugin.action(output, contextual);\n         }\n      }\n\n      return output;\n   }\n}\n", "import { SimpleGitOptions } from '../types';\nimport { asNumber, including } from '../utils';\n\nimport { SimpleGitPlugin } from './simple-git-plugin';\n\nexport function progressMonitorPlugin(progress: Exclude<SimpleGitOptions['progress'], void>) {\n   const progressCommand = '--progress';\n   const progressMethods = ['checkout', 'clone', 'fetch', 'pull', 'push'];\n\n   const onProgress: SimpleGitPlugin<'spawn.after'> = {\n      type: 'spawn.after',\n      action(_data, context) {\n         if (!context.commands.includes(progressCommand)) {\n            return;\n         }\n\n         context.spawned.stderr?.on('data', (chunk: Buffer) => {\n            const message = /^([\\s\\S]+?):\\s*(\\d+)% \\((\\d+)\\/(\\d+)\\)/.exec(chunk.toString('utf8'));\n            if (!message) {\n               return;\n            }\n\n            progress({\n               method: context.method,\n               stage: progressEventStage(message[1]),\n               progress: asNumber(message[2]),\n               processed: asNumber(message[3]),\n               total: asNumber(message[4]),\n            });\n         });\n      },\n   };\n\n   const onArgs: SimpleGitPlugin<'spawn.args'> = {\n      type: 'spawn.args',\n      action(args, context) {\n         if (!progressMethods.includes(context.method)) {\n            return args;\n         }\n\n         return including(args, progressCommand);\n      },\n   };\n\n   return [onArgs, onProgress];\n}\n\nfunction progressEventStage(input: string) {\n   return String(input.toLowerCase().split(' ', 1)) || 'unknown';\n}\n", "import { SpawnOptions } from 'child_process';\nimport { pick } from '../utils';\nimport { SimpleGitPlugin } from './simple-git-plugin';\n\nexport function spawnOptionsPlugin(\n   spawnOptions: Partial<SpawnOptions>\n): SimpleGitPlugin<'spawn.options'> {\n   const options = pick(spawnOptions, ['uid', 'gid']);\n\n   return {\n      type: 'spawn.options',\n      action(data) {\n         return { ...options, ...data };\n      },\n   };\n}\n", "import type { SimpleGitPlugin } from './simple-git-plugin';\n\nimport type { SimpleGitOptions } from '../types';\nimport { GitPluginError } from '../errors/git-plugin-error';\n\nexport function timeoutPlugin({\n   block,\n   stdErr = true,\n   stdOut = true,\n}: Exclude<SimpleGitOptions['timeout'], undefined>): SimpleGitPlugin<'spawn.after'> | void {\n   if (block > 0) {\n      return {\n         type: 'spawn.after',\n         action(_data, context) {\n            let timeout: NodeJS.Timeout;\n\n            function wait() {\n               timeout && clearTimeout(timeout);\n               timeout = setTimeout(kill, block);\n            }\n\n            function stop() {\n               context.spawned.stdout?.off('data', wait);\n               context.spawned.stderr?.off('data', wait);\n               context.spawned.off('exit', stop);\n               context.spawned.off('close', stop);\n               timeout && clearTimeout(timeout);\n            }\n\n            function kill() {\n               stop();\n               context.kill(new GitPluginError(undefined, 'timeout', `block timeout reached`));\n            }\n\n            stdOut && context.spawned.stdout?.on('data', wait);\n            stdErr && context.spawned.stderr?.on('data', wait);\n            context.spawned.on('exit', stop);\n            context.spawned.on('close', stop);\n\n            wait();\n         },\n      };\n   }\n}\n", "import { SimpleGitPlugin } from './simple-git-plugin';\nimport { isPathSpec, toPaths } from '../args/pathspec';\n\nexport function suffixPathsPlugin(): SimpleGitPlugin<'spawn.args'> {\n   return {\n      type: 'spawn.args',\n      action(data) {\n         const prefix: string[] = [];\n         let suffix: undefined | string[];\n         function append(args: string[]) {\n            (suffix = suffix || []).push(...args);\n         }\n\n         for (let i = 0; i < data.length; i++) {\n            const param = data[i];\n\n            if (isPathSpec(param)) {\n               append(toPaths(param));\n               continue;\n            }\n\n            if (param === '--') {\n               append(\n                  data.slice(i + 1).flatMap((item) => (isPathSpec(item) && toPaths(item)) || item)\n               );\n               break;\n            }\n\n            prefix.push(param);\n         }\n\n         return !suffix ? prefix : [...prefix, '--', ...suffix.map(String)];\n      },\n   };\n}\n", "import { SimpleGitFactory } from '../../typings';\n\nimport * as api from './api';\nimport {\n   abortPlugin,\n   blockUnsafeOperationsPlugin,\n   commandConfigPrefixingPlugin,\n   completionDetectionPlugin,\n   customBinaryPlugin,\n   errorDetectionHandler,\n   errorDetectionPlugin,\n   PluginStore,\n   progressMonitorPlugin,\n   spawnOptionsPlugin,\n   timeoutPlugin,\n} from './plugins';\nimport { suffixPathsPlugin } from './plugins/suffix-paths.plugin';\nimport { createInstanceConfig, folderExists } from './utils';\nimport { SimpleGitOptions } from './types';\n\nconst Git = require('../git');\n\n/**\n * Adds the necessary properties to the supplied object to enable it for use as\n * the default export of a module.\n *\n * Eg: `module.exports = esModuleFactory({ something () {} })`\n */\nexport function esModuleFactory<T>(defaultExport: T) {\n   return Object.defineProperties(defaultExport, {\n      __esModule: { value: true },\n      default: { value: defaultExport },\n   }) as T & { __esModule: true; default: T };\n}\n\nexport function gitExportFactory(factory: SimpleGitFactory) {\n   return Object.assign(factory.bind(null), api);\n}\n\nexport function gitInstanceFactory(\n   baseDir?: string | Partial<SimpleGitOptions>,\n   options?: Partial<SimpleGitOptions>\n) {\n   const plugins = new PluginStore();\n   const config = createInstanceConfig(\n      (baseDir && (typeof baseDir === 'string' ? { baseDir } : baseDir)) || {},\n      options\n   );\n\n   if (!folderExists(config.baseDir)) {\n      throw new api.GitConstructError(\n         config,\n         `Cannot use simple-git on a directory that does not exist`\n      );\n   }\n\n   if (Array.isArray(config.config)) {\n      plugins.add(commandConfigPrefixingPlugin(config.config));\n   }\n\n   plugins.add(blockUnsafeOperationsPlugin(config.unsafe));\n   plugins.add(suffixPathsPlugin());\n   plugins.add(completionDetectionPlugin(config.completion));\n   config.abort && plugins.add(abortPlugin(config.abort));\n   config.progress && plugins.add(progressMonitorPlugin(config.progress));\n   config.timeout && plugins.add(timeoutPlugin(config.timeout));\n   config.spawnOptions && plugins.add(spawnOptionsPlugin(config.spawnOptions));\n\n   plugins.add(errorDetectionPlugin(errorDetectionHandler(true)));\n   config.errors && plugins.add(errorDetectionPlugin(config.errors));\n\n   customBinaryPlugin(plugins, config.binary, config.unsafe?.allowUnsafeCustomBinary);\n\n   return new Git(config, plugins);\n}\n", "import { SimpleGit, SimpleGitOptions } from '../../../typings';\n\nimport { GitResponseError } from '../errors/git-response-error';\nimport { gitInstanceFactory } from '../git-factory';\nimport { SimpleGitTaskCallback } from '../types';\n\nconst functionNamesBuilderApi = ['customBinary', 'env', 'outputHandler', 'silent'];\n\nconst functionNamesPromiseApi = [\n   'add',\n   'addAnnotatedTag',\n   'addConfig',\n   'addRemote',\n   'addTag',\n   'applyPatch',\n   'binaryCatFile',\n   'branch',\n   'branchLocal',\n   'catFile',\n   'checkIgnore',\n   'checkIsRepo',\n   'checkout',\n   'checkoutBranch',\n   'checkoutLatestTag',\n   'checkoutLocalBranch',\n   'clean',\n   'clone',\n   'commit',\n   'cwd',\n   'deleteLocalBranch',\n   'deleteLocalBranches',\n   'diff',\n   'diffSummary',\n   'exec',\n   'fetch',\n   'getRemotes',\n   'init',\n   'listConfig',\n   'listRemote',\n   'log',\n   'merge',\n   'mergeFromTo',\n   'mirror',\n   'mv',\n   'pull',\n   'push',\n   'pushTags',\n   'raw',\n   'rebase',\n   'remote',\n   'removeRemote',\n   'reset',\n   'revert',\n   'revparse',\n   'rm',\n   'rmKeepLocal',\n   'show',\n   'stash',\n   'stashList',\n   'status',\n   'subModule',\n   'submoduleAdd',\n   'submoduleInit',\n   'submoduleUpdate',\n   'tag',\n   'tags',\n   'updateServerInfo',\n];\n\nexport function gitP(\n   ...args: [] | [string] | [Partial<SimpleGitOptions>] | [string, Partial<SimpleGitOptions>]\n): SimpleGit {\n   let git: any;\n\n   let chain = Promise.resolve();\n\n   try {\n      git = gitInstanceFactory(...args);\n   } catch (e) {\n      chain = Promise.reject(e);\n   }\n\n   function builderReturn() {\n      return promiseApi;\n   }\n\n   function chainReturn() {\n      return chain;\n   }\n\n   const promiseApi = [...functionNamesBuilderApi, ...functionNamesPromiseApi].reduce(\n      (api: any, name: string) => {\n         const isAsync = functionNamesPromiseApi.includes(name);\n\n         const valid = isAsync ? asyncWrapper(name, git) : syncWrapper(name, git, api);\n         const alternative = isAsync ? chainReturn : builderReturn;\n\n         Object.defineProperty(api, name, {\n            enumerable: false,\n            configurable: false,\n            value: git ? valid : alternative,\n         });\n\n         return api;\n      },\n      {}\n   );\n\n   return promiseApi as SimpleGit;\n\n   function asyncWrapper(fn: string, git: any): (...args: any[]) => Promise<any> {\n      return function (...args: any[]) {\n         if (typeof args[args.length] === 'function') {\n            throw new TypeError(\n               'Promise interface requires that handlers are not supplied inline, ' +\n                  'trailing function not allowed in call to ' +\n                  fn\n            );\n         }\n\n         return chain.then(function () {\n            return new Promise(function (resolve, reject) {\n               const callback: SimpleGitTaskCallback = (err: Error | null, result?: any) => {\n                  if (err) {\n                     return reject(toError(err));\n                  }\n\n                  resolve(result);\n               };\n               args.push(callback);\n\n               git[fn].apply(git, args);\n            });\n         });\n      };\n   }\n\n   function syncWrapper(fn: string, git: any, api: SimpleGit) {\n      return (...args: any[]) => {\n         git[fn](...args);\n\n         return api;\n      };\n   }\n}\n\nfunction toError(error: Error | string | any): Error {\n   if (error instanceof Error) {\n      return error;\n   }\n\n   if (typeof error === 'string') {\n      return new Error(error);\n   }\n\n   return new GitResponseError(error);\n}\n", "import { gitInstanceFactory } from './lib/git-factory';\n\nexport { gitP } from './lib/runners/promise-wrapped';\nexport * from './lib/api';\n\nexport const simpleGit = gitInstanceFactory;\n\nexport default gitInstanceFactory;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEO,SAAS,YAAY,OAAiB;AAC1C,QAAM,MAAM,IAAI,OAAO,KAAK;AAC5B,QAAM,IAAI,KAAK,KAAK;AAEpB,SAAO;AACV;AAEO,SAAS,WAAW,MAAwC;AAChE,SAAO,gBAAgB,UAAU,MAAM,IAAI,IAAI;AAClD;AAEO,SAAS,QAAQ,UAA4B;AACjD,SAAO,MAAM,IAAI,QAAQ,KAAK,CAAC;AAClC;AAfA,IAAM;AAAN;AAAA;AAAA;AAAA,IAAM,QAAQ,oBAAI,QAA0B;AAAA;AAAA;;;ACA5C,IA2Ba;AA3Bb;AAAA;AAAA;AA2BO,IAAM,WAAN,cAAuB,MAAM;AAAA,MACjC,YACU,MACP,SACD;AACC,cAAM,OAAO;AAHN;AAIP,eAAO,eAAe,MAAM,WAAW,SAAS;AAAA,MACnD;AAAA,IACH;AAAA;AAAA;;;ACnCA,IAsBa;AAtBb;AAAA;AAAA;AAAA;AAsBO,IAAM,mBAAN,cAAwC,SAAS;AAAA,MACrD,YAImB,KAChB,SACD;AACC,cAAM,QAAW,WAAW,OAAO,GAAG,CAAC;AAHvB;AAAA,MAInB;AAAA,IACH;AAAA;AAAA;;;AChCA,IAUa;AAVb;AAAA;AAAA;AAAA;AAUO,IAAM,yBAAN,cAAqC,SAAS;AAAA,MAClD,YAAY,SAAkB;AAC3B,cAAM,QAAW,OAAO;AAAA,MAC3B;AAAA,IACH;AAAA;AAAA;;;ACdA,SAAS,QAAQ,cAAc;AAWxB,SAAS,WAAgC,QAAoB;AACjE,SAAO,OAAO,WAAW,aAAa,SAAS;AAClD;AAMO,SAAS,eAAmC,QAA8B;AAC9E,SAAO,OAAO,WAAW,cAAc,WAAW;AACrD;AAEO,SAAS,QAAQ,OAAe,MAAgC;AACpE,QAAM,QAAQ,MAAM,QAAQ,IAAI;AAChC,MAAI,SAAS,GAAG;AACb,WAAO,CAAC,OAAO,EAAE;AAAA,EACpB;AAEA,SAAO,CAAC,MAAM,OAAO,GAAG,KAAK,GAAG,MAAM,OAAO,QAAQ,CAAC,CAAC;AAC1D;AAIO,SAAS,MAAM,OAA2B,SAAS,GAAmB;AAC1E,SAAO,YAAY,KAAK,KAAK,MAAM,SAAS,SAAS,MAAM,UAAU;AACxE;AAKO,SAAS,KAAK,OAAgB,SAAS,GAAG;AAC9C,MAAI,YAAY,KAAK,KAAK,MAAM,SAAS,QAAQ;AAC9C,WAAO,MAAM,MAAM,SAAS,IAAI;AAAA,EACnC;AACH;AAIA,SAAS,YAAY,OAAgC;AAClD,SAAO,CAAC,EAAE,SAAS,OAAO,MAAM,WAAW;AAC9C;AAEO,SAAS,mBAAmB,QAAQ,IAAIA,WAAU,MAAM,YAAY,MAAgB;AACxF,SAAO,MAAM,MAAM,SAAS,EAAE,OAAO,CAAC,QAAQ,SAAS;AACpD,UAAM,cAAcA,WAAU,KAAK,KAAK,IAAI;AAC5C,QAAI,aAAa;AACd,aAAO,KAAK,WAAW;AAAA,IAC1B;AACA,WAAO;AAAA,EACV,GAAG,CAAC,CAAa;AACpB;AAIO,SAAS,uBACb,OACA,UACI;AACJ,SAAO,mBAAmB,OAAO,IAAI,EAAE,IAAI,CAAC,SAAS,SAAS,IAAI,CAAC;AACtE;AAEO,SAAS,aAAa,MAAuB;AACjD,SAAO,OAAO,MAAM,MAAM;AAC7B;AAKO,SAAS,OAAU,QAAsB,MAAsB;AACnE,MAAI,MAAM,QAAQ,MAAM,GAAG;AACxB,QAAI,CAAC,OAAO,SAAS,IAAI,GAAG;AACzB,aAAO,KAAK,IAAI;AAAA,IACnB;AAAA,EACH,OAAO;AACJ,WAAO,IAAI,IAAI;AAAA,EAClB;AACA,SAAO;AACV;AAKO,SAAS,UAAa,QAAa,MAAwB;AAC/D,MAAI,MAAM,QAAQ,MAAM,KAAK,CAAC,OAAO,SAAS,IAAI,GAAG;AAClD,WAAO,KAAK,IAAI;AAAA,EACnB;AAEA,SAAO;AACV;AAEO,SAAS,OAAU,QAAsB,MAAY;AACzD,MAAI,MAAM,QAAQ,MAAM,GAAG;AACxB,UAAM,QAAQ,OAAO,QAAQ,IAAI;AACjC,QAAI,SAAS,GAAG;AACb,aAAO,OAAO,OAAO,CAAC;AAAA,IACzB;AAAA,EACH,OAAO;AACJ,WAAO,OAAO,IAAI;AAAA,EACrB;AACA,SAAO;AACV;AAMO,SAAS,QAAW,QAAsB;AAC9C,SAAO,MAAM,QAAQ,MAAM,IAAI,SAAS,CAAC,MAAM;AAClD;AAEO,SAAS,YAAY,KAAa;AACtC,SAAO,IAAI,QAAQ,cAAc,CAAC,MAAM,QAAQ;AAC7C,WAAO,IAAI,YAAY;AAAA,EAC1B,CAAC;AACJ;AAEO,SAAS,cAAiB,QAA2B;AACzD,SAAO,QAAQ,MAAM,EAAE,IAAI,MAAM;AACpC;AAEO,SAAS,SAAS,QAAmC,QAAQ,GAAG;AACpE,MAAI,UAAU,MAAM;AACjB,WAAO;AAAA,EACV;AAEA,QAAM,MAAM,SAAS,QAAQ,EAAE;AAC/B,SAAO,MAAM,GAAG,IAAI,QAAQ;AAC/B;AAEO,SAAS,cAAiB,OAAY,QAAgB;AAC1D,QAAM,SAAc,CAAC;AACrB,WAAS,IAAI,GAAG,MAAM,MAAM,QAAQ,IAAI,KAAK,KAAK;AAC/C,WAAO,KAAK,QAAQ,MAAM,EAAE;AAAA,EAC/B;AACA,SAAO;AACV;AAEO,SAAS,eAAe,OAAkC;AAC9D,UAAQ,MAAM,QAAQ,KAAK,IAAI,OAAO,OAAO,KAAK,IAAI,OAAO,SAAS,OAAO;AAChF;AAKO,SAAS,KAAK,QAA6B,YAAsB;AACrE,SAAO,OAAO;AAAA,IACX,CAAC;AAAA,IACD,GAAG,WAAW,IAAI,CAAC,aAAc,YAAY,SAAS,EAAE,CAAC,WAAW,OAAO,UAAU,IAAI,CAAC,CAAE;AAAA,EAC/F;AACH;AAEO,SAAS,MAAM,WAAW,GAAkB;AAChD,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAW,MAAM,QAAQ,CAAC;AAC1D;AAEO,SAAS,OAAU,OAAkB;AACzC,MAAI,UAAU,OAAO;AAClB,WAAO;AAAA,EACV;AACA,SAAO;AACV;AA3KA,IAGa,MAEA,MA4GA;AAjHb;AAAA;AAAA;AAGO,IAAM,OAAO;AAEb,IAAM,OAAiC,MAAM;AAAA,IAAC;AA4G9C,IAAM,iBAAiB,OAAO,UAAU,SAAS,KAAK,KAAK,OAAO,UAAU,QAAQ;AAAA;AAAA;;;ACpGpF,SAAS,WAAiB,OAAU,QAAoC,KAAmB;AAC/F,MAAI,OAAO,KAAK,GAAG;AAChB,WAAO;AAAA,EACV;AACA,SAAO,UAAU,SAAS,IAAI,MAAM;AACvC;AAMO,SAAS,iBACb,OACA,MACoB;AACpB,QAAM,OAAO,WAAW,KAAK,IAAI,WAAW,OAAO;AAEnD,SACG,wBAAwB,KAAK,IAAI,MAChC,CAAC,QAAQ,CAAC,KAAK,SAAS,IAAuC;AAEtE;AAiBO,SAAS,kBAAoC,OAAgC;AACjF,SAAO,CAAC,CAAC,SAAS,eAAe,KAAK,MAAM;AAC/C;AAEO,SAAS,eAAe,OAAmC;AAC/D,SAAO,OAAO,UAAU;AAC3B;AAzDA,IAoBa,aAgBA,cAIA,mBAIA,2BAeA;AA3Db;AAAA;AAAA;AACA;AACA;AAkBO,IAAM,cAAmD,CAAC,UAA+B;AAC7F,aAAO,MAAM,QAAQ,KAAK;AAAA,IAC7B;AAcO,IAAM,eAAgD,CAAC,UAA2B;AACtF,aAAO,OAAO,UAAU;AAAA,IAC3B;AAEO,IAAM,oBAAuD,CAAC,UAA6B;AAC/F,aAAO,MAAM,QAAQ,KAAK,KAAK,MAAM,MAAM,YAAY;AAAA,IAC1D;AAEO,IAAM,4BAAwE,CAClF,UAC8B;AAC9B,aAAO,aAAa,KAAK,KAAM,MAAM,QAAQ,KAAK,KAAK,MAAM,MAAM,YAAY;AAAA,IAClF;AAWO,IAAM,kBAA+D,CACzE,UAC+B;AAC/B,UAAI,SAAS,QAAQ,0BAA0B,SAAS,OAAO,KAAK,GAAG;AACpE,eAAO;AAAA,MACV;AACA,aAAO,MAAM,QAAQ,KAAK,KAAK,OAAO,UAAU,YAAY,OAAO,MAAM,WAAW;AAAA,IACvF;AAAA;AAAA;;;AClEA,IAIY;AAJZ;AAAA;AAAA;AAIO,IAAK,YAAL,kBAAKC,eAAL;AACJ,MAAAA,sBAAA;AACA,MAAAA,sBAAA;AACA,MAAAA,sBAAA,eAAY,MAAZ;AACA,MAAAA,sBAAA,aAAU,OAAV;AAJS,aAAAA;AAAA,OAAA;AAAA;AAAA;;;ACJZ,IAEa;AAFb;AAAA;AAAA;AAEO,IAAM,mBAAN,MAA8D;AAAA,MAClE,YACmB,QACA,QACjB;AAFiB;AACA;AAAA,MAChB;AAAA,MAEH,YAAsC;AACnC,eAAO,IAAI,iBAAiB,KAAK,OAAO,SAAS,MAAM,GAAG,KAAK,OAAO,SAAS,MAAM,CAAC;AAAA,MACzF;AAAA,IACH;AAAA;AAAA;;;ACXA,IAAa,YAoDA;AApDb;AAAA;AAAA;AAAO,IAAM,aAAN,MAAoB;AAAA,MAKxB,YACG,QACA,YACD;AAPF,aAAU,UAAoB,CAAC;AAc/B,qBAAQ,CAAC,MAA8C,WAAuB;AAC3E,eAAK,aAAa;AAElB,cAAI,CAAC,KAAK,QAAQ,MAAM,CAAC,KAAK,UAAU,KAAK,SAAS,KAAK,OAAO,KAAK,KAAK,CAAC,CAAC,GAAG;AAC9E,mBAAO;AAAA,UACV;AAEA,iBAAO,KAAK,WAAW,QAAQ,KAAK,eAAe,CAAC,MAAM;AAAA,QAC7D;AAdG,aAAK,UAAU,MAAM,QAAQ,MAAM,IAAI,SAAS,CAAC,MAAM;AACvD,YAAI,YAAY;AACb,eAAK,aAAa;AAAA,QACrB;AAAA,MACH;AAAA,MAaU,WAAW,QAAW,OAAiC;AAC9D,cAAM,IAAI,MAAM,uCAAuC;AAAA,MAC1D;AAAA,MAEU,eAAe;AACtB,aAAK,QAAQ,SAAS;AAAA,MACzB;AAAA,MAEU,iBAAiB;AACxB,eAAO,KAAK;AAAA,MACf;AAAA,MAEU,SAAS,KAAa,OAAe,MAAe;AAC3D,cAAM,UAAU,QAAQ,IAAI,KAAK,IAAI;AACrC,YAAI,SAAS;AACV,eAAK,UAAU,OAAO,OAAO;AAAA,QAChC;AAEA,eAAO,CAAC,CAAC;AAAA,MACZ;AAAA,MAEU,UAAU,QAAgB,SAAmB;AACpD,aAAK,QAAQ,KAAK,GAAG,QAAQ,MAAM,CAAC,CAAC;AAAA,MACxC;AAAA,IACH;AAEO,IAAM,mBAAN,cAAkC,WAAc;AAAA,MAC1C,SAAS,KAAa,OAAe,MAAwB;AACpE,eAAO,aAAa,KAAK,OAAO,IAAI,CAAC,KAAK,MAAM,SAAS,KAAK,OAAO,IAAI;AAAA,MAC5E;AAAA,MAEU,UAAU,OAAe,SAAmB;AACnD,YAAI,QAAQ,KAAK,QAAQ,SAAS,GAAG;AAClC,gBAAM,UAAU,OAAO,OAAO;AAAA,QACjC;AAAA,MACH;AAAA,IACH;AAAA;AAAA;;;ACrDO,SAAS,wBACV,SACc;AACjB,QAAM,UAAU,QAAQ,IAAI;AAC5B,QAAM,SAA2B,OAAO;AAAA,IACrC,iBAAE,WAAY;AAAA,IACd,GAAG,QAAQ,OAAO,CAAC,MAAM,OAAO,MAAM,YAAY,CAAC;AAAA,EACtD;AAEA,SAAO,UAAU,OAAO,WAAW;AACnC,SAAO,UAAU,OAAO,YAAY;AAEpC,SAAO;AACV;AAtBA,IAEM;AAFN;AAAA;AAAA;AAEA,IAAM,iBAAoD;AAAA,MACvD,QAAQ;AAAA,MACR,wBAAwB;AAAA,MACxB,QAAQ,CAAC;AAAA,MACT,SAAS;AAAA,IACZ;AAAA;AAAA;;;ACIO,SAAS,kBACb,SACA,WAAqB,CAAC,GACb;AACT,MAAI,CAAC,kBAA2B,OAAO,GAAG;AACvC,WAAO;AAAA,EACV;AAEA,SAAO,OAAO,KAAK,OAAO,EAAE,OAAO,CAACC,WAAoB,QAAgB;AACrE,UAAM,QAAuB,QAAQ;AAErC,QAAI,WAAW,KAAK,GAAG;AACpB,MAAAA,UAAS,KAAK,KAAK;AAAA,IACtB,WAAW,iBAAiB,OAAO,CAAC,SAAS,CAAC,GAAG;AAC9C,MAAAA,UAAS,KAAK,MAAM,MAAM,KAAK;AAAA,IAClC,OAAO;AACJ,MAAAA,UAAS,KAAK,GAAG;AAAA,IACpB;AAEA,WAAOA;AAAA,EACV,GAAG,QAAQ;AACd;AAEO,SAAS,mBACb,MACA,mBAAmB,GACnB,aAAa,OACJ;AACT,QAAM,UAAoB,CAAC;AAE3B,WAAS,IAAI,GAAG,MAAM,mBAAmB,IAAI,KAAK,SAAS,kBAAkB,IAAI,KAAK,KAAK;AACxF,QAAI,gBAAgB,SAAS,OAAO,KAAK,EAAE,GAAG;AAC3C,cAAQ,KAAK,OAAO,KAAK,EAAE,CAAC;AAAA,IAC/B;AAAA,EACH;AAEA,oBAAkB,wBAAwB,IAAI,GAAG,OAAO;AACxD,MAAI,CAAC,YAAY;AACd,YAAQ,KAAK,GAAG,sBAAsB,IAAI,CAAC;AAAA,EAC9C;AAEA,SAAO;AACV;AAEA,SAAS,sBAAsB,MAAkB;AAC9C,QAAM,sBAAsB,OAAO,KAAK,IAAI,MAAM;AAClD,SAAO,WAAW,KAAK,MAAM,sBAAsB,IAAI,CAAC,GAAG,aAAa,CAAC,CAAC;AAC7E;AAMO,SAAS,wBAAwB,MAAkC;AACvE,QAAM,sBAAsB,eAAe,KAAK,IAAI,CAAC;AACrD,SAAO,WAAW,KAAK,MAAM,sBAAsB,IAAI,CAAC,GAAG,iBAAiB;AAC/E;AAMO,SAAS,yBACb,MACA,cAAc,MACqB;AACnC,QAAM,WAAW,WAAW,KAAK,IAAI,CAAC;AACtC,SAAO,eAAe,eAAe,QAAQ,IAAI,WAAW;AAC/D;AA/EA;AAAA;AAAA;AAAA;AAOA;AAEA;AAAA;AAAA;;;ACJO,SAAS,eACbC,SACA,SACD;AACC,SAAOA,QAAO,QAAQ,QAAQ,QAAQ,MAAM;AAC/C;AAEO,SAAS,oBACb,QACAC,WACA,OACA,OAAO,MACL;AACF,UAAQ,KAAK,EAAE,QAAQ,CAAC,SAAS;AAC9B,aAAS,QAAQ,mBAAmB,MAAM,IAAI,GAAG,IAAI,GAAG,MAAM,MAAM,QAAQ,IAAI,KAAK,KAAK;AACvF,YAAM,OAAO,CAAC,SAAS,MAAM;AAC1B,YAAI,IAAI,UAAU,KAAK;AACpB;AAAA,QACH;AACA,eAAO,MAAM,IAAI;AAAA,MACpB;AAEA,MAAAA,UAAQ,KAAK,CAAC,EAAE,MAAM,MAAM,MAAM,MAAM,MAAM,CAAC;AAAA,IAClD;AAAA,EACH,CAAC;AAED,SAAO;AACV;AAhCA;AAAA;AAAA;AAGA;AAAA;AAAA;;;ACHA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;;;ACPA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAqBO,SAAS,gBAAgB,QAAsD;AACnF,UAAQ;AAAA,SACA;AACF,aAAO,oBAAoB;AAAA,SACzB;AACF,aAAO,oBAAoB;AAAA;AAGjC,QAAM,WAAW,CAAC,aAAa,uBAAuB;AAEtD,SAAO;AAAA,IACJ;AAAA,IACA,QAAQ;AAAA,IACR;AAAA,IACA;AAAA,EACH;AACH;AAEO,SAAS,sBAA2C;AACxD,QAAM,WAAW,CAAC,aAAa,WAAW;AAE1C,SAAO;AAAA,IACJ;AAAA,IACA,QAAQ;AAAA,IACR;AAAA,IACA,OAAO,MAAM;AACV,aAAO,aAAa,KAAK,KAAK,KAAK,CAAC;AAAA,IACvC;AAAA,EACH;AACH;AAEO,SAAS,sBAA2C;AACxD,QAAM,WAAW,CAAC,aAAa,sBAAsB;AAErD,SAAO;AAAA,IACJ;AAAA,IACA,QAAQ;AAAA,IACR;AAAA,IACA;AAAA,EACH;AACH;AAEA,SAAS,iBAAiB,OAAuB;AAC9C,SAAO,8CAA8C,KAAK,OAAO,KAAK,CAAC;AAC1E;AAjEA,IAGY,kBAMN,SAQA;AAjBN;AAAA;AAAA;AAAA;AAGO,IAAK,mBAAL,kBAAKC,sBAAL;AACJ,MAAAA,kBAAA,UAAO;AACP,MAAAA,kBAAA,aAAU;AACV,MAAAA,kBAAA,kBAAe;AAHN,aAAAA;AAAA,OAAA;AAMZ,IAAM,UAA0C,CAAC,EAAE,SAAS,GAAG,OAAO,MAAM,SAAS;AAClF,UAAI,kCAAkC,iBAAiB,KAAK,GAAG;AAC5D,eAAO,KAAK,OAAO,KAAK,OAAO,CAAC;AAAA,MACnC;AAEA,WAAK,KAAK;AAAA,IACb;AAEA,IAAM,SAAwC,CAAC,SAAS;AACrD,aAAO,KAAK,KAAK,MAAM;AAAA,IAC1B;AAAA;AAAA;;;ACJO,SAAS,mBAAmB,QAAiB,MAA4B;AAC7E,QAAM,UAAU,IAAI,cAAc,MAAM;AACxC,QAAM,SAAS,SAAS,sBAAsB;AAE9C,qBAAmB,IAAI,EAAE,QAAQ,CAAC,SAAS;AACxC,UAAM,UAAU,KAAK,QAAQ,QAAQ,EAAE;AAEvC,YAAQ,MAAM,KAAK,OAAO;AAC1B,KAAC,eAAe,KAAK,OAAO,IAAI,QAAQ,UAAU,QAAQ,OAAO,KAAK,OAAO;AAAA,EAChF,CAAC;AAED,SAAO;AACV;AA3BA,IAGa,eAQP,eACA,qBACA;AAbN;AAAA;AAAA;AACA;AAEO,IAAM,gBAAN,MAA4C;AAAA,MAKhD,YAA4B,QAAiB;AAAjB;AAJ5B,aAAO,QAAkB,CAAC;AAC1B,aAAO,QAAkB,CAAC;AAC1B,aAAO,UAAoB,CAAC;AAAA,MAEkB;AAAA,IACjD;AAEA,IAAM,gBAAgB;AACtB,IAAM,sBAAsB;AAC5B,IAAM,iBAAiB;AAAA;AAAA;;;ACbvB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYO,SAAS,cAAcC,SAAoC;AAC/D,SAAO;AAAA,IACJ,UAAU;AAAA,IACV,QAAQ;AAAA,IACR,QAAAA;AAAA,EACH;AACH;AAEO,SAAS,uBAAuB,OAAkC;AACtE,SAAO;AAAA,IACJ,UAAU;AAAA,IACV,QAAQ;AAAA,IACR,SAAS;AACN,YAAM,OAAO,UAAU,WAAW,IAAI,uBAAuB,KAAK,IAAI;AAAA,IACzE;AAAA,EACH;AACH;AAEO,SAAS,0BAA0B,UAAoBC,WAAU,OAA2B;AAChG,SAAO;AAAA,IACJ;AAAA,IACA,QAAQ;AAAA,IACR,OAAO,MAAM;AACV,aAAOA,WAAU,OAAO,IAAI,EAAE,KAAK,IAAI;AAAA,IAC1C;AAAA,EACH;AACH;AAEO,SAAS,0BAA0B,UAAqC;AAC5E,SAAO;AAAA,IACJ;AAAA,IACA,QAAQ;AAAA,IACR,OAAO,QAAQ;AACZ,aAAO;AAAA,IACV;AAAA,EACH;AACH;AAEO,SAAS,aAAgB,MAA+C;AAC5E,SAAO,KAAK,WAAW;AAC1B;AAEO,SAAS,YAAe,MAA2C;AACvE,SAAO,KAAK,WAAW,WAAW,CAAC,KAAK,SAAS;AACpD;AAxDA,IAGa;AAHb;AAAA;AAAA;AAAA;AAGO,IAAM,iBAAqB,CAAC;AAAA;AAAA;;;ACHnC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAkCO,SAAS,qBAAqB,MAA0B,YAAsB;AAClF,QAAM,EAAE,WAAW,SAAS,MAAM,IAAI,gBAAgB,IAAI;AAE1D,MAAI,CAAC,WAAW;AACb,WAAO,uBAAuB,0BAA0B;AAAA,EAC3D;AAEA,MAAI,CAAC,MAAM,SAAS;AACjB,WAAO,uBAAuB,8BAA8B,KAAK,UAAU,IAAI,CAAC;AAAA,EACnF;AAEA,UAAQ,KAAK,GAAG,UAAU;AAE1B,MAAI,QAAQ,KAAK,iBAAiB,GAAG;AAClC,WAAO,uBAAuB,6BAA6B;AAAA,EAC9D;AAEA,SAAO,UAAU,WAAW,OAAO;AACtC;AAEO,SAAS,UAAU,MAAiB,YAAgD;AACxF,QAAM,WAAqB,CAAC,SAAS,IAAI,QAAQ,GAAG,UAAU;AAE9D,SAAO;AAAA,IACJ;AAAA,IACA,QAAQ;AAAA,IACR,OAAO,MAA4B;AAChC,aAAO,mBAAmB,SAAS,mBAAsB,IAAI;AAAA,IAChE;AAAA,EACH;AACH;AAEO,SAAS,oBAAoB,OAA0C;AAC3E,SAAO,MAAM,QAAQ,KAAK,KAAK,MAAM,MAAM,CAAC,SAAS,kBAAkB,IAAI,IAAI,CAAC;AACnF;AAEA,SAAS,gBAAgB,OAAe;AACrC,MAAI;AACJ,MAAI,UAAoB,CAAC;AACzB,MAAI,QAAQ,EAAE,WAAW,OAAO,SAAS,KAAK;AAE9C,QACI,QAAQ,YAAY,EAAE,EACtB,MAAM,EAAE,EACR,QAAQ,CAAC,SAAS;AAChB,QAAI,YAAY,IAAI,GAAG;AACpB,kBAAY;AACZ,YAAM,YAAY;AAAA,IACrB,OAAO;AACJ,YAAM,UAAU,MAAM,WAAW,cAAe,QAAQ,QAAQ,UAAU,IAAI,MAAO;AAAA,IACxF;AAAA,EACH,CAAC;AAEJ,SAAO;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,EACH;AACH;AAEA,SAAS,YAAY,WAA4C;AAC9D,SAAO,cAAc,mBAAsB,cAAc;AAC5D;AAEA,SAAS,cAAc,QAAyB;AAC7C,SAAO,YAAY,KAAK,MAAM,KAAK,kBAAkB,IAAI,OAAO,OAAO,CAAC,CAAC;AAC5E;AAEA,SAAS,kBAAkB,QAAyB;AACjD,MAAI,UAAU,KAAK,MAAM,GAAG;AACzB,WAAO,OAAO,QAAQ,GAAG,IAAI;AAAA,EAChC;AAEA,SAAO,WAAW;AACrB;AA5GA,IAMa,+BACA,4BACA,6BAKD,cAgBN;AA7BN;AAAA;AAAA;AACA;AAEA;AACA;AAEO,IAAM,gCAAgC;AACtC,IAAM,6BAA6B;AACnC,IAAM,8BAA8B;AAKpC,IAAK,eAAL,kBAAKC,kBAAL;AACJ,MAAAA,cAAA,aAAU;AACV,MAAAA,cAAA,WAAQ;AACR,MAAAA,cAAA,sBAAmB;AACnB,MAAAA,cAAA,kBAAe;AACf,MAAAA,cAAA,eAAY;AACZ,MAAAA,cAAA,WAAQ;AACR,MAAAA,cAAA,eAAY;AAPH,aAAAA;AAAA,OAAA;AAgBZ,IAAM,oBAAiC,oBAAI,IAAI;AAAA,MAC5C;AAAA,MACA,GAAG,cAAc,OAAO,OAAO,YAAmB,CAAC;AAAA,IACtD,CAAC;AAAA;AAAA;;;ACaM,SAAS,iBAAiB,MAA0B;AACxD,QAAM,SAAS,IAAI,WAAW;AAE9B,aAAW,QAAQ,aAAa,IAAI,GAAG;AACpC,WAAO,SAAS,KAAK,MAAM,OAAO,KAAK,GAAG,GAAG,KAAK,KAAK;AAAA,EAC1D;AAEA,SAAO;AACV;AAEO,SAAS,gBAAgB,MAAc,KAA8B;AACzE,MAAI,QAAuB;AAC3B,QAAM,SAAmB,CAAC;AAC1B,QAAM,SAAgC,oBAAI,IAAI;AAE9C,aAAW,QAAQ,aAAa,MAAM,GAAG,GAAG;AACzC,QAAI,KAAK,QAAQ,KAAK;AACnB;AAAA,IACH;AAEA,WAAO,KAAM,QAAQ,KAAK,KAAM;AAEhC,QAAI,CAAC,OAAO,IAAI,KAAK,IAAI,GAAG;AACzB,aAAO,IAAI,KAAK,MAAM,CAAC,CAAC;AAAA,IAC3B;AAEA,WAAO,IAAI,KAAK,IAAI,EAAG,KAAK,KAAK;AAAA,EACpC;AAEA,SAAO;AAAA,IACJ;AAAA,IACA,OAAO,MAAM,KAAK,OAAO,KAAK,CAAC;AAAA,IAC/B;AAAA,IACA;AAAA,IACA;AAAA,EACH;AACH;AAEA,SAAS,eAAe,UAA0B;AAC/C,SAAO,SAAS,QAAQ,YAAY,EAAE;AACzC;AAEA,UAAU,aAAa,MAAc,eAA8B,MAAM;AACtE,QAAM,QAAQ,KAAK,MAAM,IAAI;AAE7B,WAAS,IAAI,GAAG,MAAM,MAAM,SAAS,GAAG,IAAI,OAAO;AAChD,UAAM,OAAO,eAAe,MAAM,IAAI;AAEtC,QAAI,QAAQ,MAAM;AAClB,QAAI,MAAM;AAEV,QAAI,MAAM,SAAS,IAAI,GAAG;AACvB,YAAM,OAAO,QAAQ,OAAO,IAAI;AAChC,YAAM,KAAK;AACX,cAAQ,KAAK;AAAA,IAChB;AAEA,UAAM,EAAE,MAAM,KAAK,MAAM;AAAA,EAC5B;AACH;AAxGA,IAGa;AAHb;AAAA;AAAA;AACA;AAEO,IAAM,aAAN,MAA8C;AAAA,MAA9C;AACJ,aAAO,QAAkB,CAAC;AAC1B,aAAO,SAA+C,uBAAO,OAAO,IAAI;AAAA;AAAA,MAIxE,IAAW,MAAoB;AAC5B,YAAI,CAAC,KAAK,MAAM;AACb,eAAK,OAAO,KAAK,MAAM,OAAO,CAAC,KAAmB,SAAiB;AAChE,mBAAO,OAAO,OAAO,KAAK,KAAK,OAAO,KAAK;AAAA,UAC9C,GAAG,CAAC,CAAC;AAAA,QACR;AAEA,eAAO,KAAK;AAAA,MACf;AAAA,MAEO,QAAQ,MAA4B;AACxC,YAAI,EAAE,QAAQ,KAAK,SAAS;AACzB,gBAAM,SAAS,KAAK,KAAK,KAAK;AAC9B,eAAK,OAAO,QAAQ,SAAS,OAAO,OAAO,KAAK,OAAO,OAAO,IAAI,CAAC;AAEnE,eAAK,MAAM,KAAK,IAAI;AAAA,QACvB;AAEA,eAAO,KAAK,OAAO;AAAA,MACtB;AAAA,MAEO,SAAS,MAAc,KAAa,OAAe;AACvD,cAAM,SAAS,KAAK,QAAQ,IAAI;AAEhC,YAAI,CAAC,OAAO,eAAe,GAAG,GAAG;AAC9B,iBAAO,OAAO;AAAA,QACjB,WAAW,MAAM,QAAQ,OAAO,IAAI,GAAG;AACpC,UAAC,OAAO,KAAkB,KAAK,KAAK;AAAA,QACvC,OAAO;AACJ,iBAAO,OAAO,CAAC,OAAO,MAAgB,KAAK;AAAA,QAC9C;AAEA,aAAK,OAAO;AAAA,MACf;AAAA,IACH;AAAA;AAAA;;;AC9BA,SAAS,cACN,OACA,UACmB;AACnB,MAAI,OAAO,UAAU,YAAY,eAAe,eAAe,KAAK,GAAG;AACpE,WAAO;AAAA,EACV;AACA,SAAO;AACV;AAEA,SAAS,cACN,KACA,OACAC,SACA,OACmB;AACnB,QAAM,WAAqB,CAAC,UAAU,KAAK,OAAO;AAElD,MAAIA,SAAQ;AACT,aAAS,KAAK,OAAO;AAAA,EACxB;AAEA,WAAS,KAAK,KAAK,KAAK;AAExB,SAAO;AAAA,IACJ;AAAA,IACA,QAAQ;AAAA,IACR,OAAO,MAAsB;AAC1B,aAAO;AAAA,IACV;AAAA,EACH;AACH;AAEA,SAAS,cAAc,KAAa,OAAqD;AACtF,QAAM,WAAqB,CAAC,UAAU,UAAU,iBAAiB,aAAa,GAAG;AAEjF,MAAI,OAAO;AACR,aAAS,OAAO,GAAG,GAAG,KAAK,OAAO;AAAA,EACrC;AAEA,SAAO;AAAA,IACJ;AAAA,IACA,QAAQ;AAAA,IACR,OAAO,MAAM;AACV,aAAO,gBAAgB,MAAM,GAAG;AAAA,IACnC;AAAA,EACH;AACH;AAEA,SAAS,eAAe,OAAuD;AAC5E,QAAM,WAAW,CAAC,UAAU,UAAU,iBAAiB,QAAQ;AAE/D,MAAI,OAAO;AACR,aAAS,KAAK,KAAK,OAAO;AAAA,EAC7B;AAEA,SAAO;AAAA,IACJ;AAAA,IACA,QAAQ;AAAA,IACR,OAAO,MAAc;AAClB,aAAO,iBAAiB,IAAI;AAAA,IAC/B;AAAA,EACH;AACH;AAEe,SAAR,iBAA+E;AACnF,SAAO;AAAA,IACJ,UAA8B,KAAa,UAAkB,MAAiB;AAC3E,aAAO,KAAK;AAAA,QACT;AAAA,UACG;AAAA,UACA;AAAA,UACA,KAAK,OAAO;AAAA,UACZ,cAAc,KAAK,IAAI,mBAAoB;AAAA,QAC9C;AAAA,QACA,yBAAyB,SAAS;AAAA,MACrC;AAAA,IACH;AAAA,IAEA,UAA8B,KAAa,OAAwB;AAChE,aAAO,KAAK;AAAA,QACT,cAAc,KAAK,cAAc,OAAO,MAAS,CAAC;AAAA,QAClD,yBAAyB,SAAS;AAAA,MACrC;AAAA,IACH;AAAA,IAEA,cAAkC,MAAiB;AAChD,aAAO,KAAK;AAAA,QACT,eAAe,cAAc,KAAK,IAAI,MAAS,CAAC;AAAA,QAChD,yBAAyB,SAAS;AAAA,MACrC;AAAA,IACH;AAAA,EACH;AACH;AA1GA,IAMY;AANZ;AAAA;AAAA;AACA;AAGA;AAEO,IAAK,iBAAL,kBAAKC,oBAAL;AACJ,MAAAA,gBAAA,YAAS;AACT,MAAAA,gBAAA,YAAS;AACT,MAAAA,gBAAA,WAAQ;AACR,MAAAA,gBAAA,cAAW;AAJF,aAAAA;AAAA,OAAA;AAAA;AAAA;;;ACQL,SAAS,iBAAiB,OAAwC;AACtE,SAAO,eAAe,IAAI,KAAuB;AACpD;AAhBA,IAAY,gBAYN;AAZN;AAAA;AAAA;AAAO,IAAK,iBAAL,kBAAKC,oBAAL;AACJ,MAAAA,gBAAA,WAAQ;AACR,MAAAA,gBAAA,YAAS;AACT,MAAAA,gBAAA,aAAU;AACV,MAAAA,gBAAA,cAAW;AACX,MAAAA,gBAAA,aAAU;AACV,MAAAA,gBAAA,aAAU;AACV,MAAAA,gBAAA,cAAW;AACX,MAAAA,gBAAA,aAAU;AACV,MAAAA,gBAAA,YAAS;AATA,aAAAA;AAAA,OAAA;AAYZ,IAAM,iBAAiB,IAAI,IAAI,OAAO,OAAO,cAAc,CAAC;AAAA;AAAA;;;ACoCrD,SAAS,oBAAoB,QAAgC;AACjE,SAAO,IAAI,UAAU,EAAE,MAAM,GAAG,MAAM;AACzC;AAEA,SAAS,UAAU,MAA0B;AAC1C,QAAM,QAA6B,oBAAI,IAAY;AACnD,QAAM,UAAiC,CAAC;AAExC,yBAAuB,MAAM,CAAC,UAAU;AACrC,UAAM,CAAC,MAAM,MAAM,OAAO,IAAI,MAAM,MAAM,IAAI;AAC9C,UAAM,IAAI,IAAI;AACd,KAAC,QAAQ,QAAQ,QAAQ,SAAS,CAAC,GAAG,KAAK;AAAA,MACxC,MAAM,SAAS,IAAI;AAAA,MACnB;AAAA,MACA;AAAA,IACH,CAAC;AAAA,EACJ,CAAC;AAED,SAAO;AAAA,IACJ;AAAA,IACA;AAAA,EACH;AACH;AAEe,SAAR,eAA6C;AACjD,SAAO;AAAA,IACJ,KAAyB,YAAmC;AACzD,YAAM,OAAO,yBAAyB,SAAS;AAC/C,YAAM,UAAU,mBAAmB,SAAS;AAE5C,iBAAW,UAAU,mBAAmB;AACrC,YAAI,QAAQ,SAAS,MAAM,GAAG;AAC3B,iBAAO,KAAK;AAAA,YACT,uBAAuB,qBAAqB,2BAA2B;AAAA,YACvE;AAAA,UACH;AAAA,QACH;AAAA,MACH;AAEA,UAAI,OAAO,eAAe,UAAU;AACjC,qBAAa,iBAAiB,EAAE,MAAM,UAAU;AAAA,MACnD;AAEA,YAAM,WAAW,CAAC,QAAQ,UAAU,MAAM,eAAe,GAAG,SAAS,GAAG,UAAU;AAElF,aAAO,KAAK;AAAA,QACT;AAAA,UACG;AAAA,UACA,QAAQ;AAAA,UACR,OAAO,QAAQ;AACZ,mBAAO,UAAU,MAAM;AAAA,UAC1B;AAAA,QACH;AAAA,QACA;AAAA,MACH;AAAA,IACH;AAAA,EACH;AACH;AAzGA,IAaM,mBAEA,OAfN,IAyBM;AAzBN;AAAA;AAAA;AAEA;AASA;AAEA,IAAM,oBAAoB,CAAC,IAAI;AAE/B,IAAM,QAAQ,OAAO,WAAW;AAUhC,IAAM,YAAN,MAAwC;AAAA,MAAxC;AACG,aAAS,MAAmB,CAAC;AAAA;AAAA,MAE7B,GAFS,YAEP,OAAO,aAAY;AAClB,mBAAW,SAAS,KAAK,QAAQ;AAC9B,gBAAM;AAAA,QACT;AAAA,MACH;AAAA,MAEA,OAAO,KAAe;AACnB,YAAI,UAAU,KAAK,OAAO,KAAK,SAAS,KAAK,GAAG,cAAc,KAAK,IAAI,GAAG,GAAG;AAC7E,eAAO;AAAA,MACV;AAAA,MAEA,SAAS,OAAiB;AACvB,aAAK,OAAO,KAAK,GAAG,cAAc,OAAO,IAAI,CAAC;AAC9C,eAAO;AAAA,MACV;AAAA,IACH;AAAA;AAAA;;;AC3CA;AAAA;AAAA;AAAA;AAAA;AAAA;AAiBO,SAAS,UAAU,MAAwB,YAAsB;AACrE,QAAM,WAAqB,CAAC,OAAO;AACnC,MAAI,iBAAiB,IAAI,GAAG;AACzB,aAAS,KAAK,KAAK,MAAM;AAAA,EAC5B;AACA,WAAS,KAAK,GAAG,UAAU;AAE3B,SAAO,0BAA0B,QAAQ;AAC5C;AAEO,SAAS,aAAa,MAAyC;AACnE,MAAI,iBAAiB,IAAI,GAAG;AACzB,WAAO;AAAA,EACV;AAEA,UAAQ,OAAO;AAAA,SACP;AAAA,SACA;AACF,aAAO;AAAA;AAGb;AACH;AAEA,SAAS,iBAAiB,MAA0C;AACjE,SAAO,WAAW,SAAS,IAAI;AAClC;AA3CA,IAGY,WAQN;AAXN;AAAA;AAAA;AAAA;AAGO,IAAK,YAAL,kBAAKC,eAAL;AACJ,MAAAA,WAAA,WAAQ;AACR,MAAAA,WAAA,UAAO;AACP,MAAAA,WAAA,UAAO;AACP,MAAAA,WAAA,WAAQ;AACR,MAAAA,WAAA,UAAO;AALE,aAAAA;AAAA,OAAA;AAQZ,IAAM,aAAa,MAAM,KAAK,OAAO,OAAO,SAAS,CAAC;AAAA;AAAA;;;ACXtD,OAAO,WAAyB;AAsBhC,SAAS,YAAY;AAClB,SAAO,MAAM,YAAY;AAC5B;AAUA,SAAS,eACN,IACA,QACA,SACqB;AACrB,MAAI,CAAC,UAAU,CAAC,OAAO,MAAM,EAAE,QAAQ,OAAO,EAAE,GAAG;AAChD,WAAO,CAAC,UACH,KACA,CAAC,YAAY,SAAS;AACnB,SAAG,SAAS,GAAG,IAAI;AACnB,cAAQ,SAAS,GAAG,IAAI;AAAA,IAC3B;AAAA,EACR;AAEA,SAAO,CAAC,YAAY,SAAS;AAC1B,OAAG,MAAM,WAAW,QAAQ,GAAG,IAAI;AACnC,QAAI,SAAS;AACV,cAAQ,SAAS,GAAG,IAAI;AAAA,IAC3B;AAAA,EACH;AACH;AAEA,SAAS,gBACN,MACA,eACA,EAAE,WAAW,gBAAgB,GACtB;AACP,MAAI,OAAO,SAAS,UAAU;AAC3B,WAAO;AAAA,EACV;AACA,QAAM,iBAAkB,iBAAiB,cAAc,aAAc;AAErE,MAAI,eAAe,WAAW,eAAe,GAAG;AAC7C,WAAO,eAAe,OAAO,gBAAgB,SAAS,CAAC;AAAA,EAC1D;AAEA,SAAO,kBAAkB;AAC5B;AAEO,SAAS,aACb,OACA,SACA,aACA,eAAe,UAAU,GACZ;AACb,QAAM,cAAe,SAAS,IAAI,YAAa;AAE/C,QAAM,UAA0B,CAAC;AACjC,QAAM,gBACH,OAAO,YAAY,WAAW,aAAa,OAAO,OAAO,IAAI;AAChE,QAAM,MAAM,gBAAgB,WAAW,SAAS,YAAY,GAAG,eAAe,YAAY;AAE1F,SAAO,KAAK,WAAW;AAEvB,WAAS,QAAQ,MAAc,SAAkB;AAC9C,WAAO;AAAA,MACJ;AAAA,MACA,aAAa,OAAO,IAAI,QAAQ,UAAU,IAAI,GAAG,SAAS,YAAY;AAAA,IACzE;AAAA,EACH;AAEA,WAAS,KAAK,OAAgB;AAC3B,UAAM,aAAc,SAAS,IAAI,YAAa;AAC9C,UAAMC,SAAS,iBAAiB,eAAe,eAAe,UAAU,KAAM;AAC9E,UAAM,OAAO,eAAe,cAAc,GAAG,eAAe,cAAcA,MAAK;AAE/E,WAAO,OAAO,OAAO,gBAAgBA,SAAQ,MAAM;AAAA,MAChD;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACH,CAAC;AAAA,EACJ;AACH;AA3GA;AAAA;AAAA;AACA;AAWA,UAAM,WAAW,IAAI,CAAC,UAAe,OAAO,gBAAgB,KAAK,IAAI,MAAM,SAAS,GAAG;AACvF,UAAM,WAAW,IAAI,CAAC,UAAkB;AACrC,UAAI,OAAO,SAAS,KAAK,GAAG;AACzB,eAAO,MAAM,SAAS,MAAM;AAAA,MAC/B;AACA,aAAO,eAAe,KAAK;AAAA,IAC9B;AAAA;AAAA;;;AClBA,IAYa;AAZb;AAAA;AAAA;AACA;AACA;AAUO,IAAM,qBAAN,MAAwB;AAAA,MAG5B,YAAoB,WAAW,eAAe;AAA1B;AAFpB,aAAQ,SAAgD,oBAAI,IAAI;AAAA,MAEjB;AAAA,MAEvC,aAAa,MAAwB;AAC1C,eAAO,KAAK,OAAO,IAAI,IAAI;AAAA,MAC9B;AAAA,MAEQ,eAAe,MAAwC;AAC5D,cAAM,OAAO,mBAAkB,QAAQ,KAAK,SAAS,EAAE;AACvD,cAAM,SAAS,aAAa,KAAK,UAAU,IAAI;AAE/C,eAAO;AAAA,UACJ;AAAA,UACA;AAAA,UACA;AAAA,QACH;AAAA,MACH;AAAA,MAEA,KAAK,MAAwC;AAC1C,cAAM,WAAW,KAAK,eAAe,IAAI;AACzC,iBAAS,OAAO,2CAA2C,KAAK,QAAQ;AAExE,aAAK,OAAO,IAAI,MAAM,QAAQ;AAE9B,eAAO;AAAA,MACV;AAAA,MAEA,MAAM,KAAe;AAClB,mBAAW,CAAC,MAAM,EAAE,OAAO,CAAC,KAAK,MAAM,KAAK,KAAK,OAAO,QAAQ,CAAC,GAAG;AACjE,cAAI,SAAS,IAAI,MAAM;AACpB,mBAAO,KAAK,aAAa,GAAG;AAC5B;AAAA,cACG;AAAA,YACH;AAAA,UACH,OAAO;AACJ,mBAAO;AAAA,cACJ;AAAA,cACA,IAAI;AAAA,YACP;AAAA,UACH;AAEA,eAAK,SAAS,IAAI;AAAA,QACrB;AAEA,YAAI,KAAK,OAAO,SAAS,GAAG;AACzB,gBAAM,IAAI,MAAM,0CAA0C,KAAK,OAAO,MAAM;AAAA,QAC/E;AAAA,MACH;AAAA,MAEA,SAAS,MAAwB;AAC9B,cAAM,WAAW,KAAK,aAAa,IAAI;AACvC,YAAI,UAAU;AACX,eAAK,OAAO,OAAO,IAAI;AAAA,QAC1B;AAAA,MACH;AAAA,MAEA,QAAQ,MAAwC;AAC7C,cAAM,WAAW,KAAK,aAAa,IAAI;AACvC,YAAI,CAAC,UAAU;AACZ,gBAAM,IAAI,SAAS,QAAW,uDAAuD;AAAA,QACxF;AACA,iBAAS,OAAO,eAAe;AAE/B,eAAO;AAAA,MACV;AAAA,MAEA,OAAO,QAAQ,OAAO,SAAS;AAC5B,eAAO,QAAQ,QAAQ,EAAE,mBAAkB;AAAA,MAC9C;AAAA,IAGH;AAzEO,IAAM,oBAAN;AAwEJ,IAxEU,kBAwEK,UAAU;AAAA;AAAA;;;ACpF5B,SAAS,aAA2B;AAwQpC,SAAS,cAAiB,MAAwB,UAAoB;AACnE,SAAO;AAAA,IACJ,QAAQ,MAAM,KAAK,QAAQ,KAAK;AAAA,IAChC;AAAA,EACH;AACH;AAEA,SAAS,gBAAgB,QAAkB,QAAsB;AAC9D,SAAO,CAAC,QAAe;AACpB,WAAO,sCAAsC,GAAG;AAChD,WAAO,KAAK,OAAO,KAAK,OAAO,IAAI,KAAK,GAAG,OAAO,CAAC;AAAA,EACtD;AACH;AAEA,SAAS,eACN,QACA,MACA,QACA,QACD;AACC,SAAO,CAAC,WAAmB;AACxB,WAAO,wBAAwB,MAAM,MAAM;AAC3C,WAAO,MAAM,MAAM;AACnB,WAAO,KAAK,MAAM;AAAA,EACrB;AACH;AAjSA,IAiBa;AAjBb;AAAA;AAAA;AACA;AAGA;AASA;AAEA;AAEO,IAAM,mBAAN,MAAoD;AAAA,MAqBxD,YACW,WACA,YACA,UACT;AAHS;AACA;AACA;AAvBX,aAAQ,SAAuB,QAAQ,QAAQ;AAC/C,aAAQ,SAAS,IAAI,kBAAkB;AAAA,MAuBpC;AAAA,MApBH,IAAW,MAAM;AACd,eAAO,KAAK,QAAQ,KAAK,UAAU;AAAA,MACtC;AAAA,MAEA,IAAW,IAAI,KAAa;AACzB,aAAK,OAAO;AAAA,MACf;AAAA,MAEA,IAAW,MAAM;AACd,eAAO,KAAK,UAAU;AAAA,MACzB;AAAA,MAEA,IAAW,gBAAgB;AACxB,eAAO,KAAK,UAAU;AAAA,MACzB;AAAA,MAQO,QAAQ;AACZ,eAAO;AAAA,MACV;AAAA,MAEO,KAAQ,MAAoC;AAChD,aAAK,OAAO,KAAK,IAAI;AAErB,eAAQ,KAAK,SAAS,KAAK,OAAO,KAAK,MAAM,KAAK,YAAY,IAAI,CAAC;AAAA,MACtE;AAAA,MAEc,YAAe,MAA2C;AAAA;AACrE,gBAAM,qBAAqB,MAAM,KAAK,WAAW,KAAK;AACtD,gBAAM,kBAAkB,MAAM,KAAK,OAAO,SAAS,IAAI;AAEvD,cAAI;AACD,kBAAM,EAAE,OAAO,IAAI,KAAK,OAAO,QAAQ,IAAI;AAC3C,mBAAQ,MAAO,YAAY,IAAI,IAC1B,KAAK,iBAAiB,MAAM,MAAM,IAClC,KAAK,kBAAkB,MAAM,MAAM;AAAA,UAC3C,SAAS,GAAP;AACC,kBAAM,KAAK,iBAAiB,MAAM,CAAU;AAAA,UAC/C,UAAE;AACC,4BAAgB;AAChB,+BAAmB;AAAA,UACtB;AAAA,QACH;AAAA;AAAA,MAEQ,iBAAoB,MAAwB,GAAU;AAC3D,cAAM,WACH,aAAa,WAAW,OAAO,OAAO,GAAG,EAAE,KAAK,CAAC,IAAI,IAAI,SAAS,MAAM,KAAK,OAAO,CAAC,CAAC;AAEzF,aAAK,SAAS,QAAQ,QAAQ;AAC9B,aAAK,OAAO,MAAM,QAAQ;AAE1B,eAAO;AAAA,MACV;AAAA,MAEc,kBAAqB,MAAuB,QAAsB;AAAA;AAC7E,gBAAM,SAAS,KAAK,SAAS,KAAK,gBAAgB,IAAI,cAAc,MAAM,KAAK,QAAQ,CAAC;AACxF,gBAAM,OAAO,KAAK,SAAS;AAAA,YACxB;AAAA,YACA,CAAC,GAAG,KAAK,QAAQ;AAAA,YACjB,cAAc,MAAM,KAAK,QAAQ;AAAA,UACpC;AAEA,gBAAM,MAAM,MAAM,KAAK;AAAA,YACpB;AAAA,YACA;AAAA,YACA;AAAA,YACA,KAAK;AAAA,YACL,OAAO,KAAK,OAAO;AAAA,UACtB;AACA,gBAAM,gBAAgB,MAAM,KAAK,eAAe,MAAM,MAAM,KAAK,OAAO,KAAK,QAAQ,CAAC;AAEtF,iBAAO,6CAA6C,KAAK,MAAM;AAE/D,cAAI,aAAa,IAAI,GAAG;AACrB,mBAAO,eAAe,KAAK,QAAQ,aAAa;AAAA,UACnD;AAEA,iBAAO,eAAe,KAAK,QAAQ,cAAc,UAAU,CAAC;AAAA,QAC/D;AAAA;AAAA,MAEc,iBAAiB,MAAiB,QAAsB;AAAA;AACnE,iBAAO,6DAA6D;AACpE,iBAAO,KAAK,OAAO,IAAI;AAAA,QAC1B;AAAA;AAAA,MAEQ,eACL,MACA,MACA,QACA,QAC0B;AAC1B,cAAM,EAAE,UAAU,WAAW,QAAQ,OAAO,IAAI;AAEhD,eAAO,IAAI,QAAQ,CAAC,MAAM,SAAS;AAChC,iBAAO,4DAA4D,QAAQ;AAE3E,gBAAM,EAAE,MAAM,IAAI,KAAK,SAAS;AAAA,YAC7B;AAAA,YACA,EAAE,OAAO,UAAU;AAAA,YACnB,kCACM,cAAc,MAAM,IAAI,IACxB;AAAA,UAET;AAEA,cAAI,SAAS,KAAK,SAAS;AACxB,mBAAO,KAAK,gDAAgD;AAE5D,mBAAO,KAAK;AAAA,cACT;AAAA,cACA;AAAA,cACA,CAAC,cAAc;AACZ,uBAAO,KAAK,yCAAyC;AACrD,uBAAO,8BAA8B,eAAe,SAAS,CAAC;AAE9D;AAAA,kBACG,IAAI;AAAA,oBACD,MAAM,QAAQ,SAAS,IAAI,OAAO,OAAO,SAAS,IAAI;AAAA,oBACtD,OAAO,OAAO,MAAM;AAAA,kBACvB;AAAA,gBACH;AAAA,cACH;AAAA,cACA;AAAA,YACH;AAAA,UACH;AAEA,cAAI,OAAO;AACR,mBAAO;AAAA,cACJ;AAAA,cACA;AAAA,cACA,OAAO;AAAA,cACP;AAAA,YACH;AACA,mBAAO,KAAK,KAAK;AAAA,UACpB;AAEA,iBAAO,KAAK,iCAAiC;AAC7C,eAAK,IAAI,iBAAiB,OAAO,OAAO,MAAM,GAAG,OAAO,OAAO,MAAM,CAAC,CAAC;AAAA,QAC1E,CAAC;AAAA,MACJ;AAAA,MAEc,YACX,MACA,SACA,MACA,eACA,QAC2B;AAAA;AAC3B,gBAAM,eAAe,OAAO,QAAQ,QAAQ;AAC5C,gBAAM,eAA6B,KAAK,SAAS;AAAA,YAC9C;AAAA,YACA;AAAA,cACG,KAAK,KAAK;AAAA,cACV,KAAK,KAAK;AAAA,cACV,aAAa;AAAA,YAChB;AAAA,YACA,cAAc,MAAM,KAAK,QAAQ;AAAA,UACpC;AAEA,iBAAO,IAAI,QAAQ,CAAC,SAAS;AAC1B,kBAAM,SAAmB,CAAC;AAC1B,kBAAM,SAAmB,CAAC;AAE1B,mBAAO,KAAK,SAAS,SAAS,IAAI;AAClC,mBAAO,MAAM,YAAY;AAEzB,gBAAI,YAAY,KAAK,aAAa,MAAM,IAAI;AAC5C,gBAAI,WAAW;AACZ,qBAAO,KAAK;AAAA,gBACT;AAAA,gBACA;AAAA,gBACA,UAAU;AAAA,gBACV;AAAA,cACH,CAAC;AAAA,YACJ;AAEA,iBAAK,SAAS,KAAK,gBAAgB,QAAW,iCACxC,cAAc,MAAM,IAAI,IADgB;AAAA,cAE3C,KAAK,QAAQ;AACV,4BAAY,UAAU;AAAA,cACzB;AAAA,YACH,EAAC;AAED,kBAAM,UAAU,MAAM,SAAS,MAAM,YAAY;AAEjD,oBAAQ,OAAQ;AAAA,cACb;AAAA,cACA,eAAe,QAAQ,UAAU,QAAQ,aAAa,KAAK,QAAQ,CAAC;AAAA,YACvE;AACA,oBAAQ,OAAQ;AAAA,cACb;AAAA,cACA,eAAe,QAAQ,UAAU,QAAQ,aAAa,KAAK,QAAQ,CAAC;AAAA,YACvE;AAEA,oBAAQ,GAAG,SAAS,gBAAgB,QAAQ,MAAM,CAAC;AAEnD,gBAAI,eAAe;AAChB,qBAAO,6DAA6D;AACpE,4BAAc,SAAS,QAAQ,QAAS,QAAQ,QAAS,CAAC,GAAG,IAAI,CAAC;AAAA,YACrE;AAEA,iBAAK,SAAS,KAAK,eAAe,QAAW,iCACvC,cAAc,MAAM,IAAI,IADe;AAAA,cAE1C;AAAA,cACA,MAAM,UAAkB,QAAgB;AACrC,qBAAK;AAAA,kBACF;AAAA,kBACA;AAAA,kBACA;AAAA,kBACA,WAAW,aAAa;AAAA,gBAC3B,CAAC;AAAA,cACJ;AAAA,cACA,KAAK,QAAe;AACjB,oBAAI,QAAQ,QAAQ;AACjB;AAAA,gBACH;AAEA,4BAAY;AACZ,wBAAQ,KAAK,QAAQ;AAAA,cACxB;AAAA,YACH,EAAC;AAAA,UACJ,CAAC;AAAA,QACJ;AAAA;AAAA,MAEQ,aAAgB,MAAwB,MAAgB;AAC7D,YAAI;AACJ,aAAK,SAAS,KAAK,gBAAgB,QAAW,iCACxC,cAAc,MAAM,IAAI,IADgB;AAAA,UAE3C,KAAK,QAAQ;AACV,wBAAY,UAAU;AAAA,UACzB;AAAA,QACH,EAAC;AAED,eAAO;AAAA,MACV;AAAA,IACH;AAAA;AAAA;;;ACtQA;AAAA;AAAA;AAAA;AAAA,IAMa;AANb;AAAA;AAAA;AAGA;AAGO,IAAM,cAAN,MAA+C;AAAA,MAMnD,YACU,KACC,YACA,UACT;AAHQ;AACC;AACA;AARX,aAAQ,SAAS,IAAI,iBAAiB,MAAM,KAAK,YAAY,KAAK,QAAQ;AAAA,MASvE;AAAA,MAEH,QAA2B;AACxB,eAAO,IAAI,iBAAiB,MAAM,KAAK,YAAY,KAAK,QAAQ;AAAA,MACnE;AAAA,MAEA,KAAQ,MAAoC;AACzC,eAAO,KAAK,OAAO,KAAK,IAAI;AAAA,MAC/B;AAAA,IACH;AAAA;AAAA;;;ACpBO,SAAS,aACb,MACA,UACA,WAAqC,MACtC;AACC,QAAM,YAAY,CAAC,SAAY;AAC5B,aAAS,MAAM,IAAI;AAAA,EACtB;AAEA,QAAMC,WAAU,CAAC,QAAqC;AACnD,SAAI,2BAAK,UAAS,MAAM;AACrB;AAAA,QACG,eAAe,mBAAmB,4BAA4B,GAAG,IAAI;AAAA,QACrE;AAAA,MACH;AAAA,IACH;AAAA,EACH;AAEA,WAAS,KAAK,WAAWA,QAAO;AACnC;AAEA,SAAS,4BAA4B,KAAuB;AACzD,MAAI,MAAM,CAAC,SAAiB;AACzB,YAAQ;AAAA,MACL,6DAA6D,uCAAuC;AAAA,IACvG;AACA,UAAM;AAAA,EACT;AAEA,SAAO,OAAO,OAAO,KAAK,OAAO,oBAAoB,IAAI,GAAG,EAAE,OAAO,mBAAmB,CAAC,CAAC,CAAC;AAE3F,WAAS,kBAAkB,KAA4B,MAA0B;AAC9E,QAAI,QAAQ,KAAK;AACd,aAAO;AAAA,IACV;AAEA,QAAI,QAAQ;AAAA,MACT,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,MAAM;AACH,YAAI,IAAI;AACR,eAAO,IAAI,IAAI;AAAA,MAClB;AAAA,IACH;AAEA,WAAO;AAAA,EACV;AACH;AApDA;AAAA;AAAA;AACA;AAEA;AAAA;AAAA;;;ACCO,SAAS,2BAA2B,WAAmB,MAA0B;AACrF,SAAO,cAAc,CAAC,aAAgC;AACnD,QAAI,CAAC,aAAa,SAAS,GAAG;AAC3B,YAAM,IAAI,MAAM,4CAA4C,YAAY;AAAA,IAC3E;AAEA,YAAS,QAAQ,UAAU,MAAM;AAAA,EACpC,CAAC;AACJ;AAZA;AAAA;AAAA;AAAA;AAEA;AAAA;AAAA;;;ACGA,SAAS,aAAa,MAAgB;AACnC,QAAM,WAAW,CAAC,YAAY,GAAG,IAAI;AACrC,MAAI,SAAS,OAAO,QAAQ,SAAS,SAAS,IAAI,GAAG;AAClD,aAAS,KAAK,OAAO,UAAU,IAAI;AAAA,EACtC;AAEA,SAAO,0BAA0B,QAAQ;AAC5C;AAEe,SAAR,mBAA4F;AAChG,SAAO;AAAA,IACJ,WAA6B;AAC1B,aAAO,KAAK;AAAA,QACT,aAAa,mBAAmB,WAAW,CAAC,CAAC;AAAA,QAC7C,yBAAyB,SAAS;AAAA,MACrC;AAAA,IACH;AAAA,IAEA,eAAmC,YAAY,YAAY;AACxD,aAAO,KAAK;AAAA,QACT,aAAa,CAAC,MAAM,YAAY,YAAY,GAAG,mBAAmB,SAAS,CAAC,CAAC;AAAA,QAC7E,yBAAyB,SAAS;AAAA,MACrC;AAAA,IACH;AAAA,IAEA,oBAAwC,YAAY;AACjD,aAAO,KAAK;AAAA,QACT,aAAa,CAAC,MAAM,YAAY,GAAG,mBAAmB,SAAS,CAAC,CAAC;AAAA,QACjE,yBAAyB,SAAS;AAAA,MACrC;AAAA,IACH;AAAA,EACH;AACH;AArCA;AAAA;AAAA;AAEA;AACA;AAAA;AAAA;;;ACYA,SAAS,uBAA2C;AACjD,SAAO;AAAA,IACJ,OAAO;AAAA,IACP,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,eAAe;AAAA,IACf,MAAM;AAAA,IACN,aAAa;AAAA,IACb,UAAU;AAAA,EACb;AACH;AAYe,SAAR,wBAAqD;AACzD,SAAO;AAAA,IACJ,eAAiC;AAC9B,aAAO,KAAK,SAAS;AAAA,QAClB,UAAU,CAAC,iBAAiB,WAAW;AAAA,QACvC,QAAQ;AAAA,QACR,OAAO,QAAgB;AACpB,iBAAO,oBAAoB,qBAAqB,GAAG,CAACC,OAAM,GAAG,MAAM;AAAA,QACtE;AAAA,MACH,CAAC;AAAA,IACJ;AAAA,EACH;AACH;AAlDA,IA4BMA;AA5BN;AAAA;AAAA;AAEA;AA0BA,IAAMA,UAAyC,IAAI;AAAA,MAChD;AAAA,MACA,CAAC,QAAQ,CAAC,KAAK,KAAK,MAAM;AACvB,cAAM,WAAW,YAAY,GAAG;AAChC,YAAI,OAAO,eAAe,QAAQ,GAAG;AAClC,iBAAO,YAAmC,SAAS,KAAK;AAAA,QAC3D;AAAA,MACH;AAAA,IACH;AAAA;AAAA;;;ACQO,SAAS,kBAAkB,QAA8B;AAC7D,QAAM,SAAuB;AAAA,IAC1B,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,MAAM;AAAA,IACN,SAAS;AAAA,MACN,SAAS;AAAA,MACT,YAAY;AAAA,MACZ,WAAW;AAAA,IACd;AAAA,EACH;AACA,SAAO,oBAAoB,QAAQ,SAAS,MAAM;AACrD;AAzDA,IAGM;AAHN;AAAA;AAAA;AACA;AAEA,IAAM,UAAsC;AAAA,MACzC,IAAI,WAAW,qCAAqC,CAAC,QAAQ,CAAC,QAAQ,MAAM,MAAM,MAAM;AACrF,eAAO,SAAS;AAChB,eAAO,SAAS;AAChB,eAAO,OAAO,CAAC,CAAC;AAAA,MACnB,CAAC;AAAA,MACD,IAAI,WAAW,qBAAqB,CAAC,QAAQ,CAAC,MAAM,MAAM;AACvD,cAAM,QAAQ,OAAO,MAAM,GAAG;AAC9B,cAAM,QAAQ,MAAM,IAAI;AAExB,YAAI,CAAC,SAAS,CAAC,MAAM,SAAS,GAAG,GAAG;AACjC;AAAA,QACH;AAEA,eAAO,SAAS;AAAA,UACb,OAAO,MAAM,OAAO,GAAG,MAAM,SAAS,CAAC;AAAA,UACvC,MAAM,MAAM,KAAK,GAAG,EAAE,KAAK;AAAA,QAC9B;AAAA,MACH,CAAC;AAAA,MACD,IAAI;AAAA,QACD;AAAA,QACA,CAAC,QAAQ,CAAC,SAAS,YAAY,SAAS,MAAM;AAC3C,iBAAO,QAAQ,UAAU,SAAS,SAAS,EAAE,KAAK;AAClD,iBAAO,QAAQ,aAAa,SAAS,YAAY,EAAE,KAAK;AACxD,iBAAO,QAAQ,YAAY,SAAS,WAAW,EAAE,KAAK;AAAA,QACzD;AAAA,MACH;AAAA,MACA,IAAI;AAAA,QACD;AAAA,QACA,CAAC,QAAQ,CAAC,SAAS,OAAO,SAAS,MAAM;AACtC,iBAAO,QAAQ,UAAU,SAAS,SAAS,EAAE,KAAK;AAClD,gBAAM,QAAQ,SAAS,OAAO,EAAE,KAAK;AACrC,cAAI,cAAc,KAAK;AACpB,mBAAO,QAAQ,YAAY;AAAA,UAC9B,WAAW,cAAc,KAAK;AAC3B,mBAAO,QAAQ,aAAa;AAAA,UAC/B;AAAA,QACH;AAAA,MACH;AAAA,IACH;AAAA;AAAA;;;AC3BO,SAAS,WACb,SACA,OACA,YACyB;AACzB,QAAM,WAAqB;AAAA,IACxB;AAAA,IACA;AAAA,IACA;AAAA,IACA,GAAG,cAAc,SAAS,IAAI;AAAA,IAC9B,GAAG;AAAA,IACH,GAAG;AAAA,EACN;AAEA,SAAO;AAAA,IACJ;AAAA,IACA,QAAQ;AAAA,IACR,QAAQ;AAAA,EACX;AACH;AAEe,SAAR,iBAA+C;AACnD,SAAO;AAAA,IACJ,OAA2B,YAA+B,MAAiB;AACxE,YAAM,OAAO,yBAAyB,SAAS;AAC/C,YAAM,OACH,2BAA2B,OAAO,KAClC;AAAA,QACG,QAAQ,OAAO;AAAA,QACf,QAAQ,WAAW,KAAK,IAAI,2BAA2B,CAAC,CAAC,CAAC;AAAA,QAC1D,CAAC,GAAG,WAAW,KAAK,IAAI,aAAa,CAAC,CAAC,GAAG,GAAG,mBAAmB,WAAW,GAAG,IAAI,CAAC;AAAA,MACtF;AAEH,aAAO,KAAK,SAAS,MAAM,IAAI;AAAA,IAClC;AAAA,EACH;AAEA,WAAS,2BAA2B,SAAmB;AACpD,WACG,CAAC,0BAA0B,OAAO,KAClC;AAAA,MACG;AAAA,IACH;AAAA,EAEN;AACH;AA5DA;AAAA;AAAA;AAGA;AACA;AASA;AAAA;AAAA;;;ACRe,SAAR,uBAAoD;AACxD,SAAO;AAAA,IACJ,cAAkD;AAC/C,aAAO,KAAK;AAAA,QACT,0BAA0B,CAAC,YAAY,mBAAmB,MAAM,GAAG,IAAI;AAAA,QACvE,yBAAyB,SAAS;AAAA,MACrC;AAAA,IACH;AAAA,EACH;AACH;AAdA;AAAA;AAAA;AAEA;AACA;AAAA;AAAA;;;ACGO,SAAS,eAAe,UAAkB,OAAoC;AAClF,QAAM,WAAW,CAAC,eAAe,QAAQ;AACzC,MAAI,OAAO;AACR,aAAS,KAAK,IAAI;AAAA,EACrB;AAEA,SAAO,0BAA0B,UAAU,IAAI;AAClD;AAbA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACcO,SAAS,UAAU,MAAe,MAAc,MAAc;AAClE,QAAM,WAAW,OAAO,IAAI,EAAE,KAAK;AACnC,MAAI;AAEJ,MAAK,SAAS,kBAAkB,KAAK,QAAQ,GAAI;AAC9C,WAAO,IAAI,YAAY,MAAM,MAAM,OAAO,OAAO,EAAE;AAAA,EACtD;AAEA,MAAK,SAAS,oBAAoB,KAAK,QAAQ,GAAI;AAChD,WAAO,IAAI,YAAY,MAAM,MAAM,MAAM,OAAO,EAAE;AAAA,EACrD;AAEA,MAAI,SAAS;AACb,QAAM,SAAS,SAAS,MAAM,GAAG;AACjC,SAAO,OAAO,QAAQ;AACnB,UAAM,QAAQ,OAAO,MAAM;AAC3B,QAAI,UAAU,MAAM;AACjB,eAAS,OAAO,KAAK,GAAG;AACxB;AAAA,IACH;AAAA,EACH;AAEA,SAAO,IAAI,YAAY,MAAM,MAAM,OAAO,KAAK,QAAQ,GAAG,MAAM;AACnE;AArCA,IAEa,aASP,mBACA;AAZN;AAAA;AAAA;AAEO,IAAM,cAAN,MAAwC;AAAA,MAC5C,YACmB,MACA,MACA,UACA,QACjB;AAJiB;AACA;AACA;AACA;AAAA,MAChB;AAAA,IACN;AAEA,IAAM,oBAAoB;AAC1B,IAAM,sBAAsB;AAAA;AAAA;;;ACN5B,SAAS,eAAe,SAAmB;AACxC,SAAO,QAAQ,SAAS,WAAW;AACtC;AAEO,SAAS,SAAS,OAAO,OAAO,MAAc,YAA8C;AAChG,QAAM,WAAW,CAAC,QAAQ,GAAG,UAAU;AACvC,MAAI,QAAQ,CAAC,eAAe,QAAQ,GAAG;AACpC,aAAS,OAAO,GAAG,GAAG,WAAW;AAAA,EACpC;AAEA,SAAO;AAAA,IACJ;AAAA,IACA,QAAQ;AAAA,IACR,OAAO,MAA0B;AAC9B,aAAO,UAAU,SAAS,SAAS,QAAQ,GAAG,MAAM,IAAI;AAAA,IAC3D;AAAA,EACH;AACH;AAvBA,IAIM;AAJN;AAAA;AAAA;AACA;AAGA,IAAM,cAAc;AAAA;AAAA;;;ACMb,SAAS,qBAAqB,YAAsB;AACxD,WAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AACzC,UAAM,SAAS,eAAe,KAAK,WAAW,EAAE;AAChD,QAAI,QAAQ;AACT,aAAO,KAAK,OAAO;AAAA,IACtB;AAAA,EACH;AAEA,SAAO;AACV;AAEO,SAAS,YAAY,WAA6B;AACtD,SAAO,eAAe,KAAK,SAAmB;AACjD;AAvBA,IAQM;AARN;AAAA;AAAA;AAQA,IAAM,iBAAiB;AAAA;AAAA;;;ACRvB,IAKa;AALb;AAAA;AAAA;AAKO,IAAM,cAAN,MAAwC;AAAA,MAAxC;AACJ,uBAAU;AACV,yBAAY;AACZ,0BAAa;AAEb,qBAA0D,CAAC;AAAA;AAAA,IAC9D;AAAA;AAAA;;;ACwGO,SAAS,cAAc,wBAAyB;AACpD,QAAMC,UAAS,mBAAmB;AAElC,SAAO,CAAC,WAAmB,oBAAoB,IAAI,YAAY,GAAGA,SAAQ,QAAQ,KAAK;AAC1F;AAvHA,IAMM,YAqCA,eAgCA,gBAaA,kBAmBA;AA3GN;AAAA;AAAA;AACA;AACA;AACA;AACA;AAEA,IAAM,aAAa;AAAA,MAChB,IAAI;AAAA,QACD;AAAA,QACA,CAAC,QAAQ,CAAC,MAAM,SAAS,cAAc,EAAE,MAAM;AAC5C,iBAAO,MAAM,KAAK;AAAA,YACf,MAAM,KAAK,KAAK;AAAA,YAChB,SAAS,SAAS,OAAO;AAAA,YACzB,YAAY,YAAY,QAAQ,SAAS,EAAE,EAAE;AAAA,YAC7C,WAAW,YAAY,QAAQ,SAAS,EAAE,EAAE;AAAA,YAC5C,QAAQ;AAAA,UACX,CAAC;AAAA,QACJ;AAAA,MACH;AAAA,MACA,IAAI;AAAA,QACD;AAAA,QACA,CAAC,QAAQ,CAAC,MAAM,QAAQ,KAAK,MAAM;AAChC,iBAAO,MAAM,KAAK;AAAA,YACf,MAAM,KAAK,KAAK;AAAA,YAChB,QAAQ,SAAS,MAAM;AAAA,YACvB,OAAO,SAAS,KAAK;AAAA,YACrB,QAAQ;AAAA,UACX,CAAC;AAAA,QACJ;AAAA,MACH;AAAA,MACA,IAAI;AAAA,QACD;AAAA,QACA,CAAC,QAAQ,CAAC,SAAS,OAAO,MAAM;AAC7B,gBAAM,WAAW,UAAU,KAAK,OAAO;AACvC,gBAAM,UAAU,UAAU,KAAK,OAAO;AAEtC,iBAAO,UAAU,SAAS,OAAO;AACjC,iBAAO,aAAa,SAAS,qCAAW,EAAE;AAC1C,iBAAO,YAAY,SAAS,mCAAU,EAAE;AAAA,QAC3C;AAAA,MACH;AAAA,IACH;AAEA,IAAM,gBAAgB;AAAA,MACnB,IAAI;AAAA,QACD;AAAA,QACA,CAAC,QAAQ,CAAC,eAAe,eAAe,IAAI,MAAM;AAC/C,gBAAM,aAAa,SAAS,aAAa;AACzC,gBAAM,YAAY,SAAS,aAAa;AAExC,iBAAO;AACP,iBAAO,cAAc;AACrB,iBAAO,aAAa;AAEpB,iBAAO,MAAM,KAAK;AAAA,YACf;AAAA,YACA,SAAS,aAAa;AAAA,YACtB;AAAA,YACA;AAAA,YACA,QAAQ;AAAA,UACX,CAAC;AAAA,QACJ;AAAA,MACH;AAAA,MACA,IAAI,WAAuB,eAAe,CAAC,QAAQ,CAAC,IAAI,MAAM;AAC3D,eAAO;AAEP,eAAO,MAAM,KAAK;AAAA,UACf;AAAA,UACA,OAAO;AAAA,UACP,QAAQ;AAAA,UACR,QAAQ;AAAA,QACX,CAAC;AAAA,MACJ,CAAC;AAAA,IACJ;AAEA,IAAM,iBAAiB;AAAA,MACpB,IAAI,WAAuB,SAAS,CAAC,QAAQ,CAAC,IAAI,MAAM;AACrD,eAAO;AACP,eAAO,MAAM,KAAK;AAAA,UACf;AAAA,UACA,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,WAAW;AAAA,UACX,QAAQ;AAAA,QACX,CAAC;AAAA,MACJ,CAAC;AAAA,IACJ;AAEA,IAAM,mBAAmB;AAAA,MACtB,IAAI;AAAA,QACD;AAAA,QACA,CAAC,QAAQ,CAAC,QAAQ,YAAY,MAAM,KAAK,EAAE,MAAM;AAC9C,iBAAO;AACP,iBAAO,MAAM,KAAK;AAAA,YACf,MAAM,kBAAM;AAAA,YACZ,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,WAAW;AAAA,YACX,QAAQ;AAAA,YACR,QAAQ,OAAO,iBAAiB,MAAM,KAAK,MAAM;AAAA,YACjD,MAAM,OAAO,CAAC,CAAC,MAAM,SAAS,MAAM,IAAI;AAAA,YACxC,YAAY,SAAS,UAAU;AAAA,UAClC,CAAC;AAAA,QACJ;AAAA,MACH;AAAA,IACH;AAEA,IAAM,qBAAkE;AAAA,MACrE,iBAAkB;AAAA,MAClB,uBAAkB;AAAA,MAClB,8BAAsB;AAAA,MACtB,qCAAyB;AAAA,MACzB,iCAAuB;AAAA,IAC1B;AAAA;AAAA;;;ACpGA,SAAS,YAAY,QAAkB,QAAuB;AAC3D,SAAO,OAAO;AAAA,IACX,CAAC,MAAM,OAAO,UAAU;AACrB,WAAK,SAAS,OAAO,UAAU;AAC/B,aAAO;AAAA,IACV;AAAA,IACA,uBAAO,OAAO,EAAE,MAAM,KAAK,CAAC;AAAA,EAC/B;AACH;AAEO,SAAS,2BACb,WAAW,UACX,SAAS,mBACT,2BACD;AACC,QAAM,kBAAkB,cAAc,SAAS;AAE/C,SAAO,SAAU,QAA8B;AAC5C,UAAM,MAAsC;AAAA,MACzC,OAAO,KAAK;AAAA,MACZ;AAAA,MACA;AAAA,IACH,EAAE,IAAI,SAAU,MAAM;AACnB,YAAM,aAAa,KAAK,MAAM,eAAe;AAC7C,YAAM,cAA+B,YAAY,WAAW,GAAG,MAAM,QAAQ,GAAG,MAAM;AAEtF,UAAI,WAAW,SAAS,KAAK,CAAC,CAAC,WAAW,GAAG,KAAK,GAAG;AAClD,oBAAY,OAAO,gBAAgB,WAAW,EAAE;AAAA,MACnD;AAEA,aAAO;AAAA,IACV,CAAC;AAED,WAAO;AAAA,MACJ;AAAA,MACA,QAAS,IAAI,UAAU,IAAI,MAAO;AAAA,MAClC,OAAO,IAAI;AAAA,IACd;AAAA,EACH;AACH;AApDA,IAKa,gBAEA,iBAEA,UAEP;AAXN;AAAA;AAAA;AACA;AACA;AACA;AAEO,IAAM,iBAAiB;AAEvB,IAAM,kBAAkB;AAExB,IAAM,WAAW;AAExB,IAAM,oBAAoB,CAAC,QAAQ,QAAQ,WAAW,QAAQ,eAAe,cAAc;AAAA;AAAA;;;ACX3F;AAAA;AAAA;AAAA;AAAA;AAMO,SAAS,gBAAgB,YAA0D;AACvF,MAAI,YAAY,qBAAqB,UAAU;AAE/C,QAAM,WAAW,CAAC,MAAM;AAExB,MAAI,6BAA8B;AAC/B;AACA,aAAS,KAAK,aAAa;AAAA,EAC9B;AAEA,WAAS,KAAK,GAAG,UAAU;AAE3B,SACG,wBAAwB,QAAQ,KAAK;AAAA,IAClC;AAAA,IACA,QAAQ;AAAA,IACR,QAAQ,cAAc,SAAS;AAAA,EAClC;AAEN;AAEO,SAAS,wBAAwB,YAAyC;AAC9E,QAAM,QAAQ,WAAW,OAAO,WAAW;AAE3C,MAAI,MAAM,SAAS,GAAG;AACnB,WAAO;AAAA,MACJ,sDAAsD,MAAM,KAAK,GAAG;AAAA,IACvE;AAAA,EACH;AAEA,MAAI,MAAM,UAAU,WAAW,SAAS,IAAI,GAAG;AAC5C,WAAO;AAAA,MACJ,gBAAgB;AAAA,IACnB;AAAA,EACH;AACH;AAzCA;AAAA;AAAA;AAEA;AACA;AACA;AAAA;AAAA;;;ACgEA,SAAS,aACN,QACA,UACmB;AACnB,QAAM,SAAmB,CAAC;AAC1B,QAAM,YAAsB,CAAC;AAE7B,SAAO,KAAK,MAAM,EAAE,QAAQ,CAAC,UAAU;AACpC,WAAO,KAAK,KAAK;AACjB,cAAU,KAAK,OAAO,OAAO,MAAM,CAAC;AAAA,EACvC,CAAC;AAED,SAAO,CAAC,QAAQ,UAAU,KAAK,QAAQ,CAAC;AAC3C;AAEA,SAAS,YAA+B,OAAmB;AACxD,SAAO,OAAO,KAAK,KAAK,EAAE,OAAO,CAAC,KAAK,QAAQ;AAC5C,QAAI,EAAE,OAAO,iBAAiB;AAC3B,UAAI,OAAO,MAAM;AAAA,IACpB;AACA,WAAO;AAAA,EACV,GAAG,CAAC,CAAY;AACnB;AAEO,SAAS,gBACb,MAA+B,CAAC,GAChC,aAAuB,CAAC,GACP;AACjB,QAAM,WAAW,WAAW,IAAI,UAAU,cAAc,QAAQ;AAChE,QAAM,SACH,CAAC,iBAAiB,IAAI,MAAM,KAAK,IAAI,SAChC,IAAI,SACJ;AAAA,IACG,MAAM;AAAA,IACN,MAAM,IAAI,eAAe,QAAQ,QAAQ;AAAA,IACzC,SAAS;AAAA,IACT,MAAM;AAAA,IACN,MAAM,IAAI,YAAY,OAAO;AAAA,IAC7B,aAAa,IAAI,YAAY,QAAQ,QAAQ;AAAA,IAC7C,cAAc,IAAI,YAAY,QAAQ,QAAQ;AAAA,EACjD;AAER,QAAM,CAAC,QAAQ,SAAS,IAAI,aAAa,QAAQ,QAAQ;AAEzD,QAAM,SAAmB,CAAC;AAC1B,QAAM,UAAoB;AAAA,IACvB,mBAAmB,iBAAiB,YAAY;AAAA,IAChD,GAAG;AAAA,EACN;AAEA,QAAM,WAAgC,IAAY,KAAM,IAAY,gBAAgB,IAAI;AACxF,MAAI,UAAU;AACX,YAAQ,KAAK,eAAe,UAAU;AAAA,EACzC;AAEA,MAAI,IAAI,QAAQ,IAAI,IAAI;AACrB,UAAM,gBAAgB,IAAI,cAAc,QAAQ,QAAQ;AACxD,WAAO,KAAK,GAAG,IAAI,QAAQ,KAAK,gBAAgB,IAAI,MAAM,IAAI;AAAA,EACjE;AAEA,MAAI,aAAa,IAAI,IAAI,GAAG;AACzB,YAAQ,KAAK,YAAY,SAAS,IAAI,IAAI,CAAC;AAAA,EAC9C;AAEA,oBAAkB,YAAY,GAAc,GAAG,OAAO;AAEtD,SAAO;AAAA,IACJ;AAAA,IACA;AAAA,IACA,UAAU,CAAC,GAAG,SAAS,GAAG,MAAM;AAAA,EACnC;AACH;AAEO,SAAS,QACb,UACA,QACA,YACyB;AACzB,QAAMC,UAAS,2BAA2B,UAAU,QAAQ,qBAAqB,UAAU,CAAC;AAE5F,SAAO;AAAA,IACJ,UAAU,CAAC,OAAO,GAAG,UAAU;AAAA,IAC/B,QAAQ;AAAA,IACR,QAAAA;AAAA,EACH;AACH;AAEe,SAAR,cAA4C;AAChD,SAAO;AAAA,IACJ,OAA8C,MAAiB;AAC5D,YAAM,OAAO,yBAAyB,SAAS;AAC/C,YAAM,UAAU;AAAA,QACb,wBAAwB,SAAS;AAAA,QACjC,WAAW,UAAU,IAAI,WAAW;AAAA,MACvC;AACA,YAAM,OACH,2BAA2B,GAAG,IAAI,KAClC,wBAAwB,QAAQ,QAAQ,KACxC,cAAc,OAAO;AAExB,aAAO,KAAK,SAAS,MAAM,IAAI;AAAA,IAClC;AAAA,EACH;AAEA,WAAS,cAAc,SAA2B;AAC/C,WAAO,QAAQ,QAAQ,UAAU,QAAQ,QAAQ,QAAQ,QAAQ;AAAA,EACpE;AAEA,WAAS,2BAA2B,MAAgB,IAAc;AAC/D,WACG,aAAa,IAAI,KACjB,aAAa,EAAE,KACf;AAAA,MACG;AAAA,IACH;AAAA,EAEN;AACH;AAzLA,IAuBK;AAvBL;AAAA;AAAA;AAEA;AACA;AACA;AAMA;AAUA;AACA;AAEA,IAAK,iBAAL,kBAAKC,oBAAL;AACG,MAAAA,gCAAA;AACA,MAAAA,gCAAA;AACA,MAAAA,gCAAA;AACA,MAAAA,gCAAA;AACA,MAAAA,gCAAA;AACA,MAAAA,gCAAA;AACA,MAAAA,gCAAA;AACA,MAAAA,gCAAA;AACA,MAAAA,gCAAA;AACA,MAAAA,gCAAA;AACA,MAAAA,gCAAA;AACA,MAAAA,gCAAA;AACA,MAAAA,gCAAA;AAbE,aAAAA;AAAA,OAAA;AAAA;AAAA;;;ACvBL,IAOa,sBAYA;AAnBb;AAAA;AAAA;AAOO,IAAM,uBAAN,MAAoD;AAAA,MACxD,YACmB,QACA,OAAsB,MACtB,MACjB;AAHiB;AACA;AACA;AAAA,MAChB;AAAA,MAEH,WAAW;AACR,eAAO,GAAG,KAAK,QAAQ,KAAK;AAAA,MAC/B;AAAA,IACH;AAEO,IAAM,qBAAN,MAAgD;AAAA,MAAhD;AACJ,aAAO,YAA6B,CAAC;AACrC,aAAO,SAAmB,CAAC;AAC3B,aAAO,SAA4B;AAAA;AAAA,MAEnC,IAAI,SAAS;AACV,eAAO,KAAK,UAAU,SAAS;AAAA,MAClC;AAAA,MAEA,IAAI,SAAS;AACV,eAAO,KAAK;AAAA,MACf;AAAA,MAEA,WAAW;AACR,YAAI,KAAK,UAAU,QAAQ;AACxB,iBAAO,cAAc,KAAK,UAAU,KAAK,IAAI;AAAA,QAChD;AAEA,eAAO;AAAA,MACV;AAAA,IACH;AAAA;AAAA;;;ACvCA,IAOa,aAgBA;AAvBb;AAAA;AAAA;AAOO,IAAM,cAAN,MAAwC;AAAA,MAAxC;AACJ,aAAO,iBAAiB;AAAA,UACrB,KAAK,CAAC;AAAA,QACT;AACA,aAAO,UAAU,CAAC;AAClB,aAAO,UAAoB,CAAC;AAC5B,aAAO,QAAkB,CAAC;AAC1B,aAAO,YAAmC,CAAC;AAC3C,aAAO,aAAoC,CAAC;AAC5C,aAAO,UAA6B;AAAA,UACjC,SAAS;AAAA,UACT,WAAW;AAAA,UACX,YAAY;AAAA,QACf;AAAA;AAAA,IACH;AAEO,IAAM,oBAAN,MAAoD;AAAA,MAApD;AACJ,sBAAS;AACT,oBAAO;AAAA,UACJ,OAAO;AAAA,UACP,QAAQ;AAAA,QACX;AACA,sBAAS;AAAA,UACN,OAAO;AAAA,UACP,QAAQ;AAAA,QACX;AACA,uBAAU;AAAA;AAAA,MAEV,WAAW;AACR,eAAO,KAAK;AAAA,MACf;AAAA,IACH;AAAA;AAAA;;;AC/BA,SAAS,wBACN,gBACgC;AAChC,SAAQ,eAAe,UAAU,eAAe,WAAW;AAAA,IACxD,aAAa;AAAA,IACb,UAAU;AAAA,IACV,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,QAAQ,EAAE,OAAO,GAAG,OAAO,EAAE;AAAA,IAC7B,OAAO,EAAE,OAAO,GAAG,OAAO,EAAE;AAAA,EAC/B;AACH;AAEA,SAAS,cAAc,QAAgB;AACpC,QAAM,QAAQ,YAAY,KAAK,MAAM;AACrC,QAAM,QAAQ,eAAe,KAAK,MAAM;AAExC,SAAO;AAAA,IACJ,OAAO,SAAU,SAAS,MAAM,MAAO,GAAG;AAAA,IAC1C,OAAO,SAAU,SAAS,MAAM,MAAO,GAAG;AAAA,EAC7C;AACH;AA5BA,IA8Ba;AA9Bb;AAAA;AAAA;AAKA;AAyBO,IAAM,8BACV;AAAA,MACG,IAAI;AAAA,QACD;AAAA,QACA,CAAC,QAAQ,CAAC,QAAQ,KAAK,MAAM;AAC1B,gBAAM,MAAM,OAAO,YAAY;AAC/B,gBAAM,cAAc,wBAAwB,OAAO,cAAc;AAEjE,iBAAO,OAAO,aAAa,EAAE,CAAC,MAAM,SAAS,KAAK,EAAE,CAAC;AAAA,QACxD;AAAA,MACH;AAAA,MACA,IAAI;AAAA,QACD;AAAA,QACA,CAAC,QAAQ,CAAC,QAAQ,KAAK,MAAM;AAC1B,gBAAM,MAAM,OAAO,YAAY;AAC/B,gBAAM,cAAc,wBAAwB,OAAO,cAAc;AAEjE,iBAAO,OAAO,aAAa,EAAE,CAAC,MAAM,SAAS,KAAK,EAAE,CAAC;AAAA,QACxD;AAAA,MACH;AAAA,MACA,IAAI;AAAA,QACD;AAAA,QACA,CAAC,QAAQ,CAAC,OAAO,QAAQ,UAAU,MAAM;AACtC,gBAAM,UAAU,wBAAwB,OAAO,cAAc;AAC7D,kBAAQ,QAAQ,cAAc,KAAK;AACnC,kBAAQ,SAAS,cAAc,MAAM;AACrC,kBAAQ,aAAa,SAAS,UAAU;AAAA,QAC3C;AAAA,MACH;AAAA,IACH;AAAA;AAAA;;;AC9BI,SAAS,oBACb,SACA,QACoB;AACpB,SAAO,oBAAoB,EAAE,gBAAgB,IAAI,qBAAqB,EAAO,GAAGC,UAAS,MAAM;AAClG;AAlCA,IAIMA,UAgCO;AApCb;AAAA;AAAA;AACA;AACA;AAEA,IAAMA,WACH;AAAA,MACG,IAAI,iBAAiB,oBAAoB,CAAC,QAAQ,CAAC,IAAI,MAAM;AAC1D,eAAO,eAAe,IAAI,KAAK,KAAK,KAAK,CAAC;AAC1C,eAAO;AAAA,MACV,CAAC;AAAA,MACD,GAAG;AAAA,MACH,IAAI;AAAA,QACD,CAAC,oCAAoC,qBAAqB;AAAA,QAC1D,CAAC,QAAQ,CAAC,cAAc,MAAM;AAC3B,UAAC,OAAO,eAA4C,iBAAiB;AAAA,QACxE;AAAA,MACH;AAAA,MACA,IAAI;AAAA,QACD,CAAC,6CAA6C,qBAAqB;AAAA,QACnE,CAAC,QAAQ,CAAC,OAAO,SAAS,GAAG,MAAM;AAChC,UAAC,OAAO,eAA4C,kBAAkB;AAAA,YACnE,OAAO,SAAS,KAAK;AAAA,YACrB;AAAA,YACA;AAAA,UACH;AAAA,QACH;AAAA,MACH;AAAA,IACH;AASI,IAAM,uBAAN,MAAqD;AAAA,MAArD;AACJ,aAAgB,MAAgB,CAAC;AAAA;AAAA,IACpC;AAAA;AAAA;;;ACyBO,SAAS,qBAAqB,QAAgB,QAAgB;AAClE,QAAM,YAAY,oBAAoB,IAAI,kBAAkB,GAAG,cAAc,CAAC,QAAQ,MAAM,CAAC;AAE7F,SAAO,UAAU,WAAW;AAC/B;AAnEA,IAMM,mBACA,eACA,cAEAC,UA2BA,cAcO,iBAIA;AAvDb;AAAA;AAAA;AACA;AAEA;AACA;AAEA,IAAM,oBAAoB;AAC1B,IAAM,gBAAgB;AACtB,IAAM,eAAe;AAErB,IAAMA,WAAoC;AAAA,MACvC,IAAI,WAAW,mBAAmB,CAAC,QAAQ,CAAC,MAAM,YAAY,SAAS,MAAM;AAC1E,eAAO,MAAM,KAAK,IAAI;AAEtB,YAAI,YAAY;AACb,iBAAO,WAAW,QAAQ,WAAW;AAAA,QACxC;AAEA,YAAI,WAAW;AACZ,iBAAO,UAAU,QAAQ,UAAU;AAAA,QACtC;AAAA,MACH,CAAC;AAAA,MACD,IAAI,WAAW,eAAe,CAAC,QAAQ,CAAC,SAAS,EAAE,YAAY,EAAE,SAAS,MAAM;AAC7E,YAAI,eAAe,UAAa,cAAc,QAAW;AACtD,iBAAO,QAAQ,UAAU,CAAC,WAAW;AACrC,iBAAO,QAAQ,aAAa,CAAC,cAAc;AAC3C,iBAAO,QAAQ,YAAY,CAAC,aAAa;AACzC,iBAAO;AAAA,QACV;AACA,eAAO;AAAA,MACV,CAAC;AAAA,MACD,IAAI,WAAW,cAAc,CAAC,QAAQ,CAAC,QAAQ,IAAI,MAAM;AACtD,eAAO,OAAO,OAAO,IAAI;AACzB,eAAO,WAAW,WAAW,OAAO,UAAU,OAAO,SAAS,IAAI;AAAA,MACrE,CAAC;AAAA,IACJ;AAEA,IAAM,eAA+C;AAAA,MAClD,IAAI,WAAW,iBAAiB,CAAC,QAAQ,CAAC,MAAM,MAAM,MAAM,OAAO,SAAS,OAAO;AAAA,MACnF,IAAI,WAAW,kBAAkB,CAAC,QAAQ,CAAC,OAAO,MAAM,MAAM,OAAO,UAAU,QAAQ;AAAA,MACvF,IAAI;AAAA,QACD;AAAA,QACA,CAAC,QAAQ,CAAC,WAAW,YAAY,aAAa,YAAY,MAAM;AAC7D,iBAAO,OAAO,QAAQ;AACtB,iBAAO,KAAK,QAAQ;AACpB,iBAAO,OAAO,SAAS;AACvB,iBAAO,KAAK,SAAS;AAAA,QACxB;AAAA,MACH;AAAA,IACH;AAEO,IAAM,kBAAkD,CAAC,QAAQ,WAAW;AAChF,aAAO,oBAAoB,IAAI,YAAY,GAAGA,UAAS,CAAC,QAAQ,MAAM,CAAC;AAAA,IAC1E;AAEO,IAAM,kBAAkD,CAAC,QAAQ,WAAW;AAChF,aAAO,OAAO;AAAA,QACX,IAAI,YAAY;AAAA,QAChB,gBAAgB,QAAQ,MAAM;AAAA,QAC9B,oBAAoC,QAAQ,MAAM;AAAA,MACrD;AAAA,IACH;AAAA;AAAA;;;AC7DA,IAMMC,UAwBO,kBAQA;AAtCb;AAAA;AAAA;AACA;AAEA;AACA;AAEA,IAAMA,WAAqC;AAAA,MACxC,IAAI,WAAW,yBAAyB,CAAC,SAAS,CAAC,SAAS,MAAM;AAC/D,gBAAQ,OAAO,KAAK,SAAS;AAAA,MAChC,CAAC;AAAA,MACD,IAAI,WAAW,iDAAiD,CAAC,SAAS,CAAC,QAAQ,IAAI,MAAM;AAC1F,gBAAQ,UAAU,KAAK,IAAI,qBAAqB,QAAQ,IAAI,CAAC;AAAA,MAChE,CAAC;AAAA,MACD,IAAI;AAAA,QACD;AAAA,QACA,CAAC,SAAS,CAAC,QAAQ,MAAM,SAAS,MAAM;AACrC,kBAAQ,UAAU,KAAK,IAAI,qBAAqB,QAAQ,MAAM,EAAE,UAAU,CAAC,CAAC;AAAA,QAC/E;AAAA,MACH;AAAA,MACA,IAAI,WAAW,yBAAyB,CAAC,SAAS,CAAC,MAAM,MAAM;AAC5D,gBAAQ,UAAU,KAAK,IAAI,qBAAqB,QAAQ,IAAI,CAAC;AAAA,MAChE,CAAC;AAAA,MACD,IAAI,WAAW,oCAAoC,CAAC,SAAS,CAAC,MAAM,MAAM;AACvE,gBAAQ,SAAS;AAAA,MACpB,CAAC;AAAA,IACJ;AAKO,IAAM,mBAAoD,CAAC,QAAQ,WAAW;AAClF,aAAO,OAAO,OAAO,iBAAiB,QAAQ,MAAM,GAAG,gBAAgB,QAAQ,MAAM,CAAC;AAAA,IACzF;AAMO,IAAM,mBAAoD,CAAC,WAAW;AAC1E,aAAO,oBAAoB,IAAI,mBAAmB,GAAGA,UAAS,MAAM;AAAA,IACvE;AAAA;AAAA;;;AClCO,SAAS,UAAU,YAA2D;AAClF,MAAI,CAAC,WAAW,QAAQ;AACrB,WAAO,uBAAuB,wCAAwC;AAAA,EACzE;AAEA,SAAO;AAAA,IACJ,UAAU,CAAC,SAAS,GAAG,UAAU;AAAA,IACjC,QAAQ;AAAA,IACR,OAAO,QAAQ,QAAqB;AACjC,YAAM,QAAQ,iBAAiB,QAAQ,MAAM;AAC7C,UAAI,MAAM,QAAQ;AACf,cAAM,IAAI,iBAAiB,KAAK;AAAA,MACnC;AAEA,aAAO;AAAA,IACV;AAAA,EACH;AACH;AAvBA;AAAA;AAAA;AACA;AACA;AAEA;AAAA;AAAA;;;ACMA,SAAS,qBAAqB,OAAe,QAAgB,QAAsC;AAChG,QAAM,UAAU,OAAO,SAAS,SAAS;AACzC,QAAM,MAAM,OAAO,SAAS,KAAK,KAAK,cAAc,KAAK,KAAK;AAC9D,QAAM,iBAAiB,CAAC,OAAO,SAAS,KAAK;AAE7C,SAAO;AAAA,IACJ;AAAA,IACA;AAAA,IACA,QAAQ,CAAC;AAAA,IACT,KAAK,CAAC;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,EACH;AACH;AAxBA,IA0BMC,UAyCO,iBAUA;AA7Eb;AAAA;AAAA;AAOA;AACA;AAkBA,IAAMA,WAAoC;AAAA,MACvC,IAAI,WAAW,qBAAqB,CAAC,QAAQ,CAAC,IAAI,MAAM;AACrD,eAAO,OAAO;AAAA,MACjB,CAAC;AAAA,MACD,IAAI,WAAW,uCAAuC,CAAC,QAAQ,CAAC,KAAK,MAAM;AACxE,eAAO,MAAM,iCACN,OAAO,OAAO,CAAC,IADT;AAAA,UAEV;AAAA,QACH;AAAA,MACH,CAAC;AAAA,MACD,IAAI,WAAW,qCAAqC,CAAC,QAAQ,CAAC,OAAO,QAAQ,IAAI,MAAM;AACpF,eAAO,OAAO,KAAK,qBAAqB,OAAO,QAAQ,IAAI,CAAC;AAAA,MAC/D,CAAC;AAAA,MACD,IAAI;AAAA,QACD;AAAA,QACA,CAAC,QAAQ,CAAC,OAAO,QAAQ,UAAU,MAAM;AACtC,iBAAO,SAAS,iCACT,OAAO,UAAU,CAAC,IADT;AAAA,YAEb;AAAA,YACA;AAAA,YACA;AAAA,UACH;AAAA,QACH;AAAA,MACH;AAAA,MACA,IAAI;AAAA,QACD;AAAA,QACA,CAAC,QAAQ,CAAC,OAAO,QAAQ,MAAM,EAAE,MAAM;AACpC,iBAAO,SAAS;AAAA,YACb,MAAM;AAAA,cACH;AAAA,cACA;AAAA,YACH;AAAA,YACA,MAAM;AAAA,cACH;AAAA,cACA;AAAA,YACH;AAAA,UACH;AAAA,QACH;AAAA,MACH;AAAA,IACH;AAEO,IAAM,kBAAkD,CAAC,QAAQ,WAAW;AAChF,YAAM,aAAa,gBAAgB,QAAQ,MAAM;AACjD,YAAM,iBAAiB,oBAA8C,QAAQ,MAAM;AAEnF,aAAO,kCACD,aACA;AAAA,IAET;AAEO,IAAM,kBAAkD,CAAC,QAAQ,WAAW;AAChF,aAAO,oBAAoB,EAAE,QAAQ,CAAC,EAAE,GAAGA,UAAS,CAAC,QAAQ,MAAM,CAAC;AAAA,IACvE;AAAA;AAAA;;;AC/EA;AAAA;AAAA;AAAA;AAAA;AAOO,SAAS,aAAa,MAAe,CAAC,GAAG,YAA8C;AAC3F,SAAO,YAAY,QAAQ;AAC3B,SAAO,SAAS,KAAK,UAAU;AAClC;AAEO,SAAS,SAAS,MAAe,CAAC,GAAG,YAA8C;AACvF,QAAM,WAAW,CAAC,QAAQ,GAAG,UAAU;AACvC,MAAI,IAAI,QAAQ;AACb,aAAS,OAAO,GAAG,GAAG,IAAI,MAAM;AAAA,EACnC;AACA,MAAI,IAAI,QAAQ;AACb,aAAS,OAAO,GAAG,GAAG,IAAI,MAAM;AAAA,EACnC;AAEA,SAAO,UAAU,IAAI;AACrB,SAAO,UAAU,WAAW;AAC5B,SAAO,UAAU,aAAa;AAE9B,SAAO;AAAA,IACJ;AAAA,IACA,QAAQ;AAAA,IACR;AAAA,EACH;AACH;AA9BA;AAAA;AAAA;AACA;AAEA;AAAA;AAAA;;;ACEe,SAAR,eAA4D;AAChE,SAAO;AAAA,IACJ,aAA+B;AAC5B,YAAM,WAAW,CAAC,QAAQ,GAAG,mBAAmB,WAAW,CAAC,CAAC;AAC7D,UAAI,CAAC,SAAS,SAAS,UAAU,GAAG;AACjC,iBAAS,OAAO,GAAG,GAAG,UAAU;AAAA,MACnC;AAEA,aAAO,KAAK;AAAA,QACT,0BAA0B,QAAQ;AAAA,QAClC,yBAAyB,SAAS;AAAA,MACrC;AAAA,IACH;AAAA,IAEA,OAAyB;AACtB,YAAM,WAAW,CAAC,QAAQ,GAAG,mBAAmB,WAAW,CAAC,CAAC;AAC7D,aAAO,KAAK;AAAA,QACT,0BAA0B,QAAQ;AAAA,QAClC,yBAAyB,SAAS;AAAA,MACrC;AAAA,IACH;AAAA,EACH;AACH;AA3BA;AAAA;AAAA;AAEA;AACA;AAAA;AAAA;;;ACHA,IAEa,eAEA;AAJb;AAAA;AAAA;AAEO,IAAM,gBAAgB;AAEtB,IAAM,oBAAN,MAAoD;AAAA,MAGxD,YACU,MACA,OACA,aACR;AAHQ;AACA;AACA;AAEP,YAAI,UAAU,OAAO,gBAAgB,KAAK;AACvC,gBAAM,SAAS,cAAc,KAAK,IAAI,KAAK,CAAC,MAAM,MAAM,IAAI;AAC5D,eAAK,OAAO,OAAO,MAAM;AACzB,eAAK,OAAO,OAAO,MAAM;AAAA,QAC5B;AAAA,MACH;AAAA,IACH;AAAA;AAAA;;;ACqBA,SAAS,YAAY,MAAc;AAChC,QAAM,CAAC,IAAI,IAAI,IAAI,KAAK,MAAM,IAAI;AAElC,SAAO;AAAA,IACJ,MAAM,QAAQ;AAAA,IACd;AAAA,EACH;AACH;AAEA,SAASC,QACN,QACA,QACA,SAC2B;AAC3B,SAAO,CAAC,GAAG,SAAS,UAAU,OAAO;AACxC;AAEA,SAAS,UAAU,WAAgC,QAA+B;AAC/E,SAAO,OAAO,IAAI,CAAC,MAAMA,QAAO,QAAQ,GAAG,CAAC,QAAQ,SAAS,OAAO,OAAO,YAAY,IAAI,CAAC,CAAC;AAChG;AA4HA,SAAS,UAAU,QAAsB,SAAiB;AACvD,QAAMC,WAAU,QAAQ,KAAK;AAC7B,UAAQ;AAAA,SACAA,SAAQ,OAAO,CAAC;AAClB,aAAO,KAAKA,SAAQ,OAAO,CAAC,GAAGA,SAAQ,OAAO,CAAC,GAAGA,SAAQ,OAAO,CAAC,CAAC;AAAA,SACjEA,SAAQ,OAAO,CAAC;AAClB,aAAO,KAAK,gBAA0BA,SAAQ,OAAO,CAAC,GAAGA,SAAQ,OAAO,CAAC,CAAC;AAAA;AAE1E;AAAA;AAGN,WAAS,KAAK,OAAe,YAAoB,MAAc;AAC5D,UAAM,MAAM,GAAG,QAAQ;AACvB,UAAM,UAAUC,SAAQ,IAAI,GAAG;AAE/B,QAAI,SAAS;AACV,cAAQ,QAAQ,IAAI;AAAA,IACvB;AAEA,QAAI,QAAQ,QAAQ,QAAQ,MAAM;AAC/B,aAAO,MAAM,KAAK,IAAI,kBAAkB,MAAM,OAAO,UAAU,CAAC;AAAA,IACnE;AAAA,EACH;AACH;AA7MA,IAMa,eAsDPA,UAqGO;AAjKb;AAAA;AAAA;AACA;AACA;AAIO,IAAM,gBAAN,MAA4C;AAAA,MAA5C;AACJ,aAAO,YAAY,CAAC;AACpB,aAAO,aAAa,CAAC;AACrB,aAAO,UAAU,CAAC;AAClB,aAAO,UAAU,CAAC;AAClB,aAAO,UAAU;AACjB,aAAO,WAAW,CAAC;AACnB,aAAO,UAAU,CAAC;AAClB,aAAO,QAAQ,CAAC;AAChB,aAAO,SAAS,CAAC;AACjB,aAAO,QAAQ;AACf,aAAO,SAAS;AAChB,aAAO,UAAU;AACjB,aAAO,WAAW;AAClB,aAAO,WAAW;AAElB,aAAO,UAAU,MAAM;AACpB,iBAAO,CAAC,KAAK,MAAM;AAAA,QACtB;AAAA;AAAA,IACH;AAmCA,IAAMA,WAAyC,IAAI,IAAI;AAAA,MACpDF;AAAA,QAAO;AAAA,QAA0B;AAAA,QAA2B,CAAC,QAAQ,SAClE,OAAO,OAAO,SAAS,IAAI;AAAA,MAC9B;AAAA,MACAA;AAAA,QAAO;AAAA,QAA0B;AAAA,QAA6B,CAAC,QAAQ,SACpE,OAAO,OAAO,SAAS,IAAI;AAAA,MAC9B;AAAA,MACAA;AAAA,QAAO;AAAA,QAA0B;AAAA,QAA8B,CAAC,QAAQ,SACrE,OAAO,OAAO,UAAU,IAAI;AAAA,MAC/B;AAAA,MAEAA;AAAA,QACG;AAAA,QACA;AAAA,QACA,CAAC,QAAQ,SAAS,OAAO,OAAO,SAAS,IAAI,KAAK,OAAO,OAAO,QAAQ,IAAI;AAAA,MAC/E;AAAA,MACAA;AAAA,QACG;AAAA,QACA;AAAA,QACA,CAAC,QAAQ,SACN,OAAO,OAAO,SAAS,IAAI,KAC3B,OAAO,OAAO,QAAQ,IAAI,KAC1B,OAAO,OAAO,UAAU,IAAI;AAAA,MAClC;AAAA,MAEAA;AAAA,QACG;AAAA,QACA;AAAA,QACA,CAAC,QAAQ,SAAS,OAAO,OAAO,SAAS,IAAI,KAAK,OAAO,OAAO,QAAQ,IAAI;AAAA,MAC/E;AAAA,MAEAA;AAAA,QACG;AAAA,QACA;AAAA,QACA,CAAC,QAAQ,SAAS,OAAO,OAAO,UAAU,IAAI,KAAK,OAAO,OAAO,QAAQ,IAAI;AAAA,MAChF;AAAA,MACAA;AAAA,QACG;AAAA,QACA;AAAA,QACA,CAAC,QAAQ,SAAS,OAAO,OAAO,UAAU,IAAI,KAAK,OAAO,OAAO,QAAQ,IAAI;AAAA,MAChF;AAAA,MAEAA,QAAO,mBAA6B,gBAA0B,CAAC,QAAQ,SAAS;AAC7E,eAAO,OAAO,SAAS,YAAY,IAAI,CAAC;AAAA,MAC3C,CAAC;AAAA,MACDA,QAAO,mBAA6B,oBAA8B,CAAC,QAAQ,SAAS;AACjF,cAAM,UAAU,YAAY,IAAI;AAChC,eAAO,OAAO,SAAS,OAAO;AAC9B,eAAO,OAAO,UAAU,QAAQ,EAAE;AAAA,MACrC,CAAC;AAAA,MACDA,QAAO,mBAA6B,mBAA6B,CAAC,SAAS,UAAU;AAClF,eAAQ,QAAQ,UAAU,QAAQ,WAAW,CAAC,GAAI,KAAK;AAAA,MAC1D,CAAC;AAAA,MAEDA;AAAA,QAAO;AAAA,QAA+B;AAAA,QAA+B,CAAC,QAAQ,SAC3E,OAAO,OAAO,WAAW,IAAI;AAAA,MAChC;AAAA,MAEA,GAAG,UAAU,iBAA2B,iBAA2B,kBAA4B;AAAA,MAC/F,GAAG;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACH;AAAA,MACA,GAAG;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACH;AAAA,MAEA;AAAA,QACG;AAAA,QACA,CAAC,QAAQ,SAAS;AACf,gBAAM,WAAW;AACjB,gBAAM,YAAY;AAClB,gBAAM,aAAa;AACnB,gBAAM,cAAc;AACpB,gBAAM,mBAAmB;AACzB,cAAI;AAEJ,wBAAc,SAAS,KAAK,IAAI;AAChC,iBAAO,QAAS,eAAe,CAAC,YAAY,MAAO;AAEnD,wBAAc,UAAU,KAAK,IAAI;AACjC,iBAAO,SAAU,eAAe,CAAC,YAAY,MAAO;AAEpD,wBAAc,WAAW,KAAK,IAAI;AAClC,iBAAO,UAAU,eAAe,YAAY;AAE5C,wBAAc,YAAY,KAAK,IAAI;AACnC,iBAAO,WAAW,eAAe,YAAY;AAE7C,wBAAc,iBAAiB,KAAK,IAAI;AACxC,iBAAO,UAAW,eAAe,YAAY,MAAO,OAAO;AAE3D,iBAAO,WAAW,gBAAgB,KAAK,IAAI;AAAA,QAC9C;AAAA,MACH;AAAA,IACH,CAAC;AAEM,IAAM,qBAAqB,SAAU,MAA4B;AACrE,YAAM,QAAQ,KAAK,MAAM,IAAI;AAC7B,YAAM,SAAS,IAAI,cAAc;AAEjC,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,IAAI,KAAK;AACxC,YAAI,OAAO,MAAM,KAAK,KAAK;AAE3B,YAAI,CAAC,MAAM;AACR;AAAA,QACH;AAEA,YAAI,KAAK,OAAO,CAAC,MAAM,mBAA6B;AACjD,kBAAQ,QAAQ,MAAM,QAAQ;AAAA,QACjC;AAEA,kBAAU,QAAQ,IAAI;AAAA,MACzB;AAEA,aAAO;AAAA,IACV;AAAA;AAAA;;;AC9KO,SAAS,WAAW,YAAgD;AACxE,QAAM,WAAW;AAAA,IACd;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,GAAG,WAAW,OAAO,CAAC,QAAQ,CAAC,eAAe,SAAS,GAAG,CAAC;AAAA,EAC9D;AAEA,SAAO;AAAA,IACJ,QAAQ;AAAA,IACR;AAAA,IACA,OAAO,MAAc;AAClB,aAAO,mBAAmB,IAAI;AAAA,IACjC;AAAA,EACH;AACH;AAvBA,IAIM;AAJN;AAAA;AAAA;AACA;AAGA,IAAM,iBAAiB,CAAC,UAAU,IAAI;AAAA;AAAA;;;ACUtC,SAAS,gBACN,QAAQ,GACR,QAAQ,GACR,QAAyB,GACzB,QAAQ,IACR,YAAY,MACE;AACd,SAAO,OAAO;AAAA,IACX;AAAA,MACG;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,MACG,QAAQ;AACL,eAAO,GAAG,KAAK,SAAS,KAAK,SAAS,KAAK;AAAA,MAC9C;AAAA,MACA,cAAc;AAAA,MACd,YAAY;AAAA,IACf;AAAA,EACH;AACH;AAEA,SAAS,uBAAuB;AAC7B,SAAO,gBAAgB,GAAG,GAAG,GAAG,IAAI,KAAK;AAC5C;AAEe,SAAR,kBAAgD;AACpD,SAAO;AAAA,IACJ,UAA4B;AACzB,aAAO,KAAK,SAAS;AAAA,QAClB,UAAU,CAAC,WAAW;AAAA,QACtB,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,QAAQ,QAAQ,OAAO,MAAM,MAAM;AAChC,cAAI,OAAO,iCAAkC;AAC1C,mBAAO,KAAK,OAAO,KAAK,aAAa,CAAC;AAAA,UACzC;AAEA,eAAK,KAAK;AAAA,QACb;AAAA,MACH,CAAC;AAAA,IACJ;AAAA,EACH;AACH;AAoBA,SAAS,cAAc,QAAgB;AACpC,MAAI,WAAW,eAAe;AAC3B,WAAO,qBAAqB;AAAA,EAC/B;AAEA,SAAO,oBAAoB,gBAAgB,GAAG,GAAG,GAAG,MAAM,GAAGG,UAAS,MAAM;AAC/E;AAvFA,IAYM,eAmDAA;AA/DN;AAAA;AAAA;AAEA;AAUA,IAAM,gBAAgB;AAmDtB,IAAMA,WAAuC;AAAA,MAC1C,IAAI;AAAA,QACD;AAAA,QACA,CAAC,QAAQ,CAAC,OAAO,OAAO,OAAO,QAAQ,EAAE,MAAM;AAC5C,iBAAO;AAAA,YACJ;AAAA,YACA,gBAAgB,SAAS,KAAK,GAAG,SAAS,KAAK,GAAG,SAAS,KAAK,GAAG,KAAK;AAAA,UAC3E;AAAA,QACH;AAAA,MACH;AAAA,MACA,IAAI;AAAA,QACD;AAAA,QACA,CAAC,QAAQ,CAAC,OAAO,OAAO,OAAO,QAAQ,EAAE,MAAM;AAC5C,iBAAO,OAAO,QAAQ,gBAAgB,SAAS,KAAK,GAAG,SAAS,KAAK,GAAG,OAAO,KAAK,CAAC;AAAA,QACxF;AAAA,MACH;AAAA,IACH;AAAA;AAAA;;;AC/EA;AAAA;AAAA;AAAA;AAAA,IA2Ba;AA3Bb;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAQO,IAAM,eAAN,MAA4C;AAAA,MAChD,YAAoB,WAA8B;AAA9B;AAAA,MAA+B;AAAA,MAEzC,SAAY,MAAwB,MAAiC;AAC5E,cAAM,QAAQ,KAAK,UAAU,MAAM;AACnC,cAAM,UAAU,MAAM,KAAK,IAAI;AAE/B,YAAI,MAAM;AACP,uBAAa,MAAM,SAAS,IAAI;AAAA,QACnC;AAEA,eAAO,OAAO,OAAO,MAAM;AAAA,UACxB,MAAM,EAAE,OAAO,QAAQ,KAAK,KAAK,OAAO,EAAE;AAAA,UAC1C,OAAO,EAAE,OAAO,QAAQ,MAAM,KAAK,OAAO,EAAE;AAAA,UAC5C,WAAW,EAAE,OAAO,MAAM;AAAA,QAC7B,CAAC;AAAA,MACJ;AAAA,MAEA,IAAI,OAA0B;AAC3B,eAAO,KAAK;AAAA,UACT,0BAA0B,CAAC,OAAO,GAAG,QAAQ,KAAK,CAAC,CAAC;AAAA,UACpD,yBAAyB,SAAS;AAAA,QACrC;AAAA,MACH;AAAA,MAEA,IAAI,WAAsD;AACvD,cAAM,OAAO,yBAAyB,SAAS;AAE/C,YAAI,OAAO,cAAc,UAAU;AAChC,iBAAO,KAAK,SAAS,2BAA2B,WAAW,KAAK,SAAS,GAAG,IAAI;AAAA,QACnF;AAEA,YAAI,QAAO,uCAAW,UAAS,UAAU;AACtC,iBAAO,KAAK;AAAA,YACT;AAAA,cACG,UAAU;AAAA,cACT,UAAU,QAAQ,KAAK,aAAc;AAAA,YACzC;AAAA,YACA;AAAA,UACH;AAAA,QACH;AAEA,eAAO,KAAK;AAAA,UACT,uBAAuB,wDAAwD;AAAA,UAC/E;AAAA,QACH;AAAA,MACH;AAAA,MAEA,WAAW,MAAc,OAA0B;AAChD,eAAO,KAAK;AAAA,UACT,eAAe,MAAM,UAAU,IAAI;AAAA,UACnC,yBAAyB,SAAS;AAAA,QACrC;AAAA,MACH;AAAA,MAEA,KAAK,MAA0B;AAC5B,eAAO,KAAK;AAAA,UACT,SAAS,SAAS,MAAM,KAAK,UAAU,KAAK,mBAAmB,SAAS,CAAC;AAAA,UACzE,yBAAyB,SAAS;AAAA,QACrC;AAAA,MACH;AAAA,MAEA,QAAQ;AACL,eAAO,KAAK;AAAA,UACT,UAAU,mBAAmB,SAAS,CAAC;AAAA,UACvC,yBAAyB,SAAS;AAAA,QACrC;AAAA,MACH;AAAA,MAEA,YAAY,QAAgB,QAAgB;AACzC,YAAI,EAAE,aAAa,MAAM,KAAK,aAAa,MAAM,IAAI;AAClD,iBAAO,KAAK;AAAA,YACT;AAAA,cACG;AAAA,YACH;AAAA,UACH;AAAA,QACH;AAEA,eAAO,KAAK;AAAA,UACT,UAAU,CAAC,QAAQ,QAAQ,GAAG,mBAAmB,SAAS,CAAC,CAAC;AAAA,UAC5D,yBAAyB,WAAW,KAAK;AAAA,QAC5C;AAAA,MACH;AAAA,MAEA,cAAc,SAAwB;AACnC,aAAK,UAAU,gBAAgB;AAC/B,eAAO;AAAA,MACV;AAAA,MAEA,OAAO;AACJ,cAAM,OAAO;AAAA,UACV;AAAA,YACG,QAAQ,WAAW,UAAU,IAAI,YAAY;AAAA,YAC7C,QAAQ,WAAW,UAAU,IAAI,YAAY;AAAA,UAChD;AAAA,UACA,mBAAmB,SAAS;AAAA,QAC/B;AAEA,eAAO,KAAK,SAAS,MAAM,yBAAyB,SAAS,CAAC;AAAA,MACjE;AAAA,MAEA,QAAQ;AACL,eAAO,KAAK;AAAA,UACT,0BAA0B,CAAC,SAAS,GAAG,mBAAmB,SAAS,CAAC,CAAC;AAAA,UACrE,yBAAyB,SAAS;AAAA,QACrC;AAAA,MACH;AAAA,MAEA,SAAS;AACN,eAAO,KAAK;AAAA,UACT,WAAW,mBAAmB,SAAS,CAAC;AAAA,UACxC,yBAAyB,SAAS;AAAA,QACrC;AAAA,MACH;AAAA,IACH;AAEA,WAAO;AAAA,MACJ,aAAa;AAAA,MACb,iBAAS;AAAA,MACT,eAAO;AAAA,MACP,eAAO;AAAA,MACP,sBAAa;AAAA,MACb,qBAAY;AAAA,MACZ,aAAK;AAAA,MACL,YAAI;AAAA,MACJ,aAAK;AAAA,MACL,gBAAQ;AAAA,IACX;AAAA;AAAA;;;AC1JA;AAAA;AAAA;AAAA;AACA,SAAS,sBAAuC;AADhD,IASM,qBAcO;AAvBb;AAAA;AAAA;AAAA;AAEA;AAOA,IAAM,uBAA4C,MAAM;AACrD,UAAI,KAAK;AACT,aAAO,MAAM;AACV;AACA,cAAM,EAAE,SAAS,KAAK,IAAI,eAAyC;AAEnE,eAAO;AAAA,UACJ;AAAA,UACA;AAAA,UACA;AAAA,QACH;AAAA,MACH;AAAA,IACH,GAAG;AAEI,IAAM,YAAN,MAAgB;AAAA,MAKpB,YAAoB,cAAc,GAAG;AAAjB;AAJpB,aAAQ,SAAS,aAAa,IAAI,WAAW;AAC7C,aAAQ,UAA2B,CAAC;AACpC,aAAQ,UAA2B,CAAC;AAGjC,aAAK,OAAO,+BAA+B,WAAW;AAAA,MACzD;AAAA,MAEQ,WAAW;AAChB,YAAI,CAAC,KAAK,QAAQ,UAAU,KAAK,QAAQ,UAAU,KAAK,aAAa;AAClE,eAAK;AAAA,YACF;AAAA,YACA,KAAK,QAAQ;AAAA,YACb,KAAK,QAAQ;AAAA,YACb,KAAK;AAAA,UACR;AACA;AAAA,QACH;AAEA,cAAM,OAAO,OAAO,KAAK,SAAS,KAAK,QAAQ,MAAM,CAAE;AACvD,aAAK,OAAO,oBAAoB,KAAK,EAAE;AACvC,aAAK,KAAK,MAAM;AACb,eAAK,OAAO,kBAAkB,KAAK,EAAE;AACrC,iBAAO,KAAK,SAAS,IAAI;AACzB,eAAK,SAAS;AAAA,QACjB,CAAC;AAAA,MACJ;AAAA,MAEA,OAA0C;AACvC,cAAM,EAAE,SAAS,GAAG,IAAI,OAAO,KAAK,SAAS,oBAAoB,CAAC;AAClE,aAAK,OAAO,oBAAoB,EAAE;AAElC,aAAK,SAAS;AAEd,eAAO;AAAA,MACV;AAAA,IACH;AAAA;AAAA;;;AC5DA;AAAA;AAAA;AAAA;AAgCO,SAAS,eAAe,SAAmB,YAA0C;AACzF,SAAO,0BAA0B,CAAC,SAAS,GAAG,YAAY,GAAG,OAAO,CAAC;AACxE;AAlCA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACiBO,SAAS,sBAAsB,QAAgB,MAAyC;AAC5F,SAAO;AAAA,IACJ;AAAA,IACA;AAAA,IACA,SAAS;AAAA,EACZ;AACH;AAEO,SAAS,sBAAsB,QAA2C;AAC9E,SAAO;AAAA,IACJ;AAAA,IACA,MAAM;AAAA,IACN,SAAS;AAAA,EACZ;AACH;AA/BA,IAOa;AAPb;AAAA;AAAA;AAOO,IAAM,sBAAN,MAA6D;AAAA,MAA7D;AACJ,mBAAkC,CAAC;AACnC,wBAA+D,CAAC;AAChE,sBAAqC,CAAC;AAAA;AAAA,MAEtC,IAAI,UAAmB;AACpB,eAAO,CAAC,KAAK,OAAO;AAAA,MACvB;AAAA,IACH;AAAA;AAAA;;;ACoBO,SAAS,uBAAuB,MAAc,iBAAqC;AACvF,SAAO,qCAAuC,iBAAiB,KAAK,IAAI;AAC3E;AArCA,IASM,oBACA,kBAEAC,UAgBO;AA5Bb;AAAA;AAAA;AACA;AAMA;AAEA,IAAM,qBAAqB;AAC3B,IAAM,mBAAmB;AAEzB,IAAMA,WAAiD;AAAA,MACpD,IAAI,WAAW,oBAAoB,CAAC,QAAQ,CAAC,QAAQ,IAAI,MAAM;AAC5D,cAAM,WAAW,sBAAsB,QAAQ,IAAI;AAEnD,eAAO,IAAI,KAAK,QAAQ;AACxB,eAAO,SAAS,UAAU;AAAA,MAC7B,CAAC;AAAA,MACD,IAAI,WAAW,kBAAkB,CAAC,QAAQ,CAAC,MAAM,MAAM;AACpD,cAAM,WAAW,sBAAsB,MAAM;AAE7C,eAAO,OAAO,KAAK,QAAQ;AAC3B,eAAO,IAAI,KAAK,QAAQ;AACxB,eAAO,SAAS,UAAU;AAAA,MAC7B,CAAC;AAAA,IACJ;AAEO,IAAM,uBAAoE,CAC9E,QACA,WACE;AACF,aAAO,oBAAoB,IAAI,oBAAoB,GAAGA,UAAS,CAAC,QAAQ,MAAM,CAAC;AAAA,IAClF;AAAA;AAAA;;;ACjCA,IAOa;AAPb;AAAA;AAAA;AAOO,IAAM,sBAAN,MAAmD;AAAA,MAAnD;AACJ,aAAO,MAAgB,CAAC;AACxB,aAAO,WAAiD,CAAC;AACzD,aAAO,UAAkB;AACzB,aAAO,WAAoB;AAAA;AAAA,MAE3B,KACG,QACA,UACA,MACA,QACA,OACD;AACC,YAAI,WAAW,mBAAgC;AAC5C,eAAK,WAAW;AAChB,eAAK,UAAU;AAAA,QAClB;AAEA,aAAK,IAAI,KAAK,IAAI;AAClB,aAAK,SAAS,QAAQ;AAAA,UACnB,SAAS,WAAW;AAAA,UACpB,gBAAgB,WAAW;AAAA,UAC3B;AAAA,UACA;AAAA,UACA;AAAA,QACH;AAAA,MACH;AAAA,IACH;AAAA;AAAA;;;ACfA,SAAS,aAAa,OAAgB;AACnC,SAAO,QAAQ,MAAM,OAAO,CAAC,IAAI;AACpC;AAEO,SAAS,mBAAmB,QAA+B;AAC/D,SAAO,oBAAoB,IAAI,oBAAoB,GAAGC,UAAS,MAAM;AACxE;AAzBA,IAIMA;AAJN;AAAA;AAAA;AACA;AACA;AAEA,IAAMA,WAA6C;AAAA,MAChD,IAAI;AAAA,QACD;AAAA,QACA,CAAC,QAAQ,CAAC,SAAS,MAAM,QAAQ,KAAK,MAAM;AACzC,iBAAO,KAAK,aAAa,OAAO,GAAG,MAAM,MAAM,QAAQ,KAAK;AAAA,QAC/D;AAAA,MACH;AAAA,MACA,IAAI;AAAA,QACD,WAAC,6CAAsC,GAAC;AAAA,QACxC,CAAC,QAAQ,CAAC,SAAS,MAAM,QAAQ,KAAK,MAAM;AACzC,iBAAO,KAAK,aAAa,OAAO,GAAG,OAAO,MAAM,QAAQ,KAAK;AAAA,QAChE;AAAA,MACH;AAAA,IACH;AAAA;AAAA;;;ACjBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAOO,SAAS,4BAA4B,UAAoB;AAC7D,QAAM,iBAAiB,CAAC,MAAM,MAAM,UAAU;AAC9C,SAAO,SAAS,KAAK,CAAC,YAAY,eAAe,SAAS,OAAO,CAAC;AACrE;AAEO,SAAS,WACb,YACqD;AACrD,QAAM,WAAW,4BAA4B,UAAU;AACvD,QAAM,WAAW,CAAC,UAAU,GAAG,UAAU;AAEzC,MAAI,SAAS,WAAW,GAAG;AACxB,aAAS,KAAK,IAAI;AAAA,EACrB;AAEA,MAAI,CAAC,SAAS,SAAS,IAAI,GAAG;AAC3B,aAAS,OAAO,GAAG,GAAG,IAAI;AAAA,EAC7B;AAEA,SAAO;AAAA,IACJ,QAAQ;AAAA,IACR;AAAA,IACA,OAAO,QAAQ,QAAQ;AACpB,UAAI,UAAU;AACX,eAAO,qBAAqB,QAAQ,MAAM,EAAE,IAAI;AAAA,MACnD;AAEA,aAAO,mBAAmB,MAAM;AAAA,IACnC;AAAA,EACH;AACH;AAEO,SAAS,kBAA6C;AAC1D,QAAMC,UAAS;AAEf,SAAO;AAAA,IACJ,QAAQ;AAAA,IACR,UAAU,CAAC,UAAU,IAAI;AAAA,IACzB,QAAAA;AAAA,EACH;AACH;AAEO,SAAS,mBACb,UACA,cAAc,OACsB;AACpC,SAAO;AAAA,IACJ,QAAQ;AAAA,IACR,UAAU,CAAC,UAAU,MAAM,cAAc,OAAO,MAAM,GAAG,QAAQ;AAAA,IACjE,OAAO,QAAQ,QAAQ;AACpB,aAAO,qBAAqB,QAAQ,MAAM;AAAA,IAC7C;AAAA,IACA,QAAQ,EAAE,UAAU,OAAO,GAAG,OAAO,MAAM,MAAM;AAC9C,UAAI,CAAC,uBAAuB,OAAO,KAAK,GAAG,QAAQ,GAAG;AACnD,eAAO,KAAK,KAAK;AAAA,MACpB;AAEA,WAAK,MAAM;AAAA,IACd;AAAA,EACH;AACH;AAEO,SAAS,iBACb,QACA,cAAc,OACuB;AACrC,QAAM,OAA6C;AAAA,IAChD,QAAQ;AAAA,IACR,UAAU,CAAC,UAAU,MAAM,cAAc,OAAO,MAAM,MAAM;AAAA,IAC5D,OAAO,QAAQ,QAAQ;AACpB,aAAO,qBAAqB,QAAQ,MAAM,EAAE,SAAS;AAAA,IACxD;AAAA,IACA,QAAQ,EAAE,UAAU,QAAQ,OAAO,GAAG,OAAO,GAAG,MAAM;AACnD,UAAI,CAAC,uBAAuB,OAAO,KAAK,GAAG,QAAQ,GAAG;AACnD,eAAO,KAAK,KAAK;AAAA,MACpB;AAEA,YAAM,IAAI;AAAA,QACP,KAAK,OAAO,eAAe,MAAM,GAAG,eAAe,MAAM,CAAC;AAAA,QAC1D,OAAO,KAAK;AAAA,MACf;AAAA,IACH;AAAA,EACH;AAEA,SAAO;AACV;AA5FA;AAAA;AAAA;AAEA;AACA;AACA;AACA;AAAA;AAAA;;;ACLA,IAGa;AAHb;AAAA;AAAA;AAGO,IAAM,mBAAmB,CAAC,SAA2B;AACzD,aAAO,KACH,MAAM,KAAK,EACX,IAAI,CAAC,SAAS,KAAK,KAAK,CAAC,EACzB,OAAO,CAAC,SAAS,CAAC,CAAC,IAAI;AAAA,IAC9B;AAAA;AAAA;;;ACRA;AAAA;AAAA;AAAA;AAGO,SAAS,gBAAgB,OAAuC;AACpE,SAAO;AAAA,IACJ,UAAU,CAAC,gBAAgB,GAAG,KAAK;AAAA,IACnC,QAAQ;AAAA,IACR,QAAQ;AAAA,EACX;AACH;AATA;AAAA;AAAA;AACA;AAAA;AAAA;;;ACDA;AAAA;AAAA;AAAA;AAAA;AA+BA,SAAS,kBAAkB,SAAiB;AACzC,SAAO,sBAAsB,KAAK,OAAO;AAC5C;AAEO,SAAS,UACb,MACA,WACA,YAC+B;AAC/B,QAAM,WAAW,CAAC,SAAS,GAAG,UAAU;AAExC,eAAa,IAAI,KAAK,SAAS,KAAK,IAAI;AACxC,eAAa,SAAS,KAAK,SAAS,KAAK,SAAS;AAElD,QAAM,SAAS,SAAS,KAAK,iBAAiB;AAC9C,MAAI,QAAQ;AACT,WAAO,uBAAuB,gDAAgD;AAAA,EACjF;AAEA,SAAO,0BAA0B,QAAQ;AAC5C;AAEO,SAAS,gBACb,MACA,WACA,YACD;AACC,SAAO,YAAY,UAAU;AAE7B,SAAO,UAAU,MAAM,WAAW,UAAU;AAC/C;AA7DA;AAAA;AAAA;AAAA;AAEA;AAAA;AAAA;;;ACmCO,SAAS,iBAAiB,QAAgB,QAA6B;AAC3E,QAAM,SAAsB;AAAA,IACzB,KAAK;AAAA,IACL,QAAQ;AAAA,IACR,UAAU,CAAC;AAAA,IACX,MAAM,CAAC;AAAA,IACP,SAAS,CAAC;AAAA,IACV,SAAS,CAAC;AAAA,EACb;AACA,SAAO,oBAAoB,QAAQC,WAAS,CAAC,QAAQ,MAAM,CAAC;AAC/D;AA/CA,IAGMA;AAHN;AAAA;AAAA;AACA;AAEA,IAAMA,YAAqC;AAAA,MACxC,IAAI,WAAW,cAAc,CAAC,QAAQ,CAAC,MAAM,MAAM;AAChD,eAAO,SAAS;AAAA,MACnB,CAAC;AAAA,MACD,IAAI,WAAW,uCAAuC,CAAC,QAAQ,CAAC,MAAM,QAAQ,MAAM;AACjF,eAAO,SAAS,KAAK;AAAA,UAClB;AAAA,UACA;AAAA,QACH,CAAC;AAAA,MACJ,CAAC;AAAA,MACD,IAAI,WAAW,oCAAoC,CAAC,QAAQ,CAAC,MAAM,QAAQ,MAAM;AAC9E,eAAO,KAAK,KAAK;AAAA,UACd;AAAA,UACA;AAAA,QACH,CAAC;AAAA,MACJ,CAAC;AAAA,MACD,IAAI,WAAW,iCAAiC,CAAC,QAAQ,CAAC,QAAQ,MAAM;AACrE,eAAO,QAAQ,KAAK;AAAA,UACjB;AAAA,QACH,CAAC;AAAA,MACJ,CAAC;AAAA,MACD,IAAI;AAAA,QACD;AAAA,QACA,CAAC,QAAQ,CAAC,MAAM,IAAI,MAAM,QAAQ,MAAM;AACrC,iBAAO,QAAQ,KAAK;AAAA,YACjB;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACH,CAAC;AAAA,QACJ;AAAA,MACH;AAAA,IACH;AAAA;AAAA;;;ACnCA;AAAA;AAAA;AAAA;AAMA,SAASC,mBAAkB,SAAiB;AACzC,SAAO,sBAAsB,KAAK,OAAO;AAC5C;AAEO,SAAS,UACb,QACA,QACA,YACoC;AACpC,QAAM,WAAW,CAAC,SAAS,GAAG,UAAU;AACxC,MAAI,UAAU,QAAQ;AACnB,aAAS,KAAK,QAAQ,MAAM;AAAA,EAC/B;AAEA,QAAM,SAAS,SAAS,KAAKA,kBAAiB;AAC9C,MAAI,QAAQ;AACT,WAAO,uBAAuB,gDAAgD;AAAA,EACjF;AAEA,SAAO;AAAA,IACJ;AAAA,IACA,QAAQ;AAAA,IACR,QAAQ;AAAA,EACX;AACH;AA9BA;AAAA;AAAA;AACA;AAGA;AAAA;AAAA;;;ACKO,SAAS,gBAAgB,QAA4B;AACzD,SAAO,oBAAoB,EAAE,OAAO,CAAC,EAAE,GAAGC,WAAS,MAAM;AAC5D;AAXA,IAGMA;AAHN;AAAA;AAAA;AACA;AAEA,IAAMA,YAAoC;AAAA,MACvC,IAAI,WAAW,2BAA2B,CAAC,QAAQ,CAAC,MAAM,EAAE,MAAM;AAC/D,eAAO,MAAM,KAAK,EAAE,MAAM,GAAG,CAAC;AAAA,MACjC,CAAC;AAAA,IACJ;AAAA;AAAA;;;ACPA;AAAA;AAAA;AAAA;AAKO,SAAS,SAAS,MAAyB,IAAoC;AACnF,SAAO;AAAA,IACJ,UAAU,CAAC,MAAM,MAAM,GAAG,QAAQ,IAAI,GAAG,EAAE;AAAA,IAC3C,QAAQ;AAAA,IACR,QAAQ;AAAA,EACX;AACH;AAXA;AAAA;AAAA;AACA;AAEA;AAAA;AAAA;;;ACHA;AAAA;AAAA;AAAA;AAMO,SAAS,SACb,QACA,QACA,YACuB;AACvB,QAAM,WAAqB,CAAC,QAAQ,GAAG,UAAU;AACjD,MAAI,UAAU,QAAQ;AACnB,aAAS,OAAO,GAAG,GAAG,QAAQ,MAAM;AAAA,EACvC;AAEA,SAAO;AAAA,IACJ;AAAA,IACA,QAAQ;AAAA,IACR,OAAO,QAAQ,QAAoB;AAChC,aAAO,gBAAgB,QAAQ,MAAM;AAAA,IACxC;AAAA,IACA,QAAQ,QAAQ,QAAQ,OAAO,MAAM;AAClC,YAAM,YAAY;AAAA,QACf,eAAe,OAAO,MAAM;AAAA,QAC5B,eAAe,OAAO,MAAM;AAAA,MAC/B;AACA,UAAI,WAAW;AACZ,eAAO,KAAK,IAAI,iBAAiB,SAAS,CAAC;AAAA,MAC9C;AAEA,WAAK,MAAM;AAAA,IACd;AAAA,EACH;AACH;AAlCA;AAAA;AAAA;AACA;AACA;AAEA;AAAA;AAAA;;;ACSO,SAAS,gBAAgB,MAAmC;AAChE,QAAM,UAAiD,CAAC;AAExD,UAAQ,MAAM,CAAC,CAAC,IAAI,MAAO,QAAQ,QAAQ,EAAE,KAAK,CAAE;AAEpD,SAAO,OAAO,OAAO,OAAO;AAC/B;AAEO,SAAS,uBAAuB,MAAgC;AACpE,QAAM,UAA8C,CAAC;AAErD,UAAQ,MAAM,CAAC,CAAC,MAAM,KAAK,OAAO,MAAM;AACrC,QAAI,CAAC,QAAQ,eAAe,IAAI,GAAG;AAChC,cAAQ,QAAQ;AAAA,QACb;AAAA,QACA,MAAM,EAAE,OAAO,IAAI,MAAM,GAAG;AAAA,MAC/B;AAAA,IACH;AAEA,QAAI,WAAW,KAAK;AACjB,cAAQ,MAAM,KAAK,QAAQ,QAAQ,WAAW,EAAE,KAAqC;AAAA,IACxF;AAAA,EACH,CAAC;AAED,SAAO,OAAO,OAAO,OAAO;AAC/B;AAEA,SAAS,QAAQ,MAAc,SAAmC;AAC/D,yBAAuB,MAAM,CAAC,SAAS,QAAQ,KAAK,MAAM,KAAK,CAAC,CAAC;AACpE;AA1CA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAIO,SAAS,cACb,YACA,YACA,YACmB;AACnB,SAAO,0BAA0B,CAAC,UAAU,OAAO,GAAG,YAAY,YAAY,UAAU,CAAC;AAC5F;AAEO,SAAS,eAAe,SAAmC;AAC/D,QAAM,WAAW,CAAC,QAAQ;AAC1B,MAAI,SAAS;AACV,aAAS,KAAK,IAAI;AAAA,EACrB;AAEA,SAAO;AAAA,IACJ;AAAA,IACA,QAAQ;AAAA,IACR,QAAQ,UAAU,yBAAyB;AAAA,EAC9C;AACH;AAEO,SAAS,gBAAgB,YAA0C;AACvE,QAAM,WAAW,CAAC,GAAG,UAAU;AAC/B,MAAI,SAAS,OAAO,aAAa;AAC9B,aAAS,QAAQ,WAAW;AAAA,EAC/B;AAEA,SAAO,0BAA0B,QAAQ;AAC5C;AAEO,SAAS,WAAW,YAA0C;AAClE,QAAM,WAAW,CAAC,GAAG,UAAU;AAC/B,MAAI,SAAS,OAAO,UAAU;AAC3B,aAAS,QAAQ,QAAQ;AAAA,EAC5B;AAEA,SAAO,0BAA0B,QAAQ;AAC5C;AAEO,SAAS,iBAAiB,YAAoB;AAClD,SAAO,0BAA0B,CAAC,UAAU,UAAU,UAAU,CAAC;AACpE;AA7CA;AAAA;AAAA;AAAA;AAEA;AAAA;AAAA;;;ACFA;AAAA;AAAA;AAAA;AAQO,SAAS,cACb,MAAkB,CAAC,GACnB,YACkC;AAClC,QAAM,UAAU,gBAAqB,GAAG;AACxC,QAAM,WAAW,CAAC,SAAS,QAAQ,GAAG,QAAQ,UAAU,GAAG,UAAU;AACrE,QAAMC,UAAS;AAAA,IACZ,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,qBAAqB,QAAQ;AAAA,EAChC;AAEA,SACG,wBAAwB,QAAQ,KAAK;AAAA,IAClC;AAAA,IACA,QAAQ;AAAA,IACR,QAAAA;AAAA,EACH;AAEN;AA3BA;AAAA;AAAA;AACA;AACA;AAEA;AACA;AAAA;AAAA;;;ACLA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAGO,SAAS,iBAAiB,MAAc,MAAkC;AAC9E,SAAO,cAAc,CAAC,OAAO,MAAM,IAAI,CAAC;AAC3C;AAEO,SAAS,kBAAkB,YAA0C;AACzE,SAAO,cAAc,CAAC,QAAQ,GAAG,UAAU,CAAC;AAC/C;AAEO,SAAS,cAAc,YAA0C;AACrE,QAAM,WAAW,CAAC,GAAG,UAAU;AAC/B,MAAI,SAAS,OAAO,aAAa;AAC9B,aAAS,QAAQ,WAAW;AAAA,EAC/B;AAEA,SAAO,0BAA0B,QAAQ;AAC5C;AAEO,SAAS,oBAAoB,YAA0C;AAC3E,SAAO,cAAc,CAAC,UAAU,GAAG,UAAU,CAAC;AACjD;AAtBA;AAAA;AAAA;AACA;AAAA;AAAA;;;ACqCA,SAAS,aAAa,GAAW,GAAmB;AACjD,QAAM,SAAS,MAAM,CAAC;AACtB,QAAM,SAAS,MAAM,CAAC;AAEtB,MAAI,WAAW,QAAQ;AACpB,WAAO,SAAS,IAAI;AAAA,EACvB;AAEA,SAAO,SAAS,OAAO,GAAG,CAAC,IAAI;AAClC;AAEA,SAAS,OAAO,GAAW,GAAW;AACnC,SAAO,MAAM,IAAI,IAAI,IAAI,IAAI,IAAI;AACpC;AAEA,SAAS,QAAQ,OAAe;AAC7B,SAAO,MAAM,KAAK;AACrB;AAEA,SAAS,SAAS,OAA2B;AAC1C,MAAI,OAAO,UAAU,UAAU;AAC5B,WAAO,SAAS,MAAM,QAAQ,SAAS,EAAE,GAAG,EAAE,KAAK;AAAA,EACtD;AAEA,SAAO;AACV;AA/DA,IAEa,SAOA;AATb;AAAA;AAAA;AAEO,IAAM,UAAN,MAAmC;AAAA,MACvC,YACmB,KACA,QACjB;AAFiB;AACA;AAAA,MAChB;AAAA,IACN;AAEO,IAAM,eAAe,SAAU,MAAc,aAAa,OAAO;AACrE,YAAM,OAAO,KAAK,MAAM,IAAI,EAAE,IAAI,OAAO,EAAE,OAAO,OAAO;AAEzD,UAAI,CAAC,YAAY;AACd,aAAK,KAAK,SAAU,MAAM,MAAM;AAC7B,gBAAM,SAAS,KAAK,MAAM,GAAG;AAC7B,gBAAM,SAAS,KAAK,MAAM,GAAG;AAE7B,cAAI,OAAO,WAAW,KAAK,OAAO,WAAW,GAAG;AAC7C,mBAAO,aAAa,SAAS,OAAO,EAAE,GAAG,SAAS,OAAO,EAAE,CAAC;AAAA,UAC/D;AAEA,mBAAS,IAAI,GAAG,IAAI,KAAK,IAAI,OAAO,QAAQ,OAAO,MAAM,GAAG,IAAI,GAAG,KAAK;AACrE,kBAAM,OAAO,OAAO,SAAS,OAAO,EAAE,GAAG,SAAS,OAAO,EAAE,CAAC;AAE5D,gBAAI,MAAM;AACP,qBAAO;AAAA,YACV;AAAA,UACH;AAEA,iBAAO;AAAA,QACV,CAAC;AAAA,MACJ;AAEA,YAAM,SAAS,aAAa,KAAK,KAAK,CAAC,GAAG,IAAI,EAAE,QAAQ,EAAE,KAAK,CAAC,QAAQ,IAAI,QAAQ,GAAG,KAAK,CAAC;AAE7F,aAAO,IAAI,QAAQ,MAAM,MAAM;AAAA,IAClC;AAAA;AAAA;;;ACpCA;AAAA;AAAA;AAAA;AAAA;AAAA;AAOO,SAAS,YAAY,aAAuB,CAAC,GAA0B;AAC3E,QAAM,gBAAgB,WAAW,KAAK,CAAC,WAAW,WAAW,KAAK,MAAM,CAAC;AAEzE,SAAO;AAAA,IACJ,QAAQ;AAAA,IACR,UAAU,CAAC,OAAO,MAAM,GAAG,UAAU;AAAA,IACrC,OAAO,MAAc;AAClB,aAAO,aAAa,MAAM,aAAa;AAAA,IAC1C;AAAA,EACH;AACH;AAKO,SAAS,WAAW,MAA4C;AACpE,SAAO;AAAA,IACJ,QAAQ;AAAA,IACR,UAAU,CAAC,OAAO,IAAI;AAAA,IACtB,SAAS;AACN,aAAO,EAAE,KAAK;AAAA,IACjB;AAAA,EACH;AACH;AAKO,SAAS,oBACb,MACA,YAC6B;AAC7B,SAAO;AAAA,IACJ,QAAQ;AAAA,IACR,UAAU,CAAC,OAAO,MAAM,MAAM,YAAY,IAAI;AAAA,IAC9C,SAAS;AACN,aAAO,EAAE,KAAK;AAAA,IACjB;AAAA,EACH;AACH;AA9CA;AAAA;AAAA;AACA;AAAA;AAAA;;;ACDA;AAAA;AAAA;AAAA,QAAM,EAAE,aAAAC,aAAY,IAAI;AACxB,QAAM,EAAE,cAAAC,cAAa,IAAI;AAEzB,QAAM,EAAE,WAAAC,WAAU,IAAI;AACtB,QAAM,EAAE,wBAAAC,wBAAuB,IAAI;AACnC,QAAM;AAAA,MACH,SAAAC;AAAA,MACA,aAAAC;AAAA,MACA,kBAAAC;AAAA,MACA,cAAAC;AAAA,MACA,2BAAAC;AAAA,MACA,YAAAC;AAAA,MACA,oBAAAC;AAAA,MACA,0BAAAC;AAAA,MACA,yBAAAC;AAAA,IACH,IAAI;AACJ,QAAM,EAAE,gBAAAC,gBAAe,IAAI;AAC3B,QAAM;AAAA,MACH,YAAAC;AAAA,MACA,iBAAAC;AAAA,MACA,oBAAAC;AAAA,MACA,kBAAAC;AAAA,IACH,IAAI;AACJ,QAAM,EAAE,iBAAAC,iBAAgB,IAAI;AAC5B,QAAM,EAAE,iBAAAC,iBAAgB,IAAI;AAC5B,QAAM,EAAE,WAAAC,YAAW,iBAAAC,iBAAgB,IAAI;AACvC,QAAM,EAAE,sBAAAC,uBAAsB,qBAAAC,qBAAoB,IAAI;AACtD,QAAM,EAAE,iBAAAC,iBAAgB,IAAI;AAC5B,QAAM,EAAE,WAAAC,WAAU,IAAI;AACtB,QAAM,EAAE,UAAAC,UAAS,IAAI;AACrB,QAAM,EAAE,UAAAC,UAAS,IAAI;AACrB,QAAM,EAAE,cAAAC,cAAa,IAAI;AACzB,QAAM;AAAA,MACH,eAAAC;AAAA,MACA,gBAAAC;AAAA,MACA,iBAAAC;AAAA,MACA,YAAAC;AAAA,MACA,kBAAAC;AAAA,IACH,IAAI;AACJ,QAAM,EAAE,cAAAC,eAAc,WAAAC,WAAU,IAAI;AACpC,QAAM,EAAE,eAAAC,eAAc,IAAI;AAC1B,QAAM;AAAA,MACH,kBAAAC;AAAA,MACA,mBAAAC;AAAA,MACA,eAAAC;AAAA,MACA,qBAAAC;AAAA,IACH,IAAI;AACJ,QAAM,EAAE,qBAAAC,sBAAqB,YAAAC,aAAY,aAAAC,aAAY,IAAI;AACzD,QAAM,EAAE,2BAAAC,4BAA2B,2BAAAC,2BAA0B,IAAI;AAEjE,aAASC,KAAI,SAAS,SAAS;AAC5B,WAAK,WAAW;AAChB,WAAK,YAAY,IAAI9C;AAAA,QAClB,QAAQ;AAAA,QACR,IAAIE,WAAU,QAAQ,sBAAsB;AAAA,QAC5C;AAAA,MACH;AAEA,WAAK,WAAW,QAAQ;AAAA,IAC3B;AAEA,KAAC4C,KAAI,YAAY,OAAO,OAAO7C,cAAa,SAAS,GAAG,cAAc6C;AAMtE,IAAAA,KAAI,UAAU,eAAe,SAAU,SAAS;AAC7C,WAAK,SAAS,YAAY,UAAU,OAAO;AAC3C,aAAO;AAAA,IACV;AAUA,IAAAA,KAAI,UAAU,MAAM,SAAU,MAAM,OAAO;AACxC,UAAI,UAAU,WAAW,KAAK,OAAO,SAAS,UAAU;AACrD,aAAK,UAAU,MAAM;AAAA,MACxB,OAAO;AACJ,SAAC,KAAK,UAAU,MAAM,KAAK,UAAU,OAAO,CAAC,GAAG,QAAQ;AAAA,MAC3D;AAEA,aAAO;AAAA,IACV;AAKA,IAAAA,KAAI,UAAU,YAAY,SAAU,SAAS;AAC1C,aAAO,KAAK;AAAA,QACTV;AAAA,UACGxB,yBAAwB,SAAS,KAAK,CAAC;AAAA,UACtCP,aAAY,OAAO,KAAK,WAAY,CAAC;AAAA,QACzC;AAAA,QACAM,0BAAyB,SAAS;AAAA,MACrC;AAAA,IACH;AAEA,aAAS,gBAAgB,KAAK,MAAM,UAAU,WAAW;AACtD,UAAI,OAAO,aAAa,UAAU;AAC/B,eAAOR,wBAAuB,OAAO,oCAAoC;AAAA,MAC5E;AAEA,aAAO,KAAK,UAAUM,YAAW,WAAWF,aAAY,GAAGG,oBAAmB,SAAS,CAAC;AAAA,IAC3F;AAKA,IAAAoC,KAAI,UAAU,QAAQ,WAAY;AAC/B,aAAO,KAAK;AAAA,QACT,gBAAgB,SAAS1B,YAAW,GAAG,SAAS;AAAA,QAChDT,0BAAyB,SAAS;AAAA,MACrC;AAAA,IACH;AAKA,IAAAmC,KAAI,UAAU,SAAS,WAAY;AAChC,aAAO,KAAK;AAAA,QACT,gBAAgB,UAAUzB,kBAAiB,GAAG,SAAS;AAAA,QACvDV,0BAAyB,SAAS;AAAA,MACrC;AAAA,IACH;AAUA,IAAAmC,KAAI,UAAU,KAAK,SAAU,MAAM,IAAI;AACpC,aAAO,KAAK,SAASpB,UAAS,MAAM,EAAE,GAAGf,0BAAyB,SAAS,CAAC;AAAA,IAC/E;AAOA,IAAAmC,KAAI,UAAU,oBAAoB,SAAU,MAAM;AAC/C,UAAI,MAAM;AACV,aAAO,KAAK,KAAK,WAAY;AAC1B,YAAI,KAAK,SAAU,KAAK,MAAM;AAC3B,cAAI,SAAS,KAAK,QAAQ,IAAI;AAAA,QACjC,CAAC;AAAA,MACJ,CAAC;AAAA,IACJ;AAKA,IAAAA,KAAI,UAAU,OAAO,SAAU,QAAQ,QAAQ,SAAS,MAAM;AAC3D,aAAO,KAAK;AAAA,QACTnB;AAAA,UACGlB,YAAW,QAAQF,aAAY;AAAA,UAC/BE,YAAW,QAAQF,aAAY;AAAA,UAC/BG,oBAAmB,SAAS;AAAA,QAC/B;AAAA,QACAC,0BAAyB,SAAS;AAAA,MACrC;AAAA,IACH;AAYA,IAAAmC,KAAI,UAAU,QAAQ,SAAU,QAAQ,QAAQ;AAC7C,aAAO,KAAK;AAAA,QACTrB;AAAA,UACGhB,YAAW,QAAQF,aAAY;AAAA,UAC/BE,YAAW,QAAQF,aAAY;AAAA,UAC/BG,oBAAmB,SAAS;AAAA,QAC/B;AAAA,QACAC,0BAAyB,SAAS;AAAA,MACrC;AAAA,IACH;AASA,IAAAmC,KAAI,UAAU,SAAS,SAAU,SAAS;AACvC,cAAQ;AAAA,QACL;AAAA,MACH;AACA,aAAO;AAAA,IACV;AAWA,IAAAA,KAAI,UAAU,OAAO,SAAU,SAAS,MAAM;AAC3C,aAAO,KAAK;AAAA,QACTH,aAAYjC,oBAAmB,SAAS,CAAC;AAAA,QACzCC,0BAAyB,SAAS;AAAA,MACrC;AAAA,IACH;AAMA,IAAAmC,KAAI,UAAU,SAAS,WAAY;AAChC,aAAO,KAAK;AAAA,QACTD,2BAA0B,CAAC,UAAU,GAAGnC,oBAAmB,SAAS,CAAC,CAAC;AAAA,QACtEC,0BAAyB,SAAS;AAAA,MACrC;AAAA,IACH;AAKA,IAAAmC,KAAI,UAAU,QAAQ,SAAU,MAAM;AACnC,aAAO,KAAK;AAAA,QACTX,WAAUD,cAAa,IAAI,GAAGxB,oBAAmB,SAAS,CAAC;AAAA,QAC3DC,0BAAyB,SAAS;AAAA,MACrC;AAAA,IACH;AAKA,IAAAmC,KAAI,UAAU,SAAS,SAAU,QAAQ;AACtC,YAAM,OAAOnC,0BAAyB,SAAS;AAE/C,UAAI,OAAO,WAAW,UAAU;AAC7B,eAAO,KAAK,SAASR,wBAAuB,yBAAyB,GAAG,IAAI;AAAA,MAC/E;AAEA,aAAO,KAAK;AAAA,QACT0C,2BAA0B,CAAC,UAAU,GAAGnC,oBAAmB,WAAW,GAAG,IAAI,GAAG,MAAM,CAAC;AAAA,QACvF;AAAA,MACH;AAAA,IACH;AAKA,IAAAoC,KAAI,UAAU,SAAS,SAAU,MAAM;AACpC,YAAM,OACH,OAAO,SAAS,WACXJ,YAAW,IAAI,IACfvC,wBAAuB,gCAAgC;AAE/D,aAAO,KAAK,SAAS,MAAMQ,0BAAyB,SAAS,CAAC;AAAA,IACjE;AAKA,IAAAmC,KAAI,UAAU,kBAAkB,SAAU,SAAS,YAAY;AAC5D,aAAO,KAAK;AAAA,QACTL,qBAAoB,SAAS,UAAU;AAAA,QACvC9B,0BAAyB,SAAS;AAAA,MACrC;AAAA,IACH;AAKA,IAAAmC,KAAI,UAAU,oBAAoB,SAAU,YAAY,aAAa,MAAM;AACxE,aAAO,KAAK;AAAA,QACT7B,kBAAiB,YAAY,OAAO,gBAAgB,YAAY,cAAc,KAAK;AAAA,QACnFN,0BAAyB,SAAS;AAAA,MACrC;AAAA,IACH;AAKA,IAAAmC,KAAI,UAAU,sBAAsB,SAAU,aAAa,aAAa,MAAM;AAC3E,aAAO,KAAK;AAAA,QACT9B,oBAAmB,aAAa,OAAO,gBAAgB,YAAY,cAAc,KAAK;AAAA,QACtFL,0BAAyB,SAAS;AAAA,MACrC;AAAA,IACH;AAQA,IAAAmC,KAAI,UAAU,SAAS,SAAU,SAAS,MAAM;AAC7C,aAAO,KAAK;AAAA,QACThC,YAAWJ,oBAAmB,SAAS,CAAC;AAAA,QACxCC,0BAAyB,SAAS;AAAA,MACrC;AAAA,IACH;AAOA,IAAAmC,KAAI,UAAU,cAAc,SAAU,MAAM;AACzC,aAAO,KAAK,SAAS/B,iBAAgB,GAAGJ,0BAAyB,SAAS,CAAC;AAAA,IAC9E;AAKA,IAAAmC,KAAI,UAAU,MAAM,SAAU,UAAU;AACrC,YAAM,qBAAqB,CAAC,MAAM,QAAQ,QAAQ;AAClD,YAAM,UAAU,CAAC,EAAE,MAAM,KAAK,qBAAqB,YAAY,UAAU,CAAC;AAE1E,eAAS,IAAI,GAAG,IAAI,QAAQ,UAAU,oBAAoB,KAAK;AAC5D,YAAI,CAACxC,kBAAiB,QAAQ,EAAE,GAAG;AAChC,kBAAQ,OAAO,GAAG,QAAQ,SAAS,CAAC;AACpC;AAAA,QACH;AAAA,MACH;AAEA,cAAQ,KAAK,GAAGI,oBAAmB,WAAW,GAAG,IAAI,CAAC;AAEtD,UAAI,OAAOC,0BAAyB,SAAS;AAE7C,UAAI,CAAC,QAAQ,QAAQ;AAClB,eAAO,KAAK;AAAA,UACTR,wBAAuB,iDAAiD;AAAA,UACxE;AAAA,QACH;AAAA,MACH;AAEA,aAAO,KAAK,SAAS0C,2BAA0B,SAAS,KAAK,QAAQ,GAAG,IAAI;AAAA,IAC/E;AAEA,IAAAC,KAAI,UAAU,eAAe,SAAU,MAAM,MAAM,MAAM;AACtD,aAAO,KAAK,SAAST,kBAAiB,MAAM,IAAI,GAAG1B,0BAAyB,SAAS,CAAC;AAAA,IACzF;AAEA,IAAAmC,KAAI,UAAU,kBAAkB,SAAU,MAAM,MAAM;AACnD,aAAO,KAAK;AAAA,QACTN,qBAAoB9B,oBAAmB,WAAW,IAAI,CAAC;AAAA,QACvDC,0BAAyB,SAAS;AAAA,MACrC;AAAA,IACH;AAEA,IAAAmC,KAAI,UAAU,gBAAgB,SAAU,MAAM,MAAM;AACjD,aAAO,KAAK;AAAA,QACTR,mBAAkB5B,oBAAmB,WAAW,IAAI,CAAC;AAAA,QACrDC,0BAAyB,SAAS;AAAA,MACrC;AAAA,IACH;AAEA,IAAAmC,KAAI,UAAU,YAAY,SAAU,SAAS,MAAM;AAChD,aAAO,KAAK;AAAA,QACTP,eAAc7B,oBAAmB,SAAS,CAAC;AAAA,QAC3CC,0BAAyB,SAAS;AAAA,MACrC;AAAA,IACH;AAEA,IAAAmC,KAAI,UAAU,aAAa,WAAY;AACpC,aAAO,KAAK;AAAA,QACTf,iBAAgBrB,oBAAmB,SAAS,CAAC;AAAA,QAC7CC,0BAAyB,SAAS;AAAA,MACrC;AAAA,IACH;AAKA,IAAAmC,KAAI,UAAU,YAAY,SAAU,YAAY,YAAY,MAAM;AAC/D,aAAO,KAAK;AAAA,QACTjB,eAAc,YAAY,YAAYnB,oBAAmB,SAAS,CAAC;AAAA,QACnEC,0BAAyB,SAAS;AAAA,MACrC;AAAA,IACH;AAKA,IAAAmC,KAAI,UAAU,eAAe,SAAU,YAAY,MAAM;AACtD,aAAO,KAAK,SAASb,kBAAiB,UAAU,GAAGtB,0BAAyB,SAAS,CAAC;AAAA,IACzF;AAMA,IAAAmC,KAAI,UAAU,aAAa,SAAU,SAAS,MAAM;AACjD,aAAO,KAAK,SAAShB,gBAAe,YAAY,IAAI,GAAGnB,0BAAyB,SAAS,CAAC;AAAA,IAC7F;AAQA,IAAAmC,KAAI,UAAU,SAAS,SAAU,SAAS,MAAM;AAC7C,aAAO,KAAK;AAAA,QACTd,YAAWtB,oBAAmB,SAAS,CAAC;AAAA,QACxCC,0BAAyB,SAAS;AAAA,MACrC;AAAA,IACH;AAQA,IAAAmC,KAAI,UAAU,MAAM,SAAU,SAAS,MAAM;AAC1C,YAAM,UAAUpC,oBAAmB,SAAS;AAE5C,UAAI,QAAQ,OAAO,OAAO;AACvB,gBAAQ,QAAQ,KAAK;AAAA,MACxB;AAEA,aAAO,KAAK,SAASmC,2BAA0B,OAAO,GAAGlC,0BAAyB,SAAS,CAAC;AAAA,IAC/F;AAOA,IAAAmC,KAAI,UAAU,mBAAmB,SAAU,MAAM;AAC9C,aAAO,KAAK;AAAA,QACTD,2BAA0B,CAAC,oBAAoB,CAAC;AAAA,QAChDlC,0BAAyB,SAAS;AAAA,MACrC;AAAA,IACH;AASA,IAAAmC,KAAI,UAAU,WAAW,SAAU,QAAQ,MAAM;AAC9C,YAAM,OAAOlB;AAAA,QACV,EAAE,QAAQnB,YAAW,QAAQF,aAAY,EAAE;AAAA,QAC3CG,oBAAmB,SAAS;AAAA,MAC/B;AAEA,aAAO,KAAK,SAAS,MAAMC,0BAAyB,SAAS,CAAC;AAAA,IACjE;AAKA,IAAAmC,KAAI,UAAU,KAAK,SAAU,OAAO;AACjC,aAAO,KAAK;AAAA,QACTD,2BAA0B,CAAC,MAAM,MAAM,GAAGzC,SAAQ,KAAK,CAAC,CAAC;AAAA,QACzDO,0BAAyB,SAAS;AAAA,MACrC;AAAA,IACH;AAQA,IAAAmC,KAAI,UAAU,cAAc,SAAU,OAAO;AAC1C,aAAO,KAAK;AAAA,QACTD,2BAA0B,CAAC,MAAM,YAAY,GAAGzC,SAAQ,KAAK,CAAC,CAAC;AAAA,QAC/DO,0BAAyB,SAAS;AAAA,MACrC;AAAA,IACH;AAWA,IAAAmC,KAAI,UAAU,UAAU,SAAU,SAAS,MAAM;AAC9C,aAAO,KAAK,SAAS,SAAS,SAAS;AAAA,IAC1C;AAEA,IAAAA,KAAI,UAAU,gBAAgB,WAAY;AACvC,aAAO,KAAK,SAAS,UAAU,SAAS;AAAA,IAC3C;AAEA,IAAAA,KAAI,UAAU,WAAW,SAAU,QAAQ,MAAM;AAC9C,UAAI,UAAUnC,0BAAyB,IAAI;AAC3C,UAAI,UAAU,CAAC,UAAU;AACzB,UAAI,UAAU,KAAK;AAEnB,UAAI,OAAO,YAAY,UAAU;AAC9B,eAAO,KAAK;AAAA,UACTR,wBAAuB,8DAA8D;AAAA,UACrF;AAAA,QACH;AAAA,MACH;AAEA,UAAI,MAAM,QAAQ,OAAO,GAAG;AACzB,gBAAQ,KAAK,MAAM,SAAS,OAAO;AAAA,MACtC;AAEA,YAAM,OACH,WAAW,WAAWyC,2BAA0B,OAAO,IAAIC,2BAA0B,OAAO;AAE/F,aAAO,KAAK,SAAS,MAAM,OAAO;AAAA,IACrC;AAEA,IAAAC,KAAI,UAAU,OAAO,SAAU,SAAS,MAAM;AAC3C,YAAM,OAAOvC,cAAa,OAAO,IAC5BJ;AAAA,QACG;AAAA,MACH,IACA0C,2BAA0B,CAAC,QAAQ,GAAGnC,oBAAmB,SAAS,CAAC,CAAC;AAEzE,aAAO,KAAK,SAAS,MAAMC,0BAAyB,SAAS,CAAC;AAAA,IACjE;AAEA,IAAAmC,KAAI,UAAU,cAAc,WAAY;AACrC,aAAO,KAAK;AAAA,QACTtB,iBAAgBd,oBAAmB,WAAW,CAAC,CAAC;AAAA,QAChDC,0BAAyB,SAAS;AAAA,MACrC;AAAA,IACH;AAEA,IAAAmC,KAAI,UAAU,aAAa,SAAU,SAAS;AAC3C,YAAM,OAAO,CAACtC,2BAA0B,OAAO,IAC1CL;AAAA,QACG;AAAA,MACH,IACAU,gBAAeT,SAAQ,OAAO,GAAGM,oBAAmB,CAAC,EAAE,MAAM,KAAK,WAAW,CAAC,CAAC,CAAC;AAErF,aAAO,KAAK,SAAS,MAAMC,0BAAyB,SAAS,CAAC;AAAA,IACjE;AAEA,IAAAmC,KAAI,UAAU,WAAW,WAAY;AAClC,YAAM,WAAW,CAAC,aAAa,GAAGpC,oBAAmB,WAAW,IAAI,CAAC;AACrE,aAAO,KAAK;AAAA,QACTmC,2BAA0B,UAAU,IAAI;AAAA,QACxClC,0BAAyB,SAAS;AAAA,MACrC;AAAA,IACH;AAIA,IAAAmC,KAAI,UAAU,QAAQ,SAAU,MAAM,SAAS,MAAM;AAClD,YAAM,yBAAyBvB,qBAAoB,IAAI;AACvD,YAAM,YACF,0BAA0B,KAAK,KAAK,EAAE,KAAMd,YAAW,MAAMF,aAAY,KAAK;AAClF,YAAM,aAAaG,oBAAmB,CAAC,EAAE,MAAM,KAAK,WAAW,yBAAyB,IAAI,CAAC,CAAC;AAE9F,aAAO,KAAK;AAAA,QACTY,sBAAqB,WAAW,UAAU;AAAA,QAC1CX,0BAAyB,SAAS;AAAA,MACrC;AAAA,IACH;AAEA,IAAAmC,KAAI,UAAU,OAAO,SAAU,MAAM;AAClC,YAAM,OAAO;AAAA,QACV,UAAU,CAAC;AAAA,QACX,QAAQ;AAAA,QACR,SAAS;AACN,cAAI,OAAO,SAAS,YAAY;AAC7B,iBAAK;AAAA,UACR;AAAA,QACH;AAAA,MACH;AAEA,aAAO,KAAK,SAAS,IAAI;AAAA,IAC5B;AAOA,IAAAA,KAAI,UAAU,aAAa,WAAY;AAGpC,aAAO;AAAA,IACV;AAQA,IAAAA,KAAI,UAAU,cAAc,SAAU,WAAW,MAAM;AACpD,aAAO,KAAK;AAAA,QACT5B,iBAAgBd,SAAQK,YAAW,WAAWD,4BAA2B,CAAC,CAAC,CAAC,CAAC;AAAA,QAC7EG,0BAAyB,SAAS;AAAA,MACrC;AAAA,IACH;AAEA,IAAAmC,KAAI,UAAU,cAAc,SAAU,WAAW,MAAM;AACpD,aAAO,KAAK;AAAA,QACT3B,iBAAgBV,YAAW,WAAWF,aAAY,CAAC;AAAA,QACnDI,0BAAyB,SAAS;AAAA,MACrC;AAAA,IACH;AAEA,WAAO,UAAUmC;AAAA;AAAA;;;AC7mBjB;;;ACAA;AAYO,IAAM,oBAAN,cAAgC,SAAS;AAAA,EAC7C,YACmB,QAChB,SACD;AACC,UAAM,QAAW,OAAO;AAHR;AAAA,EAInB;AACH;;;ADjBA;;;AEDA;AAEO,IAAM,iBAAN,cAA6B,SAAS;AAAA,EAC1C,YACU,MACS,QAChB,SACD;AACC,UAAM,MAAM,OAAO;AAJZ;AACS;AAIhB,WAAO,eAAe,MAAM,WAAW,SAAS;AAAA,EACnD;AACH;;;AFRA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AGPO,SAAS,YAAY,QAAmC;AAC5D,MAAI,CAAC,QAAQ;AACV;AAAA,EACH;AAEA,QAAM,eAA+C;AAAA,IAClD,MAAM;AAAA,IACN,OAAO,OAAO,SAAS;AACpB,eAAS,OAAO;AACb,gBAAQ,KAAK,IAAI,eAAe,QAAW,SAAS,uBAAuB,CAAC;AAAA,MAC/E;AAEA,aAAO,iBAAiB,SAAS,IAAI;AAErC,cAAQ,QAAQ,GAAG,SAAS,MAAM,OAAO,oBAAoB,SAAS,IAAI,CAAC;AAAA,IAC9E;AAAA,EACH;AAEA,QAAM,gBAAiD;AAAA,IACpD,MAAM;AAAA,IACN,OAAO,OAAO,SAAS;AACpB,UAAI,OAAO,SAAS;AACjB,gBAAQ,KAAK,IAAI,eAAe,QAAW,SAAS,wBAAwB,CAAC;AAAA,MAChF;AAAA,IACH;AAAA,EACH;AAEA,SAAO,CAAC,eAAe,YAAY;AACtC;;;AC3BA,SAAS,eAAe,KAAuB;AAC5C,SAAO,OAAO,QAAQ,YAAY,IAAI,KAAK,EAAE,YAAY,MAAM;AAClE;AAEA,SAAS,wBAAwB,KAAa,MAAc;AACzD,MAAI,CAAC,eAAe,GAAG,GAAG;AACvB;AAAA,EACH;AAEA,MAAI,CAAC,+BAA+B,KAAK,IAAI,GAAG;AAC7C;AAAA,EACH;AAEA,QAAM,IAAI;AAAA,IACP;AAAA,IACA;AAAA,IACA;AAAA,EACH;AACH;AAEA,SAAS,kBAAkB,KAAa,QAAgB;AACrD,MAAI,8BAA8B,KAAK,GAAG,GAAG;AAC1C,UAAM,IAAI;AAAA,MACP;AAAA,MACA;AAAA,MACA;AAAA,IACH;AAAA,EACH;AAEA,MAAI,WAAW,WAAW,WAAW,KAAK,GAAG,GAAG;AAC7C,UAAM,IAAI;AAAA,MACP;AAAA,MACA;AAAA,MACA;AAAA,IACH;AAAA,EACH;AAEA,MAAI,WAAW,UAAU,eAAe,KAAK,GAAG,GAAG;AAChD,UAAM,IAAI;AAAA,MACP;AAAA,MACA;AAAA,MACA;AAAA,IACH;AAAA,EACH;AACH;AAEO,SAAS,4BAA4B;AAAA,EACzC,8BAA8B;AAAA,EAC9B,kBAAkB;AACrB,IAAqC,CAAC,GAAkC;AACrE,SAAO;AAAA,IACJ,MAAM;AAAA,IACN,OAAO,MAAM,SAAS;AACnB,WAAK,QAAQ,CAAC,SAAS,UAAU;AAC9B,cAAM,OAAO,QAAQ,KAAK,SAAS,KAAK,QAAQ,KAAK;AAErD,uCAA+B,wBAAwB,SAAS,IAAI;AACpE,2BAAmB,kBAAkB,SAAS,QAAQ,MAAM;AAAA,MAC/D,CAAC;AAED,aAAO;AAAA,IACV;AAAA,EACH;AACH;;;ACpEA;AAGO,SAAS,6BACb,eAC8B;AAC9B,QAAM,SAAS,cAAc,eAAe,IAAI;AAEhD,SAAO;AAAA,IACJ,MAAM;AAAA,IACN,OAAO,MAAM;AACV,aAAO,CAAC,GAAG,QAAQ,GAAG,IAAI;AAAA,IAC7B;AAAA,EACH;AACH;;;ACZA;AAFA,SAAS,gBAAiC;AAK1C,IAAM,QAAQ,SAAS,EAAE;AAElB,SAAS,0BAA0B;AAAA,EACvC,UAAU;AAAA,EACV,SAAS;AACZ,IAAyC,CAAC,GAAmC;AAC1E,WAAS,eAAe;AACrB,QAAI,WAAW;AACf,UAAM,SAAS;AAAA,MACZ,OAAO,SAAS;AAAA,MAChB,cAAc,SAAS;AAAA,MACvB,MAAM,SAAS;AAAA,MACf,aAAa,SAAS;AAAA,IACzB;AAEA,UAAM,SAAS,QAAQ,KAAK;AAAA,MACzB,YAAY,QAAQ,QAAQ,OAAO,aAAa;AAAA,MAChD,WAAW,QAAQ,QAAQ,OAAO,YAAY;AAAA,IACjD,CAAC;AAED,qBAAiB,SAAS,OAAO,OAAO,OAAO,YAAY;AAC3D,qBAAiB,QAAQ,OAAO,MAAM,OAAO,WAAW;AAExD,WAAO;AAAA,MACJ,MAAM,MAAc;AACjB,mBAAW;AACX,eAAO,MAAM,KAAK;AAAA,MACrB;AAAA,MACA,KAAK,MAAc;AAChB,mBAAW;AACX,eAAO,KAAK,KAAK;AAAA,MACpB;AAAA,MACA,IAAI,WAAW;AACZ,eAAO;AAAA,MACV;AAAA,MACA;AAAA,IACH;AAAA,EACH;AAEA,WAAS,iBACN,MACA,OACA,SACD;AACC,QAAI,SAAS,OAAO;AACjB;AAAA,IACH;AAEA,KAAC,SAAS,OAAO,MAAM,UAAU,MAAM,QAAQ,KAAK,MAAM,MAAM,IAAI,CAAC,GAAG,KAAK,QAAQ,IAAI;AAAA,EAC5F;AAEA,SAAO;AAAA,IACJ,MAAM;AAAA,IACA,OAAO,IAAO,IAAoB;AAAA,iDAA3B,OAAO,EAAE,SAAS,MAAM,GAAG;AA1D9C,YAAAC,KAAA;AA2DS,cAAM,SAAS,aAAa;AAE5B,YAAI,aAAa;AACjB,YAAI,aAAa,MAAM,MAAM,aAAa;AAE1C,SAAAA,MAAA,QAAQ,WAAR,gBAAAA,IAAgB,GAAG,QAAQ;AAC3B,sBAAQ,WAAR,mBAAgB,GAAG,QAAQ;AAC3B,gBAAQ,GAAG,SAAS,UAAU;AAE9B,gBAAQ,GAAG,SAAS,CAAC,SAAiB,OAAO,MAAM,IAAI,CAAC;AACxD,gBAAQ,GAAG,QAAQ,CAAC,SAAiB,OAAO,KAAK,IAAI,CAAC;AAEtD,YAAI;AACD,gBAAM,OAAO;AACb,cAAI,YAAY;AACb,kBAAM,MAAM,EAAE;AAAA,UACjB;AACA,gBAAM,OAAO,QAAQ;AAAA,QACxB,SAAS,KAAP;AACC,gBAAM,OAAO,UAAU,GAAY;AAAA,QACtC;AAAA,MACH;AAAA;AAAA,EACH;AACH;;;AC/EA;AAGA,IAAM,mBAAmB;AACzB,IAAM,kBAAkB;AAExB,SAAS,cAAc,KAAa;AACjC,SAAO,CAAC,OAAO,CAAC,gCAAgC,KAAK,GAAG;AAC3D;AAEA,SAAS,eACN,OACA,aACoC;AACpC,MAAI,MAAM,SAAS,KAAK,MAAM,SAAS,GAAG;AACvC,UAAM,IAAI,eAAe,QAAW,UAAU,gBAAgB;AAAA,EACjE;AAEA,QAAM,QAAQ,MAAM,KAAK,aAAa;AACtC,MAAI,OAAO;AACR,QAAI,aAAa;AACd,cAAQ,KAAK,eAAe;AAAA,IAC/B,OAAO;AACJ,YAAM,IAAI,eAAe,QAAW,UAAU,eAAe;AAAA,IAChE;AAAA,EACH;AAEA,QAAM,CAAC,QAAQ,MAAM,IAAI;AACzB,SAAO;AAAA,IACJ;AAAA,IACA;AAAA,EACH;AACH;AAEO,SAAS,mBACb,SACA,QAAoC,CAAC,KAAK,GAC1C,cAAc,OACf;AACC,MAAI,SAAS,eAAe,QAAQ,KAAK,GAAG,WAAW;AAEvD,UAAQ,GAAG,UAAU,CAACC,WAAU;AAC7B,aAAS,eAAe,QAAQA,MAAK,GAAG,WAAW;AAAA,EACtD,CAAC;AAED,UAAQ,OAAO,gBAAgB,MAAM;AAClC,WAAO,OAAO;AAAA,EACjB,CAAC;AAED,UAAQ,OAAO,cAAc,CAAC,SAAS;AACpC,WAAO,OAAO,SAAS,CAAC,OAAO,QAAQ,GAAG,IAAI,IAAI;AAAA,EACrD,CAAC;AACJ;;;ACvDA;AAMA,SAAS,YAAY,QAAoB;AACtC,SAAO,CAAC,EAAE,OAAO,YAAY,OAAO,OAAO;AAC9C;AAEA,SAAS,gBAAgB,QAAoB;AAC1C,SAAO,OAAO,OAAO,CAAC,GAAG,OAAO,QAAQ,GAAG,OAAO,MAAM,CAAC;AAC5D;AAEO,SAAS,sBACb,YAAY,OACZ,UAAU,aACV,eAAuD,iBACxD;AACC,SAAO,CAAC,OAAmC,WAAuB;AAC/D,QAAK,CAAC,aAAa,SAAU,CAAC,QAAQ,MAAM,GAAG;AAC5C,aAAO;AAAA,IACV;AAEA,WAAO,aAAa,MAAM;AAAA,EAC7B;AACH;AAEO,SAAS,qBACb,QAC8B;AAC9B,SAAO;AAAA,IACJ,MAAM;AAAA,IACN,OAAO,MAAM,SAAS;AACnB,YAAM,QAAQ,OAAO,KAAK,OAAO;AAAA,QAC9B,QAAQ,QAAQ;AAAA,QAChB,QAAQ,QAAQ;AAAA,QAChB,UAAU,QAAQ;AAAA,MACrB,CAAC;AAED,UAAI,OAAO,SAAS,KAAK,GAAG;AACzB,eAAO,EAAE,OAAO,IAAI,SAAS,QAAW,MAAM,SAAS,OAAO,CAAC,EAAE;AAAA,MACpE;AAEA,aAAO;AAAA,QACJ;AAAA,MACH;AAAA,IACH;AAAA,EACH;AACH;;;AC1CA;AAPA,SAAS,oBAAoB;AAUtB,IAAM,cAAN,MAAkB;AAAA,EAAlB;AACJ,SAAQ,UAAqD,oBAAI,IAAI;AACrE,SAAQ,SAAS,IAAI,aAAa;AAAA;AAAA,EAElC,GACG,MACA,UACD;AACC,SAAK,OAAO,GAAG,MAAM,QAAQ;AAAA,EAChC;AAAA,EAEA,YAAmD,MAAS,MAAgC;AACzF,SAAK,OAAO,KAAK,MAAM,IAAI;AAAA,EAC9B;AAAA,EAEO,OAAsC,MAAS,QAAsC;AACzF,UAAM,SAAS,OAAO,KAAK,SAAS,EAAE,MAAM,OAAO,CAAC;AAEpD,WAAO,MAAM,KAAK,QAAQ,OAAO,MAAM;AAAA,EAC1C;AAAA,EAEO,IACJ,QACD;AACC,UAAM,UAAgC,CAAC;AAEvC,YAAQ,MAAM,EAAE,QAAQ,CAACC,YAAWA,WAAU,KAAK,QAAQ,IAAI,OAAO,SAASA,OAAM,CAAC,CAAC;AAEvF,WAAO,MAAM;AACV,cAAQ,QAAQ,CAACA,YAAW,KAAK,QAAQ,OAAOA,OAAM,CAAC;AAAA,IAC1D;AAAA,EACH;AAAA,EAEO,KACJ,MACA,MACA,SACY;AACZ,QAAI,SAAS;AACb,UAAM,aAAa,OAAO,OAAO,OAAO,OAAO,OAAO,CAAC;AAEvD,eAAW,UAAU,KAAK,SAAS;AAChC,UAAI,OAAO,SAAS,MAAM;AACvB,iBAAS,OAAO,OAAO,QAAQ,UAAU;AAAA,MAC5C;AAAA,IACH;AAEA,WAAO;AAAA,EACV;AACH;;;AC1DA;AAIO,SAAS,sBAAsB,UAAuD;AAC1F,QAAM,kBAAkB;AACxB,QAAM,kBAAkB,CAAC,YAAY,SAAS,SAAS,QAAQ,MAAM;AAErE,QAAM,aAA6C;AAAA,IAChD,MAAM;AAAA,IACN,OAAO,OAAO,SAAS;AAX7B,UAAAC;AAYS,UAAI,CAAC,QAAQ,SAAS,SAAS,eAAe,GAAG;AAC9C;AAAA,MACH;AAEA,OAAAA,MAAA,QAAQ,QAAQ,WAAhB,gBAAAA,IAAwB,GAAG,QAAQ,CAAC,UAAkB;AACnD,cAAM,UAAU,yCAAyC,KAAK,MAAM,SAAS,MAAM,CAAC;AACpF,YAAI,CAAC,SAAS;AACX;AAAA,QACH;AAEA,iBAAS;AAAA,UACN,QAAQ,QAAQ;AAAA,UAChB,OAAO,mBAAmB,QAAQ,EAAE;AAAA,UACpC,UAAU,SAAS,QAAQ,EAAE;AAAA,UAC7B,WAAW,SAAS,QAAQ,EAAE;AAAA,UAC9B,OAAO,SAAS,QAAQ,EAAE;AAAA,QAC7B,CAAC;AAAA,MACJ;AAAA,IACH;AAAA,EACH;AAEA,QAAM,SAAwC;AAAA,IAC3C,MAAM;AAAA,IACN,OAAO,MAAM,SAAS;AACnB,UAAI,CAAC,gBAAgB,SAAS,QAAQ,MAAM,GAAG;AAC5C,eAAO;AAAA,MACV;AAEA,aAAO,UAAU,MAAM,eAAe;AAAA,IACzC;AAAA,EACH;AAEA,SAAO,CAAC,QAAQ,UAAU;AAC7B;AAEA,SAAS,mBAAmB,OAAe;AACxC,SAAO,OAAO,MAAM,YAAY,EAAE,MAAM,KAAK,CAAC,CAAC,KAAK;AACvD;;;AChDA;AAGO,SAAS,mBACb,cACiC;AACjC,QAAM,UAAU,KAAK,cAAc,CAAC,OAAO,KAAK,CAAC;AAEjD,SAAO;AAAA,IACJ,MAAM;AAAA,IACN,OAAO,MAAM;AACV,aAAO,kCAAK,UAAY;AAAA,IAC3B;AAAA,EACH;AACH;;;ACVO,SAAS,cAAc;AAAA,EAC3B;AAAA,EACA,SAAS;AAAA,EACT,SAAS;AACZ,GAA2F;AACxF,MAAI,QAAQ,GAAG;AACZ,WAAO;AAAA,MACJ,MAAM;AAAA,MACN,OAAO,OAAO,SAAS;AAbhC,YAAAC,KAAA;AAcY,YAAI;AAEJ,iBAAS,OAAO;AACb,qBAAW,aAAa,OAAO;AAC/B,oBAAU,WAAW,MAAM,KAAK;AAAA,QACnC;AAEA,iBAAS,OAAO;AArB5B,cAAAA,KAAAC;AAsBe,WAAAD,MAAA,QAAQ,QAAQ,WAAhB,gBAAAA,IAAwB,IAAI,QAAQ;AACpC,WAAAC,MAAA,QAAQ,QAAQ,WAAhB,gBAAAA,IAAwB,IAAI,QAAQ;AACpC,kBAAQ,QAAQ,IAAI,QAAQ,IAAI;AAChC,kBAAQ,QAAQ,IAAI,SAAS,IAAI;AACjC,qBAAW,aAAa,OAAO;AAAA,QAClC;AAEA,iBAAS,OAAO;AACb,eAAK;AACL,kBAAQ,KAAK,IAAI,eAAe,QAAW,WAAW,uBAAuB,CAAC;AAAA,QACjF;AAEA,oBAAUD,MAAA,QAAQ,QAAQ,WAAhB,gBAAAA,IAAwB,GAAG,QAAQ;AAC7C,oBAAU,aAAQ,QAAQ,WAAhB,mBAAwB,GAAG,QAAQ;AAC7C,gBAAQ,QAAQ,GAAG,QAAQ,IAAI;AAC/B,gBAAQ,QAAQ,GAAG,SAAS,IAAI;AAEhC,aAAK;AAAA,MACR;AAAA,IACH;AAAA,EACH;AACH;;;AC1CA;AAEO,SAAS,oBAAmD;AAChE,SAAO;AAAA,IACJ,MAAM;AAAA,IACN,OAAO,MAAM;AACV,YAAM,SAAmB,CAAC;AAC1B,UAAI;AACJ,eAASE,QAAO,MAAgB;AAC7B,SAAC,SAAS,UAAU,CAAC,GAAG,KAAK,GAAG,IAAI;AAAA,MACvC;AAEA,eAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACnC,cAAM,QAAQ,KAAK;AAEnB,YAAI,WAAW,KAAK,GAAG;AACpB,UAAAA,QAAO,QAAQ,KAAK,CAAC;AACrB;AAAA,QACH;AAEA,YAAI,UAAU,MAAM;AACjB,UAAAA;AAAA,YACG,KAAK,MAAM,IAAI,CAAC,EAAE,QAAQ,CAAC,SAAU,WAAW,IAAI,KAAK,QAAQ,IAAI,KAAM,IAAI;AAAA,UAClF;AACA;AAAA,QACH;AAEA,eAAO,KAAK,KAAK;AAAA,MACpB;AAEA,aAAO,CAAC,SAAS,SAAS,CAAC,GAAG,QAAQ,MAAM,GAAG,OAAO,IAAI,MAAM,CAAC;AAAA,IACpE;AAAA,EACH;AACH;;;ACjBA;AAGA,IAAM,MAAM;AAmBL,SAAS,mBACb,SACA,SACD;AA1CF,MAAAC;AA2CG,QAAM,UAAU,IAAI,YAAY;AAChC,QAAM,SAAS;AAAA,IACX,YAAY,OAAO,YAAY,WAAW,EAAE,QAAQ,IAAI,YAAa,CAAC;AAAA,IACvE;AAAA,EACH;AAEA,MAAI,CAAC,aAAa,OAAO,OAAO,GAAG;AAChC,UAAM,IAAQ;AAAA,MACX;AAAA,MACA;AAAA,IACH;AAAA,EACH;AAEA,MAAI,MAAM,QAAQ,OAAO,MAAM,GAAG;AAC/B,YAAQ,IAAI,6BAA6B,OAAO,MAAM,CAAC;AAAA,EAC1D;AAEA,UAAQ,IAAI,4BAA4B,OAAO,MAAM,CAAC;AACtD,UAAQ,IAAI,kBAAkB,CAAC;AAC/B,UAAQ,IAAI,0BAA0B,OAAO,UAAU,CAAC;AACxD,SAAO,SAAS,QAAQ,IAAI,YAAY,OAAO,KAAK,CAAC;AACrD,SAAO,YAAY,QAAQ,IAAI,sBAAsB,OAAO,QAAQ,CAAC;AACrE,SAAO,WAAW,QAAQ,IAAI,cAAc,OAAO,OAAO,CAAC;AAC3D,SAAO,gBAAgB,QAAQ,IAAI,mBAAmB,OAAO,YAAY,CAAC;AAE1E,UAAQ,IAAI,qBAAqB,sBAAsB,IAAI,CAAC,CAAC;AAC7D,SAAO,UAAU,QAAQ,IAAI,qBAAqB,OAAO,MAAM,CAAC;AAEhE,qBAAmB,SAAS,OAAO,SAAQA,MAAA,OAAO,WAAP,gBAAAA,IAAe,uBAAuB;AAEjF,SAAO,IAAI,IAAI,QAAQ,OAAO;AACjC;;;ACxEA;AAIA,IAAM,0BAA0B,CAAC,gBAAgB,OAAO,iBAAiB,QAAQ;AAEjF,IAAM,0BAA0B;AAAA,EAC7B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACH;AAEO,SAAS,QACV,MACO;AACV,MAAI;AAEJ,MAAI,QAAQ,QAAQ,QAAQ;AAE5B,MAAI;AACD,UAAM,mBAAmB,GAAG,IAAI;AAAA,EACnC,SAAS,GAAP;AACC,YAAQ,QAAQ,OAAO,CAAC;AAAA,EAC3B;AAEA,WAAS,gBAAgB;AACtB,WAAO;AAAA,EACV;AAEA,WAAS,cAAc;AACpB,WAAO;AAAA,EACV;AAEA,QAAM,aAAa,CAAC,GAAG,yBAAyB,GAAG,uBAAuB,EAAE;AAAA,IACzE,CAAC,KAAU,SAAiB;AACzB,YAAM,UAAU,wBAAwB,SAAS,IAAI;AAErD,YAAM,QAAQ,UAAU,aAAa,MAAM,GAAG,IAAI,YAAY,MAAM,KAAK,GAAG;AAC5E,YAAM,cAAc,UAAU,cAAc;AAE5C,aAAO,eAAe,KAAK,MAAM;AAAA,QAC9B,YAAY;AAAA,QACZ,cAAc;AAAA,QACd,OAAO,MAAM,QAAQ;AAAA,MACxB,CAAC;AAED,aAAO;AAAA,IACV;AAAA,IACA,CAAC;AAAA,EACJ;AAEA,SAAO;AAEP,WAAS,aAAa,IAAYC,MAA4C;AAC3E,WAAO,YAAaC,OAAa;AAC9B,UAAI,OAAOA,MAAKA,MAAK,YAAY,YAAY;AAC1C,cAAM,IAAI;AAAA,UACP,gHAEG;AAAA,QACN;AAAA,MACH;AAEA,aAAO,MAAM,KAAK,WAAY;AAC3B,eAAO,IAAI,QAAQ,SAAU,SAAS,QAAQ;AAC3C,gBAAM,WAAkC,CAAC,KAAmB,WAAiB;AAC1E,gBAAI,KAAK;AACN,qBAAO,OAAO,QAAQ,GAAG,CAAC;AAAA,YAC7B;AAEA,oBAAQ,MAAM;AAAA,UACjB;AACA,UAAAA,MAAK,KAAK,QAAQ;AAElB,UAAAD,KAAI,IAAI,MAAMA,MAAKC,KAAI;AAAA,QAC1B,CAAC;AAAA,MACJ,CAAC;AAAA,IACJ;AAAA,EACH;AAEA,WAAS,YAAY,IAAYD,MAAU,KAAgB;AACxD,WAAO,IAAIC,UAAgB;AACxB,MAAAD,KAAI,IAAI,GAAGC,KAAI;AAEf,aAAO;AAAA,IACV;AAAA,EACH;AACH;AAEA,SAAS,QAAQ,OAAoC;AAClD,MAAI,iBAAiB,OAAO;AACzB,WAAO;AAAA,EACV;AAEA,MAAI,OAAO,UAAU,UAAU;AAC5B,WAAO,IAAI,MAAM,KAAK;AAAA,EACzB;AAEA,SAAO,IAAI,iBAAiB,KAAK;AACpC;;;ACvJO,IAAM,YAAY;AAEzB,IAAO,cAAQ;", "names": ["trimmed", "ExitCodes", "commands", "parser", "parsers", "CheckRepoActions", "parser", "trimmed", "CleanOptions", "append", "GitConfigScope", "DiffNameStatus", "ResetMode", "debug", "onError", "parser", "parser", "parser", "excludeOptions", "parsers", "parsers", "parsers", "parsers", "parser", "trimmed", "parsers", "parsers", "parsers", "parsers", "parser", "parsers", "disallowedCommand", "parsers", "parser", "GitExecutor", "SimpleGitApi", "Scheduler", "configurationErrorTask", "asArray", "filterArray", "filterPrimitives", "filterString", "filterStringOrStringArray", "filterType", "getTrailingOptions", "trailingFunctionArgument", "trailingOptionsArgument", "applyPatchTask", "branchTask", "branchLocalTask", "deleteBranchesTask", "deleteBranchTask", "checkIgnoreTask", "checkIsRepoTask", "cloneTask", "cloneMirrorTask", "cleanWithOptionsTask", "isCleanOptionsArray", "diffSummaryTask", "fetchTask", "moveTask", "pullTask", "pushTagsTask", "addRemoteTask", "getRemotesTask", "listRemotesTask", "remoteTask", "removeRemoteTask", "getResetMode", "resetTask", "stashListTask", "addSubModuleTask", "initSubModuleTask", "subModuleTask", "updateSubModuleTask", "addAnnotatedTagTask", "addTagTask", "tagListTask", "straightThroughBufferTask", "straightThroughStringTask", "Git", "_a", "input", "plugin", "_a", "_a", "_b", "append", "_a", "git", "args"]}