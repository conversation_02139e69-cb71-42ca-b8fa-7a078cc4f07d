{"version": 3, "file": "replacers.js", "sourceRoot": "", "sources": ["../../src/helpers/replacers.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;AAOA,2BAAiD;AACjD,iCAA4B;AAC5B,+BAAwC;AAExC,oCAA4E;AAC5E,gDAAiD;AAQjD,SAAsB,eAAe,CACnC,MAAe,EACf,SAA0B,EAC1B,YAAuB;;;;;QAEvB,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,2BAA2B,CAAC,CAAC;QACjD,MAAM,GAAG,GAAG,OAAO,CAAC,GAAG,EAAE,CAAC;QAC1B,MAAM,YAAY,GAAa,WAAG,CAAC,WAAW,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;QAC7D,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,qBAAqB,EAAE,YAAY,CAAC,CAAC;QAGzD,MAAM,gBAAgB,GAAoB;YACxC,OAAO,EAAE;gBACP,OAAO,EAAE,IAAI;aACd;YACD,UAAU,EAAE;gBACV,OAAO,EAAE,CAAC,CAAC,MAAM,CAAC,OAAO;aAC1B;SACF,CAAC;QAGF,IAAI,MAAM,mCACL,gBAAgB,GAChB,SAAS,CACb,CAAC;QAGF,MAAM,CAAC,MAAM,CAAC,KAAK,CACjB,sDAAsD,EACtD,YAAY,CACb,CAAC;QACF,YAAY,aAAZ,YAAY,uBAAZ,YAAY,CAAE,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE;YAC1B,MAAM,CAAC,CAAC,CAAC,GAAG;gBACV,OAAO,EAAE,IAAI;gBACb,IAAI,EAAE,CAAC;aACR,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,0BAA0B,CAAC,CAAC;QAChD,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;;YACvC,KAA6B,eAAA,YAAA,cAAA,OAAO,CAAA,aAAA;gBAAP,uBAAO;gBAAP,WAAO;;oBAAzB,MAAM,QAAQ,KAAA,CAAA;oBACvB,IAAI,QAAQ,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE;wBAEvB,IAAI,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE;4BACvD,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE,QAAQ,CAAC,CAAC;4BAC3D,MAAM,cAAc,GAAG,YACrB,gBAAgB,QAAQ,CAAC,CAAC,CAAC,WAAW,4CACvC,CAAC;4BACF,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;yBAC/C;wBAED,MAAM,IAAI,GAAG,MAAA,QAAQ,CAAC,CAAC,CAAC,0CAAE,IAAI,CAAC;wBAC/B,IAAI,CAAC,IAAI,EAAE;4BACT,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,uBAAuB,EAAE,QAAQ,CAAC,CAAC;4BACvD,SAAS;yBACV;wBAED,MAAM,iBAAiB,GAAG,CAAO,UAAkB,EAAE,EAAE;;4BACrD,MAAM,cAAc,GAAG,YAAa,UAAU,4CAAC,CAAC;4BAChD,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,0BAA0B,EAAE,cAAc,CAAC,CAAC;4BAChE,MAAM,gBAAgB,GAAG,cAAc,CAAC,OAAO,CAAC;4BAChD,IAAI,OAAO,gBAAgB,IAAI,UAAU,EAAE;gCACzC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;gCACxC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,mBAAmB,IAAI,GAAG,CAAC,CAAC;6BAChD;iCAAM;gCACL,MAAM,CAAC,MAAM,CAAC,KAAK,CACjB,8BAA8B,IAAI,4BAA4B,CAC/D,CAAC;6BACH;wBACH,CAAC,CAAA,CAAC;wBAGF,MAAM,cAAc,GAAG,CAAC,IAAA,iBAAU,EAAC,IAAI,CAAC,CAAC;wBACzC,MAAM,IAAI,GAAG,cAAc,CAAC,CAAC,CAAC,aAAa,CAAC,IAAA,WAAI,EAAC,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;wBAEpE,IAAI,IAAA,eAAU,EAAC,IAAI,CAAC,EAAE;4BACpB,IAAI;gCACF,MAAM,iBAAiB,CAAC,IAAI,CAAC,CAAC;gCAC9B,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,oBAAoB,EAAE,IAAI,CAAC,CAAC;gCAChD,SAAS;6BACV;4BAAC,WAAM,GAAE;yBACX;wBAGD,IAAI,cAAc,EAAE;4BAClB,KAAK,MAAM,UAAU,IAAI,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,IAAA,WAAI,EAAC,GAAG,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE;gCACpE,IAAI;oCACF,MAAM,iBAAiB,CAAC,UAAU,CAAC,CAAC;oCACpC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,oBAAoB,EAAE,UAAU,CAAC,CAAC;oCACtD,SAAS;iCACV;gCAAC,WAAM,GAAE;6BACX;yBACF;wBAED,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,8BAA8B,IAAI,GAAG,CAAC,CAAC;qBAC5D;;;;;aACF;;;;;;;;;QACD,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,mBAAmB,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC;;CAC5D;AAlGD,0CAkGC;AASD,SAAsB,YAAY,CAChC,MAAe,EACf,IAAY,EACZ,eAAyB,EACzB,oBAA6B;;QAE7B,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE,IAAI,CAAC,CAAC;QACvD,MAAM,IAAI,GAAG,MAAM,aAAG,CAAC,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QAC9C,MAAM,QAAQ,GAAG,kBAAkB,CACjC,MAAM,EACN,IAAI,EACJ,IAAI,EACJ,eAAe,EACf,oBAAoB,CACrB,CAAC;QAEF,IAAI,IAAI,KAAK,QAAQ,EAAE;YACrB,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,6BAA6B,EAAE,IAAI,CAAC,CAAC;YACzD,MAAM,aAAG,CAAC,SAAS,CAAC,IAAI,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;YAC5C,OAAO,IAAI,CAAC;SACb;QACD,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE,IAAI,CAAC,CAAC;QAC5D,OAAO,KAAK,CAAC;IACf,CAAC;CAAA;AAvBD,oCAuBC;AAUD,SAAgB,kBAAkB,CAChC,MAAe,EACf,IAAY,EACZ,IAAY,EACZ,eAAyB,EACzB,oBAA6B;IAE7B,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,EAAE;QACpC,IAAI,GAAG,IAAA,gCAAwB,EAAC,IAAI,EAAE,IAAI,EAAE,CAAC,IAAI,EAAE,EAAE,CACnD,QAAQ,CAAC;YACP,IAAI;YACJ,IAAI;YACJ,MAAM;SACP,CAAC,CACH,CAAC;IACJ,CAAC,CAAC,CAAC;IAIH,IAAI,eAAe,EAAE;QACnB,IAAI,GAAG,IAAA,8BAAsB,EAAC,IAAI,EAAE,IAAI,EAAE,oBAAoB,CAAC,CAAC;KACjE;IAED,OAAO,IAAI,CAAC;AACd,CAAC;AAxBD,gDAwBC", "sourcesContent": ["/**\n * @file\n *\n * This file has all helperfunctions related to replacing.\n */\n\n/** */\nimport { existsSync, promises as fsp } from 'fs';\nimport { Dir } from 'mylas';\nimport { isAbsolute, join } from 'path';\nimport { IConfig, ReplacerOptions } from '../interfaces';\nimport { replaceSourceImportPaths, resolveFullImportPaths } from '../utils';\nimport normalizePath = require('normalize-path');\n\n/**\n * importReplacers imports replacers for tsc-alias to use.\n * @param {IConfig} config the tsc-alias config object.\n * @param {ReplacerOptions} replacers the tsc-alias replacer options.\n * @param {string[]} cmdReplacers array of filepaths to replacers from command-line.\n */\nexport async function importReplacers(\n  config: IConfig,\n  replacers: ReplacerOptions,\n  cmdReplacers?: string[]\n) {\n  config.output.debug('Started loading replacers');\n  const dir = process.cwd();\n  const node_modules: string[] = Dir.nodeModules({ cwd: dir });\n  config.output.debug('Found node_modules:', node_modules);\n\n  // List of default replacers.\n  const defaultReplacers: ReplacerOptions = {\n    default: {\n      enabled: true\n    },\n    'base-url': {\n      enabled: !!config.baseUrl\n    }\n  };\n\n  // List of all replacers.\n  let merged: ReplacerOptions = {\n    ...defaultReplacers,\n    ...replacers\n  };\n\n  // Added replacers to list from command-line filepaths.\n  config.output.debug(\n    'Added replacers to list from command-line filepaths:',\n    cmdReplacers\n  );\n  cmdReplacers?.forEach((v) => {\n    merged[v] = {\n      enabled: true,\n      file: v\n    };\n  });\n\n  config.output.debug('Reading replacers config');\n  const entries = Object.entries(merged);\n  for await (const replacer of entries) {\n    if (replacer[1].enabled) {\n      // Importing default replacers.\n      if (Object.keys(defaultReplacers).includes(replacer[0])) {\n        config.output.debug('Loading default replacer:', replacer);\n        const replacerModule = await import(\n          `../replacers/${replacer[0]}.replacer`\n        );\n        config.replacers.push(replacerModule.default);\n      }\n\n      const file = replacer[1]?.file;\n      if (!file) {\n        config.output.debug('Replacer has no file:', replacer);\n        continue; // When file is undefined don't try to import.\n      }\n      // Try to import replacer.\n      const tryImportReplacer = async (targetPath: string) => {\n        const replacerModule = await import(targetPath);\n        config.output.debug('Imported replacerModule:', replacerModule);\n        const replacerFunction = replacerModule.default;\n        if (typeof replacerFunction == 'function') {\n          config.replacers.push(replacerFunction);\n          config.output.info(`Added replacer \"${file}\"`);\n        } else {\n          config.output.error(\n            `Failed to import replacer \"${file}\", not in replacer format.`\n          );\n        }\n      };\n\n      // Look for replacer in cwd.\n      const isRelativePath = !isAbsolute(file);\n      const path = isRelativePath ? normalizePath(join(dir, file)) : file;\n\n      if (existsSync(path)) {\n        try {\n          await tryImportReplacer(path);\n          config.output.debug('Imported replacer:', path);\n          continue;\n        } catch {}\n      }\n\n      // Look for replacer in node_modules.\n      if (isRelativePath) {\n        for (const targetPath of node_modules.map((v) => join(dir, v, file))) {\n          try {\n            await tryImportReplacer(targetPath);\n            config.output.debug('Imported replacer:', targetPath);\n            continue;\n          } catch {}\n        }\n      }\n\n      config.output.error(`Failed to import replacer \"${file}\"`);\n    }\n  }\n  config.output.debug('Loaded replacers:', config.replacers);\n}\n\n/**\n * replaceAlias replaces aliases in file.\n * @param {IConfig} config configuration\n * @param {string} file file to replace aliases in.\n * @param {boolean} resolveFullPath if tsc-alias should resolve the full path\n * @returns {Promise<boolean>} if something has been replaced.\n */\nexport async function replaceAlias(\n  config: IConfig,\n  file: string,\n  resolveFullPath?: boolean,\n  resolveFullExtension?: string\n): Promise<boolean> {\n  config.output.debug('Starting to replace file:', file);\n  const code = await fsp.readFile(file, 'utf8');\n  const tempCode = replaceAliasString(\n    config,\n    file,\n    code,\n    resolveFullPath,\n    resolveFullExtension\n  );\n\n  if (code !== tempCode) {\n    config.output.debug('replaced file with changes:', file);\n    await fsp.writeFile(file, tempCode, 'utf8');\n    return true;\n  }\n  config.output.debug('replaced file without changes:', file);\n  return false;\n}\n\n/**\n * replaceAliasString replaces aliases in the given code content and returns the changed code.\n * @param {IConfig} config configuration\n * @param {string} file path of the file to replace aliases in.\n * @param {string} code contents of the file to replace aliases in.\n * @param {boolean} resolveFullPath if tsc-alias should resolve the full path\n * @returns {string} content of the file with any replacements possible applied.\n */\nexport function replaceAliasString(\n  config: IConfig,\n  file: string,\n  code: string,\n  resolveFullPath?: boolean,\n  resolveFullExtension?: string\n): string {\n  config.replacers.forEach((replacer) => {\n    code = replaceSourceImportPaths(code, file, (orig) =>\n      replacer({\n        orig,\n        file,\n        config\n      })\n    );\n  });\n\n  // Fully resolve all import paths (not just aliased ones)\n  // *after* the aliases are resolved\n  if (resolveFullPath) {\n    code = resolveFullImportPaths(code, file, resolveFullExtension);\n  }\n\n  return code;\n}\n"]}