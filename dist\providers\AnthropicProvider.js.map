{"version": 3, "file": "AnthropicProvider.js", "sourceRoot": "", "sources": ["../../src/providers/AnthropicProvider.ts"], "names": [], "mappings": "AAAA,OAAO,SAAS,MAAM,mBAAmB,CAAC;AAE1C,OAAO,EAAE,gBAAgB,EAAE,MAAM,sBAAsB,CAAC;AACxD,OAAO,EAAE,MAAM,EAAE,MAAM,gBAAgB,CAAC;AACxC,OAAO,EAAE,YAAY,EAAE,MAAM,sBAAsB,CAAC;AAEpD,MAAM,OAAO,iBAAiB;IACZ,IAAI,GAAG,WAAW,CAAC;IAC3B,MAAM,CAAY;IAClB,MAAM,CAAS;IACf,YAAY,CAAe;IAEnC,YAAY,MAA+B;QACzC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,WAAW,EAAE,CAAC;QACnC,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC,WAAW,EAAE,CAAC;QAE/C,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;YACnB,MAAM,IAAI,gBAAgB,CAAC,IAAI,CAAC,IAAI,EAAE,+BAA+B,CAAC,CAAC;QACzE,CAAC;QAED,IAAI,CAAC,MAAM,GAAG,IAAI,SAAS,CAAC;YAC1B,MAAM,EAAE,MAAM,CAAC,MAAgB;YAC/B,OAAO,EAAE,MAAM,CAAC,OAAiB;SAClC,CAAC,CAAC;IACL,CAAC;IAEM,KAAK,CAAC,gBAAgB,CAAC,MAAc,EAAE,UAAsB,EAAE;QACpE,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,IAAI,4BAA4B,CAAC;YAC5D,MAAM,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;YAErD,MAAM,aAAa,GAAkC;gBACnD,KAAK;gBACL,QAAQ;gBACR,UAAU,EAAE,OAAO,CAAC,SAAS,IAAI,IAAI;gBACrC,WAAW,EAAE,OAAO,CAAC,WAAW,IAAI,GAAG;aACxC,CAAC;YAEF,iCAAiC;YACjC,IAAI,OAAO,CAAC,YAAY,EAAE,CAAC;gBACzB,aAAa,CAAC,MAAM,GAAG,OAAO,CAAC,YAAY,CAAC;YAC9C,CAAC;YAED,qCAAqC;YACrC,IAAI,OAAO,CAAC,KAAK,IAAI,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC9C,aAAa,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;oBAC/C,IAAI,EAAE,IAAI,CAAC,IAAI;oBACf,WAAW,EAAE,IAAI,CAAC,WAAW;oBAC7B,YAAY,EAAE,IAAI,CAAC,UAAU;iBAC9B,CAAC,CAAC,CAAC;YACN,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,8BAA8B,EAAE;gBAChD,KAAK;gBACL,YAAY,EAAE,QAAQ,CAAC,MAAM;gBAC7B,QAAQ,EAAE,CAAC,CAAC,aAAa,CAAC,KAAK;gBAC/B,SAAS,EAAE,aAAa,CAAC,KAAK,EAAE,MAAM,IAAI,CAAC;aAC5C,CAAC,CAAC;YAEH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;YAElE,IAAI,OAAO,GAAG,EAAE,CAAC;YACjB,MAAM,SAAS,GAAe,EAAE,CAAC;YAEjC,KAAK,MAAM,KAAK,IAAI,QAAQ,CAAC,OAAO,EAAE,CAAC;gBACrC,IAAI,KAAK,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;oBAC1B,OAAO,IAAI,KAAK,CAAC,IAAI,CAAC;gBACxB,CAAC;qBAAM,IAAI,KAAK,CAAC,IAAI,KAAK,UAAU,EAAE,CAAC;oBACrC,SAAS,CAAC,IAAI,CAAC;wBACb,EAAE,EAAE,KAAK,CAAC,EAAE;wBACZ,IAAI,EAAE,KAAK,CAAC,IAAI;wBAChB,SAAS,EAAE,KAAK,CAAC,KAA4B;qBAC9C,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;YAED,MAAM,WAAW,GAAgB;gBAC/B,OAAO;gBACP,SAAS;gBACT,KAAK,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;oBACtB,YAAY,EAAE,QAAQ,CAAC,KAAK,CAAC,YAAY;oBACzC,gBAAgB,EAAE,QAAQ,CAAC,KAAK,CAAC,aAAa;oBAC9C,WAAW,EAAE,QAAQ,CAAC,KAAK,CAAC,YAAY,GAAG,QAAQ,CAAC,KAAK,CAAC,aAAa;iBACxE,CAAC,CAAC,CAAC,SAAS;aACd,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC;YAE7E,OAAO,WAAW,CAAC;QAErB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,8BAA8B,EAAE;gBAChD,KAAK,EAAG,KAAe,CAAC,OAAO;gBAC/B,MAAM,EAAE,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAG,KAAK;aAC3C,CAAC,CAAC;YAEH,IAAI,KAAK,YAAY,SAAS,CAAC,QAAQ,EAAE,CAAC;gBACxC,MAAM,IAAI,gBAAgB,CACxB,IAAI,CAAC,IAAI,EACT,wBAAwB,KAAK,CAAC,OAAO,EAAE,EACvC,EAAE,MAAM,EAAG,KAAa,CAAC,MAAM,EAAE,IAAI,EAAG,KAAa,CAAC,IAAI,EAAE,CAC7D,CAAC;YACJ,CAAC;YAED,MAAM,IAAI,gBAAgB,CAAC,IAAI,CAAC,IAAI,EAAG,KAAe,CAAC,OAAO,CAAC,CAAC;QAClE,CAAC;IACH,CAAC;IAEM,KAAK,CAAC,CAAC,sBAAsB,CAAC,MAAc,EAAE,UAAsB,EAAE;QAC3E,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,IAAI,4BAA4B,CAAC;YAC5D,MAAM,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;YAErD,MAAM,aAAa,GAAkC;gBACnD,KAAK;gBACL,QAAQ;gBACR,UAAU,EAAE,OAAO,CAAC,SAAS,IAAI,IAAI;gBACrC,WAAW,EAAE,OAAO,CAAC,WAAW,IAAI,GAAG;gBACvC,MAAM,EAAE,IAAI;aACb,CAAC;YAEF,iCAAiC;YACjC,IAAI,OAAO,CAAC,YAAY,EAAE,CAAC;gBACzB,aAAa,CAAC,MAAM,GAAG,OAAO,CAAC,YAAY,CAAC;YAC9C,CAAC;YAED,qCAAqC;YACrC,IAAI,OAAO,CAAC,KAAK,IAAI,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC9C,aAAa,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;oBAC/C,IAAI,EAAE,IAAI,CAAC,IAAI;oBACf,WAAW,EAAE,IAAI,CAAC,WAAW;oBAC7B,YAAY,EAAE,IAAI,CAAC,UAAU;iBAC9B,CAAC,CAAC,CAAC;YACN,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,wCAAwC,EAAE;gBAC1D,KAAK;gBACL,YAAY,EAAE,QAAQ,CAAC,MAAM;gBAC7B,QAAQ,EAAE,CAAC,CAAC,aAAa,CAAC,KAAK;aAChC,CAAC,CAAC;YAEH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;YAEhE,IAAI,KAAK,EAAE,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;gBACjC,IAAI,KAAK,CAAC,IAAI,KAAK,qBAAqB,IAAI,KAAK,CAAC,KAAK,CAAC,IAAI,KAAK,YAAY,EAAE,CAAC;oBAC9E,MAAM,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC;gBACzB,CAAC;YACH,CAAC;QAEH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,wCAAwC,EAAE;gBAC1D,KAAK,EAAG,KAAe,CAAC,OAAO;gBAC/B,MAAM,EAAE,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAG,KAAK;aAC3C,CAAC,CAAC;YAEH,IAAI,KAAK,YAAY,SAAS,CAAC,QAAQ,EAAE,CAAC;gBACxC,MAAM,IAAI,gBAAgB,CACxB,IAAI,CAAC,IAAI,EACT,kCAAkC,KAAK,CAAC,OAAO,EAAE,EACjD,EAAE,MAAM,EAAG,KAAa,CAAC,MAAM,EAAE,IAAI,EAAG,KAAa,CAAC,IAAI,EAAE,CAC7D,CAAC;YACJ,CAAC;YAED,MAAM,IAAI,gBAAgB,CAAC,IAAI,CAAC,IAAI,EAAG,KAAe,CAAC,OAAO,CAAC,CAAC;QAClE,CAAC;IACH,CAAC;IAEM,mBAAmB;QACxB,OAAO,IAAI,CAAC;IACd,CAAC;IAEM,KAAK,CAAC,QAAQ,CAAC,QAAkB,EAAE,OAAqB;QAC7D,IAAI,CAAC;YACH,OAAO,MAAM,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;QAChE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,6CAA6C,EAAE;gBAC/D,QAAQ,EAAE,QAAQ,CAAC,IAAI;gBACvB,KAAK,EAAG,KAAe,CAAC,OAAO;aAChC,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAEO,aAAa,CAAC,MAAc,EAAE,OAAmB;QACvD,MAAM,QAAQ,GAA6B,EAAE,CAAC;QAE9C,mEAAmE;QACnE,IAAI,OAAO,CAAC,mBAAmB,EAAE,CAAC;YAChC,KAAK,MAAM,GAAG,IAAI,OAAO,CAAC,mBAAmB,EAAE,CAAC;gBAC9C,IAAI,GAAG,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;oBAC1B,sDAAsD;oBACtD,SAAS;gBACX,CAAC;gBAED,IAAI,GAAG,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;oBACxB,gDAAgD;oBAChD,QAAQ,CAAC,IAAI,CAAC;wBACZ,IAAI,EAAE,MAAM;wBACZ,OAAO,EAAE;4BACP;gCACE,IAAI,EAAE,aAAa;gCACnB,WAAW,EAAE,GAAG,CAAC,UAAW;gCAC5B,OAAO,EAAE,GAAG,CAAC,OAAO;6BACrB;yBACF;qBACF,CAAC,CAAC;gBACL,CAAC;qBAAM,CAAC;oBACN,QAAQ,CAAC,IAAI,CAAC;wBACZ,IAAI,EAAE,GAAG,CAAC,IAA4B;wBACtC,OAAO,EAAE,GAAG,CAAC,OAAO;qBACrB,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;QACH,CAAC;QAED,qBAAqB;QACrB,QAAQ,CAAC,IAAI,CAAC;YACZ,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,MAAM;SAChB,CAAC,CAAC;QAEH,OAAO,QAAQ,CAAC;IAClB,CAAC;IAEM,KAAK,CAAC,cAAc;QACzB,IAAI,CAAC;YACH,8BAA8B;YAC9B,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;gBAChC,KAAK,EAAE,yBAAyB;gBAChC,QAAQ,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;gBAC3C,UAAU,EAAE,EAAE;aACf,CAAC,CAAC;YACH,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,qCAAqC,EAAE;gBACvD,KAAK,EAAG,KAAe,CAAC,OAAO;aAChC,CAAC,CAAC;YACH,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAEM,KAAK,CAAC,kBAAkB;QAC7B,mEAAmE;QACnE,OAAO;YACL,wBAAwB;YACxB,0BAA0B;YAC1B,yBAAyB;YACzB,4BAA4B;SAC7B,CAAC;IACJ,CAAC;IAEM,eAAe;QACpB,OAAO,4BAA4B,CAAC;IACtC,CAAC;IAEM,YAAY,CAAC,KAAc;QAChC,MAAM,WAAW,GAA2B;YAC1C,wBAAwB,EAAE,MAAM;YAChC,0BAA0B,EAAE,MAAM;YAClC,yBAAyB,EAAE,MAAM;YACjC,4BAA4B,EAAE,MAAM;SACrC,CAAC;QAEF,OAAO,WAAW,CAAC,KAAK,IAAI,4BAA4B,CAAC,IAAI,MAAM,CAAC;IACtE,CAAC;IAEM,cAAc,CAAC,IAAY;QAChC,iEAAiE;QACjE,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,GAAG,CAAC,CAAC;IACtC,CAAC;IAEM,aAAa,CAAC,KAAyD,EAAE,KAAc;QAC5F,4CAA4C;QAC5C,MAAM,OAAO,GAAsD;YACjE,wBAAwB,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE;YACnD,0BAA0B,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE;YACpD,yBAAyB,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE;YACxD,4BAA4B,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE;SACvD,CAAC;QAEF,MAAM,YAAY,GAAG,OAAO,CAAC,KAAK,IAAI,4BAA4B,CAAC,IAAI,OAAO,CAAC,4BAA4B,CAAC,CAAC;QAC7G,MAAM,SAAS,GAAG,CAAC,KAAK,CAAC,YAAY,GAAG,OAAO,CAAC,GAAG,YAAY,CAAC,KAAK,CAAC;QACtE,MAAM,UAAU,GAAG,CAAC,KAAK,CAAC,gBAAgB,GAAG,OAAO,CAAC,GAAG,YAAY,CAAC,MAAM,CAAC;QAE5E,OAAO,SAAS,GAAG,UAAU,CAAC;IAChC,CAAC;CACF"}