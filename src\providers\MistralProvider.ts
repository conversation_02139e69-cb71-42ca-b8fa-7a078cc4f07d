import fetch from 'node-fetch';
import { <PERSON><PERSON><PERSON><PERSON>, LLMO<PERSON>s, LLMResponse, ToolCall, ToolResult, AgentContext } from '@/types';
import { LLMProviderError } from '@/utils/ErrorHandler';
import { Logger } from '@/utils/Logger';
import { ToolRegistry } from '@/tools/ToolRegistry';

export class MistralProvider implements LLMProvider {
  public readonly name = 'mistral';
  private apiKey: string;
  private baseURL: string;
  private logger: Logger;
  private toolRegistry: ToolRegistry;

  constructor(config: Record<string, unknown>) {
    this.logger = Logger.getInstance();
    this.toolRegistry = ToolRegistry.getInstance();

    if (!config.apiKey) {
      throw new LLMProviderError(this.name, 'Mistral API key is required');
    }

    this.apiKey = config.apiKey as string;
    this.baseURL = (config.baseURL as string) || 'https://api.mistral.ai/v1';
  }

  public async generateResponse(prompt: string, options: LLMOptions = {}): Promise<LLMResponse> {
    try {
      const model = options.model || 'mistral-small';
      const messages = this.buildMessages(prompt, options);

      const requestBody = {
        model,
        messages,
        temperature: options.temperature || 0.7,
        max_tokens: options.maxTokens || 4000
      };

      // Add tool descriptions to system message if tools are available
      if (options.tools && options.tools.length > 0) {
        const toolDescriptions = this.buildToolDescriptions(options.tools);
        const systemMessage = messages.find(m => m.role === 'system');
        if (systemMessage) {
          systemMessage.content += `\n\n${  toolDescriptions}`;
        } else {
          messages.unshift({
            role: 'system',
            content: toolDescriptions
          });
        }
      }

      this.logger.debug('Making Mistral API request', {
        model,
        messageCount: messages.length
      });

      const response = await fetch(`${this.baseURL}/chat/completions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.apiKey}`
        },
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }

      const data = await response.json() as any;
      const choice = data.choices?.[0];

      if (!choice) {
        throw new LLMProviderError(this.name, 'No response choice returned from Mistral');
      }

      const content = choice.message?.content || '';

      // Parse tool calls from response if tools were provided
      const toolCalls = options.tools && options.tools.length > 0 ?
        this.parseToolCalls(content) : [];

      const llmResponse: LLMResponse = {
        content,
        toolCalls,
        usage: data.usage ? {
          promptTokens: data.usage.prompt_tokens,
          completionTokens: data.usage.completion_tokens,
          totalTokens: data.usage.total_tokens
        } : undefined
      };

      this.logger.logLLMCall(this.name, model, prompt, content, llmResponse.usage);

      return llmResponse;

    } catch (error) {
      this.logger.error('Mistral API request failed', {
        error: (error as Error).message,
        prompt: `${prompt.substring(0, 100)  }...`
      });

      throw new LLMProviderError(this.name, (error as Error).message);
    }
  }

  public async *generateStreamResponse(prompt: string, options: LLMOptions = {}): AsyncGenerator<string, void, unknown> {
    try {
      const model = options.model || 'mistral-small';
      const messages = this.buildMessages(prompt, options);

      const requestBody = {
        model,
        messages,
        temperature: options.temperature || 0.7,
        max_tokens: options.maxTokens || 4000,
        stream: true
      };

      this.logger.debug('Making Mistral streaming API request', {
        model,
        messageCount: messages.length
      });

      const response = await fetch(`${this.baseURL}/chat/completions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.apiKey}`
        },
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }

      const reader = (response.body as any)?.getReader();
      if (!reader) {
        throw new Error('No response body reader available');
      }

      const decoder = new TextDecoder();
      let buffer = '';

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6);
            if (data === '[DONE]') break;

            try {
              const parsed = JSON.parse(data);
              const content = parsed.choices?.[0]?.delta?.content;
              if (content) {
                yield content;
              }
            } catch {
              // Skip invalid JSON lines
            }
          }
        }
      }

    } catch (error) {
      this.logger.error('Mistral streaming API request failed', {
        error: (error as Error).message,
        prompt: `${prompt.substring(0, 100)  }...`
      });

      throw new LLMProviderError(this.name, (error as Error).message);
    }
  }

  public supportsToolCalling(): boolean {
    return true; // Now supports tool calling through function descriptions
  }

  public async callTool(toolCall: ToolCall, context: AgentContext): Promise<ToolResult> {
    try {
      return await this.toolRegistry.executeTool(toolCall, context);
    } catch (error) {
      this.logger.error('Tool execution failed in Mistral provider', {
        toolName: toolCall.name,
        error: (error as Error).message
      });
      throw error;
    }
  }

  private buildMessages(prompt: string, options: LLMOptions): any[] {
    const messages: any[] = [];

    // Add system message if provided
    if (options.systemPrompt) {
      messages.push({
        role: 'system',
        content: options.systemPrompt
      });
    }

    // Add conversation history if provided
    if (options.conversationHistory) {
      for (const msg of options.conversationHistory) {
        if (msg.role !== 'tool') { // Skip tool messages for now
          messages.push({
            role: msg.role,
            content: msg.content
          });
        }
      }
    }

    // Add current prompt
    messages.push({
      role: 'user',
      content: prompt
    });

    return messages;
  }

  public async validateApiKey(): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseURL}/models`, {
        headers: {
          'Authorization': `Bearer ${this.apiKey}`
        }
      });
      return response.ok;
    } catch (error) {
      this.logger.error('Mistral API key validation failed', {
        error: (error as Error).message
      });
      return false;
    }
  }

  public async getAvailableModels(): Promise<string[]> {
    try {
      const response = await fetch(`${this.baseURL}/models`, {
        headers: {
          'Authorization': `Bearer ${this.apiKey}`
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json() as any;
      return data.data?.map((model: any) => model.id) || [];
    } catch (error) {
      this.logger.error('Failed to fetch Mistral models', {
        error: (error as Error).message
      });
      return ['mistral-tiny', 'mistral-small', 'mistral-medium', 'mistral-large'];
    }
  }

  public getDefaultModel(): string {
    return 'mistral-small';
  }

  public getMaxTokens(model?: string): number {
    const tokenLimits: Record<string, number> = {
      'mistral-tiny': 32000,
      'mistral-small': 32000,
      'mistral-medium': 32000,
      'mistral-large': 32000
    };

    return tokenLimits[model || 'mistral-small'] || 32000;
  }

  public estimateTokens(text: string): number {
    // Rough estimation: 1 token ≈ 4 characters for English text
    return Math.ceil(text.length / 4);
  }

  public calculateCost(usage: { promptTokens: number; completionTokens: number }, model?: string): number {
    // Mistral pricing (in USD per 1M tokens)
    const pricing: Record<string, { input: number; output: number }> = {
      'mistral-tiny': { input: 0.25, output: 0.25 },
      'mistral-small': { input: 2, output: 6 },
      'mistral-medium': { input: 2.7, output: 8.1 },
      'mistral-large': { input: 8, output: 24 }
    };

    const modelPricing = pricing[model || 'mistral-small'] || pricing['mistral-small'];
    const inputCost = (usage.promptTokens / 1000000) * modelPricing.input;
    const outputCost = (usage.completionTokens / 1000000) * modelPricing.output;

    return inputCost + outputCost;
  }

  private buildToolDescriptions(tools: any[]): string {
    const descriptions = tools.map(tool => {
      const params = tool.parameters.properties ?
        Object.entries(tool.parameters.properties)
          .map(([name, prop]: [string, any]) => `${name}: ${prop.description}`)
          .join(', ') : '';

      return `${tool.name}(${params}): ${tool.description}`;
    });

    return `Available tools:
${descriptions.join('\n')}

To use a tool, respond with a JSON object in this format:
{"tool_call": {"name": "tool_name", "arguments": {"param1": "value1", "param2": "value2"}}}

You can call multiple tools by providing an array:
[{"tool_call": {"name": "tool1", "arguments": {...}}}, {"tool_call": {"name": "tool2", "arguments": {...}}}]`;
  }

  private parseToolCalls(content: string): ToolCall[] {
    const toolCalls: ToolCall[] = [];

    try {
      // Look for JSON objects with tool_call structure
      const jsonRegex = /\{[^{}]*"tool_call"[^{}]*\}/g;
      const arrayRegex = /\[[^\[\]]*"tool_call"[^\[\]]*\]/g;

      let matches = content.match(arrayRegex);
      if (matches) {
        // Handle array format
        for (const match of matches) {
          try {
            const parsed = JSON.parse(match);
            if (Array.isArray(parsed)) {
              for (const item of parsed) {
                if (item.tool_call) {
                  toolCalls.push({
                    id: `mistral_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
                    name: item.tool_call.name,
                    arguments: item.tool_call.arguments || {}
                  });
                }
              }
            }
          } catch {
            // Skip invalid JSON
          }
        }
      } else {
        // Handle single object format
        matches = content.match(jsonRegex);
        if (matches) {
          for (const match of matches) {
            try {
              const parsed = JSON.parse(match);
              if (parsed.tool_call) {
                toolCalls.push({
                  id: `mistral_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
                  name: parsed.tool_call.name,
                  arguments: parsed.tool_call.arguments || {}
                });
              }
            } catch {
              // Skip invalid JSON
            }
          }
        }
      }
    } catch (error) {
      this.logger.debug('Failed to parse tool calls from Mistral response', {
        error: (error as Error).message,
        content: content.substring(0, 200)
      });
    }

    return toolCalls;
  }
}
