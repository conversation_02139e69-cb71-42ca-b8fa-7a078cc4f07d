import Anthropic from '@anthropic-ai/sdk';
import { <PERSON><PERSON><PERSON>ider, LLMOptions, LLMResponse, ToolCall, <PERSON>l<PERSON><PERSON>ult, AgentContext } from '@/types';
import { LLMProviderError } from '@/utils/ErrorHandler';
import { Logger } from '@/utils/Logger';
import { ToolRegistry } from '@/tools/ToolRegistry';

export class AnthropicProvider implements LLMProvider {
  public readonly name = 'anthropic';
  private client: Anthropic;
  private logger: Logger;
  private toolRegistry: ToolRegistry;

  constructor(config: Record<string, unknown>) {
    this.logger = Logger.getInstance();
    this.toolRegistry = ToolRegistry.getInstance();

    if (!config.apiKey) {
      throw new LLMProviderError(this.name, 'Anthropic API key is required');
    }

    this.client = new Anthropic({
      apiKey: config.apiKey as string,
      baseURL: config.baseURL as string
    });
  }

  public async generateResponse(prompt: string, options: LLMOptions = {}): Promise<LLMResponse> {
    try {
      const model = options.model || 'claude-3-5-sonnet-20241022';
      const messages = this.buildMessages(prompt, options);

      const requestParams: Anthropic.MessageCreateParams = {
        model,
        messages,
        max_tokens: options.maxTokens || 4000,
        temperature: options.temperature || 0.7
      };

      // Add system message if provided
      if (options.systemPrompt) {
        requestParams.system = options.systemPrompt;
      }

      // Add tools if available and enabled
      if (options.tools && options.tools.length > 0) {
        requestParams.tools = options.tools.map(tool => ({
          name: tool.name,
          description: tool.description,
          input_schema: tool.parameters
        }));
      }

      this.logger.debug('Making Anthropic API request', {
        model,
        messageCount: messages.length,
        hasTools: !!requestParams.tools,
        toolCount: requestParams.tools?.length || 0
      });

      const response = await this.client.messages.create(requestParams);

      let content = '';
      const toolCalls: ToolCall[] = [];

      for (const block of response.content) {
        if (block.type === 'text') {
          content += block.text;
        } else if (block.type === 'tool_use') {
          toolCalls.push({
            id: block.id,
            name: block.name,
            arguments: block.input as Record<string, any>
          });
        }
      }

      const llmResponse: LLMResponse = {
        content,
        toolCalls,
        usage: response.usage ? {
          promptTokens: response.usage.input_tokens,
          completionTokens: response.usage.output_tokens,
          totalTokens: response.usage.input_tokens + response.usage.output_tokens
        } : undefined
      };

      this.logger.logLLMCall(this.name, model, prompt, content, llmResponse.usage);

      return llmResponse;

    } catch (error) {
      this.logger.error('Anthropic API request failed', {
        error: (error as Error).message,
        prompt: `${prompt.substring(0, 100)  }...`
      });

      if (error instanceof Anthropic.APIError) {
        throw new LLMProviderError(
          this.name,
          `Anthropic API error: ${error.message}`,
          { status: (error as any).status, type: (error as any).type }
        );
      }

      throw new LLMProviderError(this.name, (error as Error).message);
    }
  }

  public async *generateStreamResponse(prompt: string, options: LLMOptions = {}): AsyncGenerator<string, void, unknown> {
    try {
      const model = options.model || 'claude-3-5-sonnet-20241022';
      const messages = this.buildMessages(prompt, options);

      const requestParams: Anthropic.MessageCreateParams = {
        model,
        messages,
        max_tokens: options.maxTokens || 4000,
        temperature: options.temperature || 0.7,
        stream: true
      };

      // Add system message if provided
      if (options.systemPrompt) {
        requestParams.system = options.systemPrompt;
      }

      // Add tools if available and enabled
      if (options.tools && options.tools.length > 0) {
        requestParams.tools = options.tools.map(tool => ({
          name: tool.name,
          description: tool.description,
          input_schema: tool.parameters
        }));
      }

      this.logger.debug('Making Anthropic streaming API request', {
        model,
        messageCount: messages.length,
        hasTools: !!requestParams.tools
      });

      const stream = await this.client.messages.create(requestParams);

      for await (const event of stream) {
        if (event.type === 'content_block_delta' && event.delta.type === 'text_delta') {
          yield event.delta.text;
        }
      }

    } catch (error) {
      this.logger.error('Anthropic streaming API request failed', {
        error: (error as Error).message,
        prompt: `${prompt.substring(0, 100)  }...`
      });

      if (error instanceof Anthropic.APIError) {
        throw new LLMProviderError(
          this.name,
          `Anthropic streaming API error: ${error.message}`,
          { status: (error as any).status, type: (error as any).type }
        );
      }

      throw new LLMProviderError(this.name, (error as Error).message);
    }
  }

  public supportsToolCalling(): boolean {
    return true;
  }

  public async callTool(toolCall: ToolCall, context: AgentContext): Promise<ToolResult> {
    try {
      return await this.toolRegistry.executeTool(toolCall, context);
    } catch (error) {
      this.logger.error('Tool execution failed in Anthropic provider', {
        toolName: toolCall.name,
        error: (error as Error).message
      });
      throw error;
    }
  }

  private buildMessages(prompt: string, options: LLMOptions): Anthropic.MessageParam[] {
    const messages: Anthropic.MessageParam[] = [];

    // Add conversation history if provided (excluding system messages)
    if (options.conversationHistory) {
      for (const msg of options.conversationHistory) {
        if (msg.role === 'system') {
          // System messages are handled separately in Anthropic
          continue;
        }

        if (msg.role === 'tool') {
          // Tool results need to be formatted differently
          messages.push({
            role: 'user',
            content: [
              {
                type: 'tool_result',
                tool_use_id: msg.toolCallId!,
                content: msg.content
              }
            ]
          });
        } else {
          messages.push({
            role: msg.role as 'user' | 'assistant',
            content: msg.content
          });
        }
      }
    }

    // Add current prompt
    messages.push({
      role: 'user',
      content: prompt
    });

    return messages;
  }

  public async validateApiKey(): Promise<boolean> {
    try {
      // Test with a minimal request
      await this.client.messages.create({
        model: 'claude-3-haiku-20240307',
        messages: [{ role: 'user', content: 'Hi' }],
        max_tokens: 10
      });
      return true;
    } catch (error) {
      this.logger.error('Anthropic API key validation failed', {
        error: (error as Error).message
      });
      return false;
    }
  }

  public async getAvailableModels(): Promise<string[]> {
    // Anthropic doesn't have a models endpoint, so return known models
    return [
      'claude-3-opus-20240229',
      'claude-3-sonnet-20240229',
      'claude-3-haiku-20240307',
      'claude-3-5-sonnet-20241022'
    ];
  }

  public getDefaultModel(): string {
    return 'claude-3-5-sonnet-20241022';
  }

  public getMaxTokens(model?: string): number {
    const tokenLimits: Record<string, number> = {
      'claude-3-opus-20240229': 200000,
      'claude-3-sonnet-20240229': 200000,
      'claude-3-haiku-20240307': 200000,
      'claude-3-5-sonnet-20241022': 200000
    };

    return tokenLimits[model || 'claude-3-5-sonnet-20241022'] || 200000;
  }

  public estimateTokens(text: string): number {
    // Anthropic's tokenization is roughly 1 token per 3.5 characters
    return Math.ceil(text.length / 3.5);
  }

  public calculateCost(usage: { promptTokens: number; completionTokens: number }, model?: string): number {
    // Pricing as of 2024 (in USD per 1M tokens)
    const pricing: Record<string, { input: number; output: number }> = {
      'claude-3-opus-20240229': { input: 15, output: 75 },
      'claude-3-sonnet-20240229': { input: 3, output: 15 },
      'claude-3-haiku-20240307': { input: 0.25, output: 1.25 },
      'claude-3-5-sonnet-20241022': { input: 3, output: 15 }
    };

    const modelPricing = pricing[model || 'claude-3-5-sonnet-20241022'] || pricing['claude-3-5-sonnet-20241022'];
    const inputCost = (usage.promptTokens / 1000000) * modelPricing.input;
    const outputCost = (usage.completionTokens / 1000000) * modelPricing.output;

    return inputCost + outputCost;
  }
}
