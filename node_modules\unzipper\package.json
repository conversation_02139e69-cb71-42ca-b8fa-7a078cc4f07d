{"name": "unzipper", "version": "0.12.3", "description": "Unzip cross-platform streaming API ", "author": "<PERSON> <<EMAIL>>", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "https://github.com/ZJONSSON/node-unzipper.git"}, "license": "MIT", "dependencies": {"bluebird": "~3.7.2", "duplexer2": "~0.1.4", "fs-extra": "^11.2.0", "graceful-fs": "^4.2.2", "node-int64": "^0.4.0"}, "devDependencies": {"@eslint/js": "^9.2.0", "aws-sdk": "^2.1636.0", "@aws-sdk/client-s3": "^3.0.0", "dirdiff": ">= 0.0.1 < 1", "eslint": "^9.2.0", "globals": "^15.2.0", "iconv-lite": "^0.4.24", "request": "^2.88.0", "stream-buffers": ">= 0.2.5 < 1", "tap": "^16.3.10", "temp": ">= 0.4.0 < 1"}, "directories": {"example": "examples", "test": "test"}, "keywords": ["zip", "unzip", "zlib", "uncompress", "archive", "stream", "extract"], "main": "unzip.js", "scripts": {"test": "npx tap test/*.js --coverage-report=html --lines=90 --functions=85 --statements=90 --branches=80 --reporter=dot"}}