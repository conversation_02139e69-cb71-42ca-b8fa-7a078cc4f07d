import { ProjectContext, FileInfo } from '../types';
export declare class ProjectIndexer {
    private static instance;
    private logger;
    private configManager;
    private constructor();
    static getInstance(): ProjectIndexer;
    indexProject(rootPath: string): Promise<ProjectContext>;
    private detectProjectType;
    indexFiles(rootPath: string): Promise<FileInfo[]>;
    private isTextFile;
    private getGitInfo;
    private getPackageInfo;
    private parsePackageJson;
    private parseRequirementsTxt;
    private parseCargoToml;
    private parseGoMod;
    private parsePomXml;
    private extractDependencies;
    updateFileIndex(rootPath: string, relativePath: string): Promise<FileInfo | null>;
}
//# sourceMappingURL=ProjectIndexer.d.ts.map