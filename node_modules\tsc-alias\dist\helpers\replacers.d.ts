import { IConfig, ReplacerOptions } from '../interfaces';
export declare function importReplacers(config: IConfig, replacers: ReplacerOptions, cmdReplacers?: string[]): Promise<void>;
export declare function replaceAlias(config: IConfig, file: string, resolveFullPath?: boolean, resolveFullExtension?: string): Promise<boolean>;
export declare function replaceAliasString(config: IConfig, file: string, code: string, resolveFullPath?: boolean, resolveFullExtension?: string): string;
